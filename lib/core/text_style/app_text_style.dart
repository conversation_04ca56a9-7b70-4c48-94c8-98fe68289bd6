import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

class AppTextStyles {
  AppTextStyles._();

  static const TextStyle textStyle10 = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle11 = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle12 = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle13 = TextStyle(
    fontSize: 13,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle14 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle15 = TextStyle(
    fontSize: 15,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle16 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle17 = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle18 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle19 = TextStyle(
    fontSize: 19,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle20 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle21 = TextStyle(
    fontSize: 21,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle22 = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle23 = TextStyle(
    fontSize: 23,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle24 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle25 = TextStyle(
    fontSize: 25,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle26 = TextStyle(
    fontSize: 26,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle27 = TextStyle(
    fontSize: 27,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle28 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle29 = TextStyle(
    fontSize: 29,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle30 = TextStyle(
    fontSize: 30,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle31 = TextStyle(
    fontSize: 31,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyle32 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.normal,
  );

  static const TextStyle textStyleBold10 = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold11 = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold12 = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold13 = TextStyle(
    fontSize: 13,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold14 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold15 = TextStyle(
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold16 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold17 = TextStyle(
    fontSize: 17,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold18 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold19 = TextStyle(
    fontSize: 19,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold20 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold21 = TextStyle(
    fontSize: 21,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold22 = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold23 = TextStyle(
    fontSize: 23,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold24 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold25 = TextStyle(
    fontSize: 25,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold26 = TextStyle(
    fontSize: 26,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold27 = TextStyle(
    fontSize: 27,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold28 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold29 = TextStyle(
    fontSize: 29,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold30 = TextStyle(
    fontSize: 30,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold31 = TextStyle(
    fontSize: 31,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle textStyleBold32 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w600,
  );

  // Text Style Appbar

  static const TextStyle textStyleAppBar = TextStyle(
    fontSize: 16,
    color: AppColor.primary,
    fontWeight: FontWeight.w600,
  );
}

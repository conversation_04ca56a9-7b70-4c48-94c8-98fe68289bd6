// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'date_range_query.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RelativeDateRangeQueryAdapter
    extends TypeAdapter<RelativeDateRangeQuery> {
  @override
  final int typeId = 111;

  @override
  RelativeDateRangeQuery read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RelativeDateRangeQuery(
      fields[0] as int,
      fields[1] as DateRangeUnit,
    );
  }

  @override
  void write(BinaryWriter writer, RelativeDateRangeQuery obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.offset)
      ..writeByte(1)
      ..write(obj.unit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RelativeDateRangeQueryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AbsoluteDateRangeQueryAdapter
    extends TypeAdapter<AbsoluteDateRangeQuery> {
  @override
  final int typeId = 110;

  @override
  AbsoluteDateRangeQuery read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AbsoluteDateRangeQuery(
      after: fields[0] as DateTime?,
      before: fields[1] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, AbsoluteDateRangeQuery obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.after)
      ..writeByte(1)
      ..write(obj.before);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AbsoluteDateRangeQueryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AbsoluteDateRangeQuery _$AbsoluteDateRangeQueryFromJson(
        Map<String, dynamic> json) =>
    AbsoluteDateRangeQuery(
      after: _$JsonConverterFromJson<String, DateTime>(
          json['after'], const LocalDateTimeJsonConverter().fromJson),
      before: _$JsonConverterFromJson<String, DateTime>(
          json['before'], const LocalDateTimeJsonConverter().fromJson),
    );

Map<String, dynamic> _$AbsoluteDateRangeQueryToJson(
        AbsoluteDateRangeQuery instance) =>
    <String, dynamic>{
      'after': _$JsonConverterToJson<String, DateTime>(
          instance.after, const LocalDateTimeJsonConverter().toJson),
      'before': _$JsonConverterToJson<String, DateTime>(
          instance.before, const LocalDateTimeJsonConverter().toJson),
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) =>
    json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) =>
    value == null ? null : toJson(value);

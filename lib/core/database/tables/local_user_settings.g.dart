// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_user_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalUserSettingsAdapter extends TypeAdapter<LocalUserSettings> {
  @override
  final int typeId = 1;

  @override
  LocalUserSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalUserSettings(
      isBiometricAuthenticationEnabled: fields[0] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, LocalUserSettings obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.isBiometricAuthenticationEnabled);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalUserSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

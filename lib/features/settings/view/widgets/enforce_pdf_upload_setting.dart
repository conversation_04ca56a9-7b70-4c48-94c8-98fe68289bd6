import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/features/settings/view/widgets/global_settings_builder.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class EnforcePdfUploadSetting extends StatelessWidget {
  const EnforcePdfUploadSetting({super.key});

  @override
  Widget build(BuildContext context) {
    return GlobalSettingsBuilder(builder: (context, settings) {
      return ListTile(
        title: Text(S.of(context)!.uploadScansAsPdf),
        subtitle: Text(S.of(context)!.convertSinglePageScanToPdf),
        trailing: CupertinoSwitch(
          value: settings.enforceSinglePagePdfUpload,
          activeColor: AppColor.primary, // <PERSON>àu của công tắc khi bật
          // trackColor: AppColor.white, // <PERSON><PERSON><PERSON> nề<PERSON> thanh trượt
          onChanged: (value) {
            settings.enforceSinglePagePdfUpload = value;
            settings.save();
          },
        ),
        onTap: () {
          final value = !settings.enforceSinglePagePdfUpload;
          settings.enforceSinglePagePdfUpload = value;
          settings.save();
        },
      );
    });
  }
}

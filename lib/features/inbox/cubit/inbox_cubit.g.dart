// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inbox_cubit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InboxState _$InboxStateFromJson(Map<String, dynamic> json) => InboxState(
      isHintAcknowledged: json['isHintAcknowledged'] as bool? ?? false,
    );

Map<String, dynamic> _$InboxStateToJson(InboxState instance) =>
    <String, dynamic>{
      'isHintAcknowledged': instance.isHintAcknowledged,
    };

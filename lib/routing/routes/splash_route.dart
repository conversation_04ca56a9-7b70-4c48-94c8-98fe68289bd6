import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:paperless_mobile/features/splash/splash_screen.dart';
import 'package:paperless_mobile/routing/navigation_keys.dart';

part 'splash_route.g.dart';

@TypedGoRoute<SplashRoute>(
  path: '/',
  name: 'splash',
)
class SplashRoute extends GoRouteData  {
  static final $parentNavigatorKey = rootNavigatorKey;

  const SplashRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SplashScreen();
  }
}
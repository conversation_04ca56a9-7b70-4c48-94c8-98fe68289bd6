// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_permission.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserPermission _$UserPermissionFromJson(Map<String, dynamic> json) =>
    UserPermission(
      id: (json['id'] as num?)?.toInt(),
      username: json['username'] as String?,
      email: json['email'] as String?,
      password: json['password'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      dateJoined: json['dateJoined'] == null
          ? null
          : DateTime.parse(json['dateJoined'] as String),
      isStaff: json['is_staff'] as bool?,
      isActive: json['is_active'] as bool?,
      isSuperuser: json['isSuperuser'] as bool?,
      groups: json['groups'] as List<dynamic>?,
      userPermissions: json['user_permissions'] as List<dynamic>?,
      inheritedPermissions: (json['inherited_permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isMfaEnabled: json['isMfaEnabled'] as bool?,
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$UserPermissionToJson(UserPermission instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'password': instance.password,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'dateJoined': instance.dateJoined?.toIso8601String(),
      'is_staff': instance.isStaff,
      'is_active': instance.isActive,
      'isSuperuser': instance.isSuperuser,
      'groups': instance.groups,
      'user_permissions': instance.userPermissions,
      'inherited_permissions': instance.inheritedPermissions,
      'isMfaEnabled': instance.isMfaEnabled,
      'isSelected': instance.isSelected,
    };

{"count": 4, "next": null, "previous": null, "results": [{"id": 1, "username": "admin", "password": "**********", "email": "<EMAIL>", "first_name": "", "last_name": "", "date_joined": "2022-02-14T23:11:09.103293Z", "is_staff": true, "is_active": true, "is_superuser": true, "groups": [], "user_permissions": [], "inherited_permissions": ["auth.delete_permission", "paperless_mail.change_mailrule", "django_celery_results.add_taskresult", "documents.view_taskattributes", "documents.view_paperlesstask", "django_q.add_success", "documents.view_uisettings", "auth.change_user", "admin.delete_logentry", "django_celery_results.change_taskresult", "django_q.change_schedule", "django_celery_results.delete_taskresult", "paperless_mail.add_mailaccount", "auth.change_group", "documents.add_note", "paperless_mail.delete_mailaccount", "authtoken.delete_tokenproxy", "guardian.delete_groupobjectpermission", "contenttypes.delete_contenttype", "documents.change_correspondent", "authtoken.delete_token", "documents.delete_documenttype", "django_q.change_ormq", "documents.change_savedviewfilterrule", "auth.delete_group", "documents.add_documenttype", "django_q.change_success", "documents.delete_tag", "documents.change_note", "django_q.delete_task", "documents.add_savedviewfilterrule", "django_q.view_task", "paperless_mail.add_mailrule", "paperless_mail.view_mailaccount", "documents.add_frontendsettings", "sessions.change_session", "documents.view_savedview", "authtoken.add_tokenproxy", "documents.change_tag", "documents.view_document", "documents.add_savedview", "auth.delete_user", "documents.view_log", "documents.view_note", "guardian.change_groupobjectpermission", "sessions.delete_session", "django_q.change_failure", "guardian.change_userobjectpermission", "documents.change_storagepath", "documents.delete_document", "documents.delete_taskattributes", "django_celery_results.change_groupresult", "django_q.add_ormq", "guardian.view_groupobjectpermission", "admin.change_logentry", "django_q.delete_schedule", "documents.delete_paperlesstask", "django_q.view_ormq", "documents.change_paperlesstask", "guardian.delete_userobjectpermission", "auth.view_permission", "auth.view_user", "django_q.add_schedule", "authtoken.change_token", "guardian.add_groupobjectpermission", "documents.view_documenttype", "documents.change_log", "paperless_mail.delete_mailrule", "auth.view_group", "authtoken.view_token", "admin.view_logentry", "django_celery_results.view_chordcounter", "django_celery_results.view_groupresult", "documents.view_storagepath", "documents.add_storagepath", "django_celery_results.add_groupresult", "documents.view_tag", "guardian.view_userobjectpermission", "documents.delete_correspondent", "documents.add_tag", "documents.delete_savedviewfilterrule", "documents.add_correspondent", "authtoken.view_tokenproxy", "documents.delete_frontendsettings", "django_celery_results.delete_chordcounter", "django_q.change_task", "documents.add_taskattributes", "documents.delete_storagepath", "sessions.add_session", "documents.add_uisettings", "documents.change_taskattributes", "documents.delete_uisettings", "django_q.delete_ormq", "auth.change_permission", "documents.view_savedviewfilterrule", "documents.change_frontendsettings", "documents.change_documenttype", "documents.view_correspondent", "auth.add_user", "paperless_mail.change_mailaccount", "documents.add_paperlesstask", "django_q.view_success", "django_celery_results.delete_groupresult", "documents.delete_savedview", "authtoken.change_tokenproxy", "documents.view_frontendsettings", "authtoken.add_token", "django_celery_results.add_chordcounter", "contenttypes.change_contenttype", "admin.add_logentry", "django_q.delete_failure", "documents.change_uisettings", "django_q.view_failure", "documents.add_log", "documents.change_savedview", "paperless_mail.view_mailrule", "django_q.view_schedule", "documents.change_document", "django_celery_results.change_chordcounter", "documents.add_document", "django_celery_results.view_taskresult", "contenttypes.add_contenttype", "django_q.delete_success", "documents.delete_note", "django_q.add_failure", "guardian.add_userobjectpermission", "sessions.view_session", "contenttypes.view_contenttype", "auth.add_permission", "documents.delete_log", "django_q.add_task", "auth.add_group"]}, {"id": 15, "username": "test", "password": "**********", "first_name": "", "last_name": "", "date_joined": "2022-11-23T08:30:54Z", "is_staff": true, "is_active": true, "is_superuser": false, "groups": [1], "user_permissions": ["add_group", "change_group", "delete_group", "view_group", "add_permission", "change_permission", "delete_permission", "view_permission", "add_token", "change_token", "delete_token", "view_token", "add_tokenproxy", "change_tokenproxy", "delete_tokenproxy", "view_tokenproxy", "add_contenttype", "change_contenttype", "delete_contenttype", "view_contenttype", "add_chordcounter", "change_chordcounter", "delete_chordcounter", "view_chordcounter", "add_groupresult", "change_groupresult", "delete_groupresult", "view_groupresult", "add_taskresult", "change_taskresult", "delete_taskresult", "view_taskresult", "add_failure", "change_failure", "delete_failure", "view_failure", "add_ormq", "change_ormq", "delete_ormq", "view_ormq", "add_schedule", "change_schedule", "delete_schedule", "view_schedule", "add_success", "change_success", "delete_success", "view_success", "add_task", "change_task", "delete_task", "view_task", "add_note", "change_note", "delete_note", "view_note", "add_frontendsettings", "change_frontendsettings", "delete_frontendsettings", "view_frontendsettings", "add_log", "change_log", "delete_log", "view_log", "add_savedviewfilterrule", "change_savedviewfilterrule", "delete_savedviewfilterrule", "view_savedviewfilterrule", "add_taskattributes", "change_taskattributes", "delete_taskattributes", "view_taskattributes", "add_session", "change_session", "delete_session", "view_session"], "inherited_permissions": ["auth.delete_permission", "django_celery_results.add_taskresult", "documents.view_taskattributes", "django_q.add_ormq", "django_q.add_success", "django_q.delete_schedule", "django_q.view_ormq", "auth.view_permission", "django_q.add_schedule", "django_celery_results.change_taskresult", "django_q.change_schedule", "django_celery_results.delete_taskresult", "authtoken.change_token", "auth.change_group", "documents.add_note", "authtoken.delete_tokenproxy", "documents.view_documenttype", "contenttypes.delete_contenttype", "documents.change_correspondent", "authtoken.delete_token", "documents.change_log", "auth.view_group", "authtoken.view_token", "django_celery_results.view_chordcounter", "django_celery_results.view_groupresult", "documents.delete_documenttype", "django_q.change_ormq", "documents.change_savedviewfilterrule", "django_celery_results.add_groupresult", "auth.delete_group", "documents.add_documenttype", "django_q.change_success", "auth.add_permission", "documents.delete_correspondent", "documents.delete_savedviewfilterrule", "documents.add_correspondent", "authtoken.view_tokenproxy", "documents.delete_frontendsettings", "django_celery_results.delete_chordcounter", "documents.add_taskattributes", "django_q.change_task", "sessions.add_session", "documents.change_taskattributes", "documents.change_note", "django_q.delete_task", "django_q.delete_ormq", "auth.change_permission", "documents.add_savedviewfilterrule", "django_q.view_task", "documents.view_savedviewfilterrule", "documents.change_frontendsettings", "documents.change_documenttype", "documents.view_correspondent", "django_q.view_success", "documents.add_frontendsettings", "django_celery_results.delete_groupresult", "documents.delete_savedview", "authtoken.change_tokenproxy", "documents.view_frontendsettings", "authtoken.add_token", "sessions.change_session", "django_celery_results.add_chordcounter", "documents.view_savedview", "contenttypes.change_contenttype", "django_q.delete_failure", "authtoken.add_tokenproxy", "documents.view_document", "documents.add_savedview", "django_q.view_failure", "documents.view_note", "documents.view_log", "documents.add_log", "documents.change_savedview", "django_q.view_schedule", "documents.change_document", "django_celery_results.change_chordcounter", "documents.add_document", "sessions.delete_session", "django_q.change_failure", "django_celery_results.view_taskresult", "contenttypes.add_contenttype", "django_q.delete_success", "documents.delete_note", "django_q.add_failure", "sessions.view_session", "contenttypes.view_contenttype", "documents.delete_taskattributes", "documents.delete_document", "documents.delete_log", "django_q.add_task", "django_celery_results.change_groupresult", "auth.add_group"]}, {"id": 6, "username": "testuser", "password": "**********", "email": "<EMAIL>", "first_name": "", "last_name": "", "date_joined": "2022-11-16T04:14:20.484914Z", "is_staff": false, "is_active": true, "is_superuser": false, "groups": [1, 6], "user_permissions": ["add_logentry", "change_logentry", "delete_logentry", "view_logentry"], "inherited_permissions": ["auth.delete_permission", "django_celery_results.add_taskresult", "documents.view_taskattributes", "django_q.add_ormq", "django_q.add_success", "django_q.delete_schedule", "django_q.view_ormq", "auth.change_user", "auth.view_permission", "auth.view_user", "django_q.add_schedule", "django_celery_results.change_taskresult", "django_q.change_schedule", "django_celery_results.delete_taskresult", "authtoken.change_token", "auth.change_group", "documents.add_note", "authtoken.delete_tokenproxy", "documents.view_documenttype", "contenttypes.delete_contenttype", "documents.change_correspondent", "authtoken.delete_token", "documents.change_log", "auth.view_group", "authtoken.view_token", "django_celery_results.view_chordcounter", "django_celery_results.view_groupresult", "documents.delete_documenttype", "django_q.change_ormq", "documents.change_savedviewfilterrule", "django_celery_results.add_groupresult", "auth.delete_group", "documents.add_documenttype", "django_q.change_success", "auth.add_permission", "documents.delete_correspondent", "documents.delete_savedviewfilterrule", "documents.add_correspondent", "authtoken.view_tokenproxy", "documents.delete_frontendsettings", "django_celery_results.delete_chordcounter", "documents.add_taskattributes", "django_q.change_task", "sessions.add_session", "documents.change_taskattributes", "documents.change_note", "django_q.delete_task", "django_q.delete_ormq", "auth.change_permission", "documents.add_savedviewfilterrule", "django_q.view_task", "documents.view_savedviewfilterrule", "documents.change_frontendsettings", "documents.change_documenttype", "documents.view_correspondent", "auth.add_user", "django_q.view_success", "documents.add_frontendsettings", "django_celery_results.delete_groupresult", "documents.delete_savedview", "authtoken.change_tokenproxy", "documents.view_frontendsettings", "authtoken.add_token", "sessions.change_session", "django_celery_results.add_chordcounter", "documents.view_savedview", "contenttypes.change_contenttype", "django_q.delete_failure", "authtoken.add_tokenproxy", "documents.view_document", "documents.add_savedview", "django_q.view_failure", "documents.view_note", "documents.view_log", "auth.delete_user", "documents.add_log", "documents.change_savedview", "django_q.view_schedule", "documents.change_document", "django_celery_results.change_chordcounter", "documents.add_document", "sessions.delete_session", "django_q.change_failure", "django_celery_results.view_taskresult", "contenttypes.add_contenttype", "django_q.delete_success", "documents.delete_note", "django_q.add_failure", "sessions.view_session", "contenttypes.view_contenttype", "documents.delete_taskattributes", "documents.delete_document", "documents.delete_log", "django_q.add_task", "django_celery_results.change_groupresult", "auth.add_group"]}]}
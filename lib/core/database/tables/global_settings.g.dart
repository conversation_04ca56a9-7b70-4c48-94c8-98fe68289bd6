// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'global_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GlobalSettingsAdapter extends TypeAdapter<GlobalSettings> {
  @override
  final int typeId = 0;

  @override
  GlobalSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GlobalSettings(
      preferredLocaleSubtag: fields[0] as String,
      preferredThemeMode: fields[1] as ThemeMode,
      preferredColorSchemeOption: fields[2] as ColorSchemeOption,
      showOnboarding: fields[3] as bool,
      loggedInUserId: fields[4] as String?,
      defaultDownloadType: fields[5] as FileDownloadType,
      defaultShareType: fields[6] as FileDownloadType,
      enforceSinglePagePdfUpload: fields[7] == null ? false : fields[7] as bool,
      skipDocumentPreprarationOnUpload:
          fields[8] == null ? false : fields[8] as bool,
      disableAnimations: fields[9] == null ? false : fields[9] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, GlobalSettings obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.preferredLocaleSubtag)
      ..writeByte(1)
      ..write(obj.preferredThemeMode)
      ..writeByte(2)
      ..write(obj.preferredColorSchemeOption)
      ..writeByte(3)
      ..write(obj.showOnboarding)
      ..writeByte(4)
      ..write(obj.loggedInUserId)
      ..writeByte(5)
      ..write(obj.defaultDownloadType)
      ..writeByte(6)
      ..write(obj.defaultShareType)
      ..writeByte(7)
      ..write(obj.enforceSinglePagePdfUpload)
      ..writeByte(8)
      ..write(obj.skipDocumentPreprarationOnUpload)
      ..writeByte(9)
      ..write(obj.disableAnimations);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GlobalSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';

class MimeTypesPie<PERSON>hart extends StatefulWidget {
  final PaperlessServerStatisticsModel statistics;

  const MimeTypesPieChart({
    super.key,
    required this.statistics,
  });

  @override
  State<MimeTypesPieChart> createState() => _MimeTypesPieChartState();
}

class _MimeTypesPieChartState extends State<MimeTypesPieChart> {
  static final _mimeTypeNames = {
    "application/pdf": "PDF",
    "image/png": "PNG",
    "image/jpeg": "JPEG",
    "image/tiff": "TIFF",
    "image/gif": "GIF",
    "image/webp": "WebP",
    "text/plain": "TXT",
    "application/msword": "DOC",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        "DOCX",
    "application/vnd.ms-powerpoint": "PPT",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        "PPTX",
    "application/vnd.ms-excel": "XLS",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "XLSX",
    "application/vnd.oasis.opendocument.text": "ODT",
    "application/vnd.oasis.opendocument.presentation": "ODP",
    "application/vnd.oasis.opendocument.spreadsheet": "ODS",
  };

  int? _touchedIndex = -1;

  List<double> percentChart = [];
  List<Color> colorChart = const [
    Color(0xFF11407B), // Xanh đậm
    Color(0xFF2A9D8F), // Xanh ngọc
    Color(0xFFE76F51), // Cam đất
    Color(0xFFF4A261), // Cam nhạt
    Color(0xFFE9C46A), // Vàng đất
    Color(0xFF264653), // Xanh đen
    Color(0xFF6D597A), // Tím xám
    Color(0xFFB5838D), // Hồng đất
    Color(0xFFA8DADC), // Xanh pastel
    Color(0xFF457B9D), // Xanh dương sáng
    Color(0xFFF28482), // Cam hồng
    Color(0xFF8D99AE), // Xám xanh
    Color(0xFFBC6C25), // Nâu đất
    Color(0xFF7FB069), // Xanh lá
    Color(0xFFD4A373), // Nâu nhạt
    Color(0xFF6C584C), // Nâu rêu
  ];
  // final colorShades = Colors.blue.values;

  @override
  void initState() {
    super.initState();
    initData();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final double totalWidth = size.width - 94;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Gap(16),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            width: totalWidth,
            height: 10,
            child: Row(
              children: percentChart.asMap().entries.map((entry) {
                final index = entry.key;
                final value = entry.value;
                final isLast = index == percentChart.length - 1;

                final double totalMargin = (percentChart.length - 1) * 2;
                final double adjustedWidth = totalWidth - totalMargin;
                final double totalPercent =
                    percentChart.reduce((a, b) => a + b);
                final width = adjustedWidth * (value / totalPercent);

                return Container(
                  width: width,
                  color: colorChart[index],
                  margin: EdgeInsets.only(
                    right: isLast ? 0 : 2,
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        const Gap(16),
        Align(
          alignment: Alignment.centerLeft,
          child: Wrap(
            alignment: WrapAlignment.start,
            runAlignment: WrapAlignment.start,
            crossAxisAlignment: WrapCrossAlignment.start,
            spacing: 12,
            runSpacing: 8,
            children: [
              for (int i = 0; i < widget.statistics.fileTypeCounts.length; i++)
                GestureDetector(
                  onTapDown: (_) {
                    setState(() {
                      _touchedIndex = i;
                    });
                  },
                  onTapUp: (details) {
                    setState(() {
                      _touchedIndex = -1;
                    });
                  },
                  onTapCancel: () {
                    setState(() {
                      _touchedIndex = -1;
                    });
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: colorChart[i],
                        ),
                        margin: const EdgeInsets.only(right: 4),
                        width: 10,
                        height: 10,
                      ),
                      Text(
                        _mimeTypeNames[
                            widget.statistics.fileTypeCounts[i].mimeType]!,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyles.textStyleBold16,
                      ),
                      Text('(${percentChart[i].toStringAsFixed(1)}%)')
                    ],
                  ),
                ),
            ],
          ),
        ),
        const Gap(18),
      ],
    );
  }

  void initData() {
    percentChart.clear();
    for (int i = 0; i < widget.statistics.fileTypeCounts.length; i++) {
      final type = widget.statistics.fileTypeCounts[i];
      final percentage = type.count / widget.statistics.documentsTotal * 100;
      percentChart.add(percentage);
      // colorChart.add(colorShades[i % colorShades.length]);
    }
  }
}

extension AllShades on MaterialColor {
  List<Color> get values => [
        shade200,
        shade600,
        shade300,
        shade100,
        shade800,
        shade400,
        shade900,
        shade500,
        shade700,
      ];
}

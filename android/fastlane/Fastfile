# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do |options|
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit build to Google Play"
  lane :publish_in_play_store do |options|
    release_status = (options[:is_draft] ? "draft" : "completed")
    track = (options[:track])
    sh "$FLUTTER_ROOT/flutter build appbundle -v"
    upload_to_play_store(
      track: track,
      release_status: release_status,
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      json_key_data: ENV['PLAY_STORE_CREDENTIALS'],
    )
  end
  
  desc "Build the app"
  lane :build do |options|
    sh "$FLUTTER_ROOT/flutter build appbundle -v"
  end

  desc "Promote track"
  lane :play_store_promote do |options|
    track = options[:track]
    promote_to = options[:promote_to_track]
    upload_to_play_store(
      track: track,
      track_promote_to: promote_to,
      json_key_data: ENV['PLAY_STORE_CREDENTIALS'],
    )
  end

  desc "Builds apks and creates a new release on GitHub"
  lane :github do |options|
    sh "$FLUTTER_ROOT/flutter build apk --split-per-abi --release"
    sh "$FLUTTER_ROOT/flutter build apk --release"
    set_github_release(
      repository_name: "astubenbord/paperless-mobile",
      api_token: ENV["GH_ACCESS_TOKEN"],
      name: "v" + flutter_version()["version_name"],
      tag_name: "v" + flutter_version()["version_name"],
      is_generate_release_notes: true,
      is_draft: options[:is_draft],
      commitish: options[:branch],
      upload_assets: [
        "../build/app/outputs/flutter-apk/app-x86_64-release.apk",
        "../build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk", 
        "../build/app/outputs/flutter-apk/app-arm64-v8a-release.apk", 
        "../build/app/outputs/flutter-apk/app-release.apk"
      ],
    )
  end
end
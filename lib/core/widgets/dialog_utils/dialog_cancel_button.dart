import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class DialogCancelButton extends StatelessWidget {
  final void Function()? onTap;
  const DialogCancelButton({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onTap ?? () => Navigator.pop(context),
      child: Text(
        S.of(context)!.cancel,
        style: const TextStyle(
            color: AppColor.primary, fontWeight: FontWeight.w600),
      ),
    );
  }
}

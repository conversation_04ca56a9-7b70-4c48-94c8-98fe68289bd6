import 'package:paperless_api/src/models/task/task.dart';

abstract class PaperlessTasksApi {
  Future<Task?> find({int? id, String? taskId});
  Future<Iterable<Task>> findAll([Iterable<int>? ids]);
  Stream<Task> listenForTaskChanges(String taskId,
      {Map<String, dynamic>? param, List<int> projects });
  Future<Task> acknowledgeTask(Task task);
  Future<Iterable<Task>> acknowledgeTasks(Iterable<Task> tasks);
  Future<void> addPermission(Map<String,dynamic> body);

  Future<void> addProject(List<int> projects, String id);

}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'logging_out_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $loggingOutRoute,
    ];

RouteBase get $loggingOutRoute => GoRouteData.$route(
      path: '/logging-out',
      name: 'loggingOut',
      parentNavigatorKey: LoggingOutRoute.$parentNavigatorKey,
      factory: $LoggingOutRouteExtension._fromState,
    );

extension $LoggingOutRouteExtension on LoggingOutRoute {
  static LoggingOutRoute _fromState(GoRouterState state) =>
      const LoggingOutRoute();

  String get location => GoRouteData.$location(
        '/logging-out',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

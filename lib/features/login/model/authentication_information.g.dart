// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authentication_information.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AuthenticationInformationAdapter
    extends TypeAdapter<AuthenticationInformation> {
  @override
  final int typeId = 4;

  @override
  AuthenticationInformation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AuthenticationInformation(
      username: fields[3] as String,
      serverUrl: fields[1] as String,
      token: fields[0] as String?,
      clientCertificate: fields[2] as ClientCertificate?,
    );
  }

  @override
  void write(BinaryWriter writer, AuthenticationInformation obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.token)
      ..writeByte(1)
      ..write(obj.serverUrl)
      ..writeByte(2)
      ..write(obj.clientCertificate)
      ..writeByte(3)
      ..write(obj.username);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AuthenticationInformationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

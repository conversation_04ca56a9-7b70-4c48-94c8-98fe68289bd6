{"developedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {name}.", "@developedBy": {"placeholders": {"name": {}}}, "addAnotherAccount": "Přidat <PERSON>", "@addAnotherAccount": {}, "account": "Účet", "@account": {}, "addCorrespondent": "Nový korespondent", "@addCorrespondent": {"description": "Title when adding a new correspondent"}, "addDocumentType": "Nový typ dokumentu", "@addDocumentType": {"description": "Title when adding a new document type"}, "addStoragePath": "Nová cesta úložiště", "@addStoragePath": {"description": "Title when adding a new storage path"}, "addTag": "<PERSON><PERSON> tag", "@addTag": {"description": "Title when adding a new tag"}, "aboutThisApp": "O aplikaci", "@aboutThisApp": {"description": "Label for about this app tile displayed in the drawer"}, "loggedInAs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jako {name}", "@loggedInAs": {"placeholders": {"name": {}}}, "disconnect": "<PERSON><PERSON><PERSON><PERSON>", "@disconnect": {"description": "Logout button label"}, "reportABug": "Nahlásit chybu", "@reportABug": {}, "settings": "Nastavení", "@settings": {}, "authenticateOnAppStart": "Přihlášení při spuštění aplikace", "@authenticateOnAppStart": {"description": "Description of the biometric authentication settings tile"}, "biometricAuthentication": "Zapnout biometrické ověření", "@biometricAuthentication": {}, "authenticateToToggleBiometricAuthentication": "{mode, select, enable{Pro zapnutí biometrického ověřování je třeba se přihlásit} disable{Pro vypnutí biometrického ověřování je třeba se přihlásit} other{}}", "@authenticateToToggleBiometricAuthentication": {"placeholders": {"mode": {}}}, "documents": "Dokumenty", "@documents": {}, "inbox": "Inbox", "@inbox": {}, "labels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@labels": {}, "scanner": "<PERSON><PERSON><PERSON>", "@scanner": {}, "startTyping": "Začni psát...", "@startTyping": {}, "doYouReallyWantToDeleteThisView": "Opravdu chceš tento náhled smazat?", "@doYouReallyWantToDeleteThisView": {}, "deleteView": "<PERSON><PERSON><PERSON><PERSON> {name}?", "@deleteView": {}, "addedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@addedAt": {}, "archiveSerialNumber": "Archivní s<PERSON><PERSON><PERSON><PERSON>", "@archiveSerialNumber": {}, "asn": "ASČ", "@asn": {}, "correspondent": "Korespondent", "@correspondent": {}, "createdAt": "Vytvořeno", "@createdAt": {}, "documentSuccessfullyDeleted": "Dokument byl úsp<PERSON>š<PERSON> s<PERSON>.", "@documentSuccessfullyDeleted": {}, "assignAsn": "Přiřadit ASČ", "@assignAsn": {}, "deleteDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@deleteDocumentTooltip": {"description": "Tooltip shown for the delete button on details page"}, "downloadDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@downloadDocumentTooltip": {"description": "Tooltip shown for the download button on details page"}, "editDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@editDocumentTooltip": {"description": "Tooltip shown for the edit button on details page"}, "loadFullContent": "Načíst celý obsah", "@loadFullContent": {}, "noAppToDisplayPDFFilesFound": "Aplikace pro otevírání PDF souborů nenalezena.", "@noAppToDisplayPDFFilesFound": {}, "openInSystemViewer": "Otevřít v systémovém prohlížeči", "@openInSystemViewer": {}, "couldNotOpenFilePermissionDenied": "Soubor nelze otevřít: přístup zamítnut.", "@couldNotOpenFilePermissionDenied": {}, "previewTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@previewTooltip": {"description": "Tooltip shown for the preview button on details page"}, "shareTooltip": "Sdílet", "@shareTooltip": {"description": "Tooltip shown for the share button on details page"}, "similarDocuments": "Podobné do<PERSON>", "@similarDocuments": {"description": "Label shown in the tabbar on details page"}, "content": "<PERSON><PERSON><PERSON>", "@content": {"description": "Label shown in the tabbar on details page"}, "metaData": "<PERSON><PERSON><PERSON>", "@metaData": {"description": "Label shown in the tabbar on details page"}, "overview": "<PERSON><PERSON><PERSON><PERSON>", "@overview": {"description": "Label shown in the tabbar on details page"}, "documentType": "Typ dokumentu", "@documentType": {}, "archivedPdf": "Archivováno (pdf)", "@archivedPdf": {"description": "Option to chose when downloading a document"}, "chooseFiletype": "Choose filetype", "@chooseFiletype": {}, "original": "Originál", "@original": {"description": "Option to chose when downloading a document"}, "documentSuccessfullyDownloaded": "Dokument úspěšně stažen.", "@documentSuccessfullyDownloaded": {}, "suggestions": "Návrhy:", "@suggestions": {}, "editDocument": "Upravit dokument", "@editDocument": {}, "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@advanced": {}, "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@apply": {}, "extended": "Prodloužené", "@extended": {}, "titleAndContent": "Název & Obsah", "@titleAndContent": {}, "title": "<PERSON><PERSON><PERSON><PERSON>", "@title": {}, "reset": "Zrušit", "@reset": {}, "filterDocuments": "Filtrovat dokumenty", "@filterDocuments": {"description": "Title of the document filter"}, "originalMD5Checksum": "MD5 součet originálu", "@originalMD5Checksum": {}, "mediaFilename": "<PERSON><PERSON><PERSON> so<PERSON>u média", "@mediaFilename": {}, "originalFileSize": "Velikost originálu", "@originalFileSize": {}, "originalMIMEType": "MIME-Typ originálu", "@originalMIMEType": {}, "modifiedAt": "Pozměněno", "@modifiedAt": {}, "preview": "<PERSON><PERSON><PERSON><PERSON>", "@preview": {"description": "Title of the document preview page"}, "scanADocument": "Oskenovat dokument", "@scanADocument": {}, "noDocumentsScannedYet": "Zatím nebyly naskenovány žádné dokumenty.", "@noDocumentsScannedYet": {}, "or": "nebo", "@or": {"description": "Used on the scanner page between both main actions when no scans have been captured."}, "deleteAllScans": "Smazat všechny skeny", "@deleteAllScans": {}, "uploadADocumentFromThisDevice": "Nahrát jeden dokument z tohoto zařízení", "@uploadADocumentFromThisDevice": {"description": "Button label on scanner page"}, "noMatchesFound": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>.", "@noMatchesFound": {"description": "Displayed when no documents were found in the document search."}, "removeFromSearchHistory": "Odstranit z historie vyhledávání?", "@removeFromSearchHistory": {}, "results": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@results": {"description": "Label displayed above search results in document search."}, "searchDocuments": "Prohledat dokumenty", "@searchDocuments": {}, "resetFilter": "Zrušit", "@resetFilter": {}, "lastMonth": "<PERSON><PERSON><PERSON>", "@lastMonth": {}, "last7Days": "Posledních 7 dní", "@last7Days": {}, "last3Months": "Poslední 3 měsíce", "@last3Months": {}, "lastYear": "Minulý rok", "@lastYear": {}, "search": "Hledat", "@search": {}, "documentsSuccessfullyDeleted": "Dokument<PERSON> byly <PERSON><PERSON>.", "@documentsSuccessfullyDeleted": {}, "thereSeemsToBeNothingHere": "Zde je ještě prázdno...", "@thereSeemsToBeNothingHere": {}, "oops": "<PERSON><PERSON><PERSON>.", "@oops": {}, "newDocumentAvailable": "Dostupný nový dokument!", "@newDocumentAvailable": {}, "orderBy": "Řadit dle", "@orderBy": {}, "thisActionIsIrreversibleDoYouWishToProceedAnyway": "<PERSON>to a<PERSON>ci nelze vrátit zpět. Opravdu chcete pokračovat?", "@thisActionIsIrreversibleDoYouWishToProceedAnyway": {}, "confirmDeletion": "Potvrdit smazání", "@confirmDeletion": {}, "areYouSureYouWantToDeleteTheFollowingDocuments": "{count, plural, one{Jste si jisti že chcete smazat následující dokument?} other{Jste si jisti že chcete smazat následující dokumenty?}}", "@areYouSureYouWantToDeleteTheFollowingDocuments": {"placeholders": {"count": {}}}, "countSelected": "{count} v<PERSON>b<PERSON><PERSON><PERSON>", "@countSelected": {"description": "Displayed in the appbar when at least one document is selected.", "placeholders": {"count": {}}}, "storagePath": "Cesta k úložišti", "@storagePath": {}, "prepareDocument": "Připravit dokument", "@prepareDocument": {}, "tags": "Tagy", "@tags": {}, "documentSuccessfullyUpdated": "Dokument úspěšně aktualizován.", "@documentSuccessfullyUpdated": {}, "fileName": "<PERSON><PERSON><PERSON>", "@fileName": {}, "synchronizeTitleAndFilename": "Synchronizovat název a jméno souboru", "@synchronizeTitleAndFilename": {}, "reload": "Znovu načíst", "@reload": {}, "documentSuccessfullyUploadedProcessing": "Dokument úspěšně <PERSON>, zpracovávám...", "@documentSuccessfullyUploadedProcessing": {}, "deleteLabelWarningText": "Dokumenty mají přiřazen tento štítek. Odstraněním štítku bude označení odstraněno. Pokračovat?", "@deleteLabelWarningText": {}, "couldNotAcknowledgeTasks": "Nepodařilo se potvrdit úkoly.", "@couldNotAcknowledgeTasks": {}, "authenticationFailedPleaseTryAgain": "Přihláš<PERSON>í se<PERSON>, zkuste to znovu.", "@authenticationFailedPleaseTryAgain": {}, "anErrorOccurredWhileTryingToAutocompleteYourQuery": "Při automatickém doplnění požadavku došlo k chybě.", "@anErrorOccurredWhileTryingToAutocompleteYourQuery": {}, "biometricAuthenticationFailed": "Biometrické ověř<PERSON>í se<PERSON>.", "@biometricAuthenticationFailed": {}, "biometricAuthenticationNotSupported": "Biometrické ověření není na tomto přístroji podporováno.", "@biometricAuthenticationNotSupported": {}, "couldNotBulkEditDocuments": "Úprava více dokumentů naráz se nezdařila.", "@couldNotBulkEditDocuments": {}, "couldNotCreateCorrespondent": "Nepodařilo se vytvořit korespondenta, zkuste to znovu.", "@couldNotCreateCorrespondent": {}, "couldNotLoadCorrespondents": "Nezdařilo se načíst korespondenty.", "@couldNotLoadCorrespondents": {}, "couldNotCreateSavedView": "Nezdařilo se vytvořit uložený náhled, zkuste to znovu.", "@couldNotCreateSavedView": {}, "couldNotDeleteSavedView": "Nezdařilo se smazat uložený náhled, zkuste to znovu.", "@couldNotDeleteSavedView": {}, "youAreCurrentlyOffline": "Nelze stáhnout data: Nejste připojeni k internetu.", "@youAreCurrentlyOffline": {}, "couldNotAssignArchiveSerialNumber": "Nepodařilo se přiřadit archivní sériové číslo.", "@couldNotAssignArchiveSerialNumber": {}, "couldNotDeleteDocument": "Nezdařilo se smazat dokument, zkuste to znovu.", "@couldNotDeleteDocument": {}, "couldNotLoadDocuments": "Nezdařilo se načíst dokument, zkuste to znovu.", "@couldNotLoadDocuments": {}, "couldNotLoadDocumentPreview": "Nezdařilo se načíst náhled dokumentu.", "@couldNotLoadDocumentPreview": {}, "couldNotCreateDocument": "Nezdařilo se vytvořit dokument, zkuste to znovu.", "@couldNotCreateDocument": {}, "couldNotLoadDocumentTypes": "Nezdařilo se načíst dokument, zkuste to znovu.", "@couldNotLoadDocumentTypes": {}, "couldNotUpdateDocument": "Nezdařilo se aktualizovat dokument, zkuste to znovu.", "@couldNotUpdateDocument": {}, "couldNotUploadDocument": "Nezdařilo se nahrát dokument, zkuste to znovu.", "@couldNotUploadDocument": {}, "invalidCertificateOrMissingPassphrase": "Neplatný certifikát nebo chybějící přihlašovací fráze, zkuste to znovu.", "@invalidCertificateOrMissingPassphrase": {}, "couldNotLoadSavedViews": "Nezdařilo se načíst uložené náhledy.", "@couldNotLoadSavedViews": {}, "aClientCertificateWasExpectedButNotSent": "<PERSON><PERSON> certifik<PERSON><PERSON> klienta, kter<PERSON> ale nebyl z<PERSON>lán. Dodejte prosím platný certifikát klienta.", "@aClientCertificateWasExpectedButNotSent": {}, "userIsNotAuthenticated": "Uživatel není <PERSON>.", "@userIsNotAuthenticated": {}, "requestTimedOut": "Při dotazu na server došlo k překročení času odezvy.", "@requestTimedOut": {}, "anErrorOccurredRemovingTheScans": "Došlo k chybě při odstraňování skenů.", "@anErrorOccurredRemovingTheScans": {}, "couldNotReachYourPaperlessServer": "<PERSON><PERSON><PERSON> se př<PERSON><PERSON>jit k <PERSON> serveru, je opravdu spuštěn a dostupný?", "@couldNotReachYourPaperlessServer": {}, "couldNotLoadSimilarDocuments": "Nelze načíst podobné dokumenty.", "@couldNotLoadSimilarDocuments": {}, "couldNotCreateStoragePath": "Nelze vytvořit cestu k úložišti, zkuste to znovu.", "@couldNotCreateStoragePath": {}, "couldNotLoadStoragePaths": "Nelze načíst cestu k úložišti.", "@couldNotLoadStoragePaths": {}, "couldNotLoadSuggestions": "Návrhy se nepodařilo na<PERSON>.", "@couldNotLoadSuggestions": {}, "couldNotCreateTag": "Nelze vytvořit tag, zkuste to znovu.", "@couldNotCreateTag": {}, "couldNotLoadTags": "Nelze načíst tagy.", "@couldNotLoadTags": {}, "anUnknownErrorOccurred": "Došlo k neznámé chybě.", "@anUnknownErrorOccurred": {}, "fileFormatNotSupported": "Tento typ souboru není <PERSON>.", "@fileFormatNotSupported": {}, "report": "NAHLÁSIT", "@report": {}, "absolute": "Absolutní", "@absolute": {}, "hintYouCanAlsoSpecifyRelativeValues": "Tip: <PERSON><PERSON><PERSON> k<PERSON>rétního data lze také specifikovat relativní časovou odchylku k aktuálnímu datu.", "@hintYouCanAlsoSpecifyRelativeValues": {"description": "Displayed in the extended date range picker"}, "amount": "Počet", "@amount": {}, "relative": "Relativní", "@relative": {}, "last": "Poslední", "@last": {}, "timeUnit": "<PERSON><PERSON><PERSON><PERSON>", "@timeUnit": {}, "selectDateRange": "<PERSON><PERSON><PERSON><PERSON> rozmezí", "@selectDateRange": {}, "after": "Po", "@after": {}, "before": "<PERSON><PERSON><PERSON>", "@before": {}, "days": "{count, plural, zero{dnů} one{den} few{dny} many{dnů} other{dnů}}", "@days": {"placeholders": {"count": {}}}, "lastNDays": "{count, plural, zero{Posledních {count} dní} one{Včera} few{Poslední {count} dny} many{Posledních {count} dní} other{Posledních {count} dní}}", "@lastNDays": {"placeholders": {"count": {}}}, "lastNMonths": "{count, plural, zero{<PERSON><PERSON><PERSON>ch {count} m<PERSON><PERSON><PERSON><PERSON><PERSON>} one{Minulý měsíc} few{Minulé {count} měs<PERSON><PERSON>} many{Minulých {count} m<PERSON><PERSON><PERSON><PERSON><PERSON>} other{Minulých {count} mě<PERSON><PERSON><PERSON><PERSON>}}", "@lastNMonths": {"placeholders": {"count": {}}}, "lastNWeeks": "{count, plural, zero{Posledn<PERSON>ch {count} tý<PERSON><PERSON>} one{Minulý týden} few{Poslední {count} týdny} many{Posledních {count} tý<PERSON><PERSON>} other{Posledních {count} týdn<PERSON>}}", "@lastNWeeks": {"placeholders": {"count": {}}}, "lastNYears": "{count, plural, zero{Minulých {count} let} one{Minulý rok} few{Minulé {count} roky} many{Minulých {count} let} other{Minulých {count} let}}", "@lastNYears": {"placeholders": {"count": {}}}, "months": "{count, plural, zero{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} one{mě<PERSON><PERSON><PERSON>} few{<PERSON><PERSON><PERSON><PERSON>} many{m<PERSON><PERSON><PERSON><PERSON><PERSON>} other{m<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "@months": {"placeholders": {"count": {}}}, "weeks": "{count, plural, zero{tý<PERSON><PERSON>} one{týden} few{týdn<PERSON>} many{týdn<PERSON>} other{týdn<PERSON>}}", "@weeks": {"placeholders": {"count": {}}}, "years": "{count, plural, zero{let} one{rok} few{roky} many{let} other{let}}", "@years": {"placeholders": {"count": {}}}, "gotIt": "Rozumím!", "@gotIt": {}, "cancel": "Zrušit", "@cancel": {}, "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@close": {}, "create": "Vytvořit", "@create": {}, "delete": "<PERSON><PERSON><PERSON><PERSON>", "@delete": {}, "edit": "<PERSON><PERSON><PERSON><PERSON>", "@edit": {}, "ok": "Ok", "@ok": {}, "save": "Uložit", "@save": {}, "select": "<PERSON><PERSON><PERSON><PERSON>", "@select": {}, "saveChanges": "Aktualizovat", "@saveChanges": {}, "upload": "<PERSON><PERSON><PERSON><PERSON>", "@upload": {}, "youreOffline": "Jste offline.", "@youreOffline": {}, "deleteDocument": "Smazat dokument", "@deleteDocument": {"description": "Used as an action label on each inbox item"}, "removeDocumentFromInbox": "Dokument odstraněn z inboxu.", "@removeDocumentFromInbox": {}, "areYouSureYouWantToMarkAllDocumentsAsSeen": "Opravdu chcete označit všechny dokumenty jako shlédnuté? Toto provede hromadnou úpravu a odstraní inbox tag u všech dokumentů.\nToto je nevratná akce! Opravdu chcete pokračovat?", "@areYouSureYouWantToMarkAllDocumentsAsSeen": {}, "markAllAsSeen": "Označit vše jako s<PERSON>?", "@markAllAsSeen": {}, "allSeen": "Označit vše jako s<PERSON>", "@allSeen": {}, "markAsSeen": "Označit jako <PERSON>", "@markAsSeen": {}, "refresh": "Obnovit", "@refresh": {}, "youDoNotHaveUnseenDocuments": "<PERSON><PERSON><PERSON><PERSON> neshlédnuté dokument<PERSON>.", "@youDoNotHaveUnseenDocuments": {}, "quickAction": "Rychlá a<PERSON>ce", "@quickAction": {}, "suggestionSuccessfullyApplied": "Návhr úspěšně použit.", "@suggestionSuccessfullyApplied": {}, "today": "Dnes", "@today": {}, "undo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@undo": {}, "nUnseen": "{count} ne<PERSON><PERSON><PERSON><PERSON><PERSON>", "@nUnseen": {"placeholders": {"count": {}}}, "swipeLeftToMarkADocumentAsSeen": "Tip: Potáhnutím doleva označit dokument jako shl<PERSON>ý a odstranit všechny inbox tagy.", "@swipeLeftToMarkADocumentAsSeen": {}, "yesterday": "Včera", "@yesterday": {}, "anyAssigned": "Jakékoliv přiřazené", "@anyAssigned": {}, "noItemsFound": "Nic se neshoduje!", "@noItemsFound": {}, "caseIrrelevant": "Ignorovat velikost znaků", "@caseIrrelevant": {}, "matchingAlgorithm": "<PERSON><PERSON><PERSON><PERSON> shody", "@matchingAlgorithm": {}, "match": "S<PERSON><PERSON>", "@match": {}, "name": "Jméno", "@name": {}, "notAssigned": "Nepřiřazeno", "@notAssigned": {}, "addNewCorrespondent": "Založit nového korespondenta", "@addNewCorrespondent": {}, "noCorrespondentsSetUp": "Zatím nebyli založeni žádní korespondenti.", "@noCorrespondentsSetUp": {}, "correspondents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@correspondents": {}, "addNewDocumentType": "Založit nový typ dokumentu", "@addNewDocumentType": {}, "noDocumentTypesSetUp": "Zdá se že zatím nebyly založeny žádné typy dokumentů.", "@noDocumentTypesSetUp": {}, "documentTypes": "<PERSON><PERSON>", "@documentTypes": {}, "addNewStoragePath": "Vytvořit novou cestu k úložišti", "@addNewStoragePath": {}, "noStoragePathsSetUp": "Zdá se že zatím nebyly vytvořeny žádné cesty k úložištím.", "@noStoragePathsSetUp": {}, "storagePaths": "Cesty k úložišti", "@storagePaths": {}, "addNewTag": "Založit nový tag", "@addNewTag": {}, "noTagsSetUp": "Zdá se že zatím nebyly založeny žádné tagy.", "@noTagsSetUp": {}, "linkedDocuments": "Propoje<PERSON><PERSON>", "@linkedDocuments": {}, "advancedSettings": "Rozšířená nastavení", "@advancedSettings": {}, "passphrase": "Heslová fráze", "@passphrase": {}, "configureMutualTLSAuthentication": "Konfigurovat vzájemné TLS ověřování", "@configureMutualTLSAuthentication": {}, "invalidCertificateFormat": "Neplatný certif<PERSON>, povolen pouze .pfx formát.", "@invalidCertificateFormat": {}, "clientcertificate": "Klientský certifikát", "@clientcertificate": {}, "selectFile": "<PERSON><PERSON><PERSON><PERSON> soubor...", "@selectFile": {}, "continueLabel": "Po<PERSON><PERSON><PERSON><PERSON>", "@continueLabel": {}, "incorrectOrMissingCertificatePassphrase": "Chybná nebo chybějící he<PERSON>lová fráze certifikátu.", "@incorrectOrMissingCertificatePassphrase": {}, "connect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@connect": {}, "password": "He<PERSON><PERSON>", "@password": {}, "passwordMustNotBeEmpty": "<PERSON><PERSON><PERSON> b<PERSON> p<PERSON>.", "@passwordMustNotBeEmpty": {}, "connectionTimedOut": "Čas spojení vypršel.", "@connectionTimedOut": {}, "loginPageReachabilityMissingClientCertificateText": "<PERSON><PERSON> certifik<PERSON><PERSON> k<PERSON>, ale nebyl dodán. Dodejte prosím certifikát.", "@loginPageReachabilityMissingClientCertificateText": {}, "couldNotEstablishConnectionToTheServer": "Nepodařilo se navázat spojení se serverem.", "@couldNotEstablishConnectionToTheServer": {}, "connectionSuccessfulylEstablished": "Připojení úspěšně vytvořeno.", "@connectionSuccessfulylEstablished": {}, "hostCouldNotBeResolved": "Adresa nemo<PERSON>a být rozpoznána. Zkontrolujte prosím adresu serveru a své internetové připojení.", "@hostCouldNotBeResolved": {}, "serverAddress": "<PERSON><PERSON><PERSON>", "@serverAddress": {}, "invalidAddress": "Neplatná adresa", "@invalidAddress": {}, "serverAddressMustIncludeAScheme": "Adresa serveru musí obsahovat sch<PERSON>.", "@serverAddressMustIncludeAScheme": {}, "serverAddressMustNotBeEmpty": "Adresa <PERSON>u ne<PERSON> b<PERSON>t prázdná.", "@serverAddressMustNotBeEmpty": {}, "signIn": "Přihlásit", "@signIn": {}, "loginPageSignInTitle": "Přihlásit", "@loginPageSignInTitle": {}, "signInToServer": "Přihlásit k {serverAddress}", "@signInToServer": {"placeholders": {"serverAddress": {}}}, "connectToPaperless": "<PERSON><PERSON><PERSON> s <PERSON>", "@connectToPaperless": {}, "username": "Jméno už<PERSON>le", "@username": {}, "usernameMustNotBeEmpty": "Jméno uživatele nesmí být prázdné.", "@usernameMustNotBeEmpty": {}, "documentContainsAllOfTheseWords": "Dokument obsahuje všechna tato slova", "@documentContainsAllOfTheseWords": {}, "all": "<PERSON><PERSON><PERSON>", "@all": {}, "documentContainsAnyOfTheseWords": "Dokument obsahuje některé z těchto slov", "@documentContainsAnyOfTheseWords": {}, "any": "Jakékoliv slovo", "@any": {}, "learnMatchingAutomatically": "Učit přiřazování automaticky", "@learnMatchingAutomatically": {}, "auto": "Automatický", "@auto": {}, "documentContainsThisString": "Dokument obsahuje tento řetězec", "@documentContainsThisString": {}, "exact": "Př<PERSON>ný", "@exact": {}, "documentContainsAWordSimilarToThisWord": "Dokument obsahuje slovo podobné tomuto slovu", "@documentContainsAWordSimilarToThisWord": {}, "fuzzy": "Podobný", "@fuzzy": {}, "documentMatchesThisRegularExpression": "Dokument odpovídá regulárnímu výrazu", "@documentMatchesThisRegularExpression": {}, "regularExpression": "Regulární výraz", "@regularExpression": {}, "anInternetConnectionCouldNotBeEstablished": "Nezdařilo se vytvořit připojení k internetu.", "@anInternetConnectionCouldNotBeEstablished": {}, "done": "Hotovo", "@done": {}, "next": "Dalš<PERSON>", "@next": {}, "couldNotAccessReceivedFile": "Přístup k obdrženému souboru zamítnut. <PERSON><PERSON><PERSON>, zkus nejdříve otevřít aplikaci.", "@couldNotAccessReceivedFile": {}, "newView": "<PERSON><PERSON> náhled", "@newView": {}, "createsASavedViewBasedOnTheCurrentFilterCriteria": "Vytvoří nový náhled na základě aktuálních pravidel filtru.", "@createsASavedViewBasedOnTheCurrentFilterCriteria": {}, "createViewsToQuicklyFilterYourDocuments": "Vytvoře si různé náhledy pro rychlé filtrování dokumentů.", "@createViewsToQuicklyFilterYourDocuments": {}, "nFiltersSet": "{count, plural, zero{{count} filtrů nastaveno} one{{count} filtr nastaven} few{{count} filtry nastaveny} many{{count} filtrů nastaveno} other{{count} filtrů nastaveno}}", "@nFiltersSet": {"placeholders": {"count": {}}}, "showInSidebar": "Zobrazit v postranní liště", "@showInSidebar": {}, "showOnDashboard": "Zobrazit na hlavním panelu", "@showOnDashboard": {}, "views": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@views": {}, "clearAll": "Smazat všechny", "@clearAll": {}, "scan": "Sken", "@scan": {}, "previewScan": "<PERSON><PERSON><PERSON><PERSON>", "@previewScan": {}, "scrollToTop": "Scroll to top", "@scrollToTop": {}, "paperlessServerVersion": "Verze Paperless serveru", "@paperlessServerVersion": {}, "darkTheme": "Tmavý vzhled", "@darkTheme": {}, "lightTheme": "Světlý vzhled", "@lightTheme": {}, "systemTheme": "Použít systémový vzhled", "@systemTheme": {}, "appearance": "Vzhled", "@appearance": {}, "languageAndVisualAppearance": "Jazyk a vzhled", "@languageAndVisualAppearance": {}, "applicationSettings": "Aplikace", "@applicationSettings": {}, "colorSchemeHint": "Vyberte mezi klasickým barevným schématem inpirovaným Paperless zelenou nebo použijte dynamické barevn<PERSON> sch<PERSON>, založené na barvách běžícího systému.", "@colorSchemeHint": {}, "colorSchemeNotSupportedWarning": "Dynamické rozpoznávání barev podporují pouze přístroje se systémem Android 12 a vyšším. Vybrání dynamických barev na starších systémech nebude mít pravděpodobně žádný efekt.", "@colorSchemeNotSupportedWarning": {}, "colors": "Barvy", "@colors": {}, "language": "Jazyk", "@language": {}, "security": "Zabezpečení", "@security": {}, "mangeFilesAndStorageSpace": "Spravovat soubory a velikost úložiště", "@mangeFilesAndStorageSpace": {}, "storage": "Úložiště", "@storage": {}, "dark": "Tmavý", "@dark": {}, "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@light": {}, "system": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@system": {}, "ascending": "Vzestupně", "@ascending": {}, "descending": "Sestupně", "@descending": {}, "storagePathDay": "den", "@storagePathDay": {}, "storagePathMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@storagePathMonth": {}, "storagePathYear": "rok", "@storagePathYear": {}, "color": "<PERSON><PERSON>", "@color": {}, "filterTags": "Začni psát...", "@filterTags": {}, "inboxTag": "Tag inboxu", "@inboxTag": {}, "uploadInferValuesHint": "Pokud specif<PERSON><PERSON><PERSON><PERSON> hodnoty pro tato pole, paperless instance nebude automaticky přiřazovat naučené hodnoty. Pokud mají být tato pole automaticky vyplňována, nevyplňujte zde nic.", "@uploadInferValuesHint": {}, "useTheConfiguredBiometricFactorToAuthenticate": "Použít nastavené biometrické údaje pro ověření a odemčení dokumentů.", "@useTheConfiguredBiometricFactorToAuthenticate": {}, "verifyYourIdentity": "<PERSON><PERSON><PERSON>ř svou identitu", "@verifyYourIdentity": {}, "verifyIdentity": "Ověřit identitu", "@verifyIdentity": {}, "detailed": "Detailně", "@detailed": {}, "grid": "Mřížka", "@grid": {}, "list": "Seznam", "@list": {}, "remove": "Odstranit", "removeQueryFromSearchHistory": "Odstranit dotaz z historie vyhledávání?", "dynamicColorScheme": "Dynamicky", "@dynamicColorScheme": {}, "classicColorScheme": "<PERSON><PERSON><PERSON><PERSON>", "@classicColorScheme": {}, "notificationDownloadComplete": "Stahování <PERSON>", "@notificationDownloadComplete": {"description": "Notification title when a download has been completed."}, "notificationDownloadingDocument": "Stahování dokumentu", "@notificationDownloadingDocument": {"description": "Notification title shown when a document download is pending"}, "archiveSerialNumberUpdated": "Archivní sériové číslo aktualizováno.", "@archiveSerialNumberUpdated": {"description": "Message shown when the ASN has been updated."}, "donateCoffee": "Kupte mi kávu", "@donateCoffee": {"description": "Label displayed in the app drawer"}, "thisFieldIsRequired": "Toto pole je povinné!", "@thisFieldIsRequired": {"description": "Message shown below the form field when a required field has not been filled out."}, "confirm": "Potvrdit", "confirmAction": "Potvrdit akci", "@confirmAction": {"description": "Typically used as a title to confirm a previously selected action"}, "areYouSureYouWantToContinue": "Opravdu chcete pokračovat?", "bulkEditTagsAddMessage": "{count, plural, one{This operation will add the tags {tags} to the selected document.} other{This operation will add the tags {tags} to {count} selected documents.}}", "@bulkEditTagsAddMessage": {"description": "Message of the confirmation dialog when bulk adding tags."}, "bulkEditTagsRemoveMessage": "{count, plural, one{This operation will remove the tags {tags} from the selected document.} other{This operation will remove the tags {tags} from {count} selected documents.}}", "@bulkEditTagsRemoveMessage": {"description": "Message of the confirmation dialog when bulk removing tags."}, "bulkEditTagsModifyMessage": "{count, plural, one{This operation will add the tags {addTags} and remove the tags {removeTags} from the selected document.} other{This operation will add the tags {addTags} and remove the tags {removeTags} from {count} selected documents.}}", "@bulkEditTagsModifyMessage": {"description": "Message of the confirmation dialog when both adding and removing tags."}, "bulkEditCorrespondentAssignMessage": "{count, plural, one{This operation will assign the correspondent {correspondent} to the selected document.} other{This operation will assign the correspondent {correspondent} to {count} selected documents.}}", "bulkEditDocumentTypeAssignMessage": "{count, plural, one{This operation will assign the document type {docType} to the selected document.} other{This operation will assign the documentType {docType} to {count} selected documents.}}", "bulkEditStoragePathAssignMessage": "{count, plural, one{This operation will assign the storage path {path} to the selected document.} other{This operation will assign the storage path {path} to {count} selected documents.}}", "bulkEditCorrespondentRemoveMessage": "{count, plural, one{This operation will remove the correspondent from the selected document.} other{This operation will remove the correspondent from {count} selected documents.}}", "bulkEditDocumentTypeRemoveMessage": "{count, plural, one{This operation will remove the document type from the selected document.} other{This operation will remove the document type from {count} selected documents.}}", "bulkEditStoragePathRemoveMessage": "{count, plural, one{This operation will remove the storage path from the selected document.} other{This operation will remove the storage path from {count} selected documents.}}", "anyTag": "Jak<PERSON><PERSON><PERSON><PERSON>", "@anyTag": {"description": "Label shown when any tag should be filtered"}, "allTags": "Všechny", "@allTags": {"description": "Label shown when a document has to be assigned to all selected tags"}, "switchingAccountsPleaseWait": "Přepínání <PERSON>. Počkejte prosím...", "@switchingAccountsPleaseWait": {"description": "Message shown while switching accounts is in progress."}, "testConnection": "Ověřit připojení", "@testConnection": {"description": "Button label shown on login page. Allows user to test whether the server is reachable or not."}, "accounts": "Úč<PERSON>", "@accounts": {"description": "Title of the account management dialog"}, "addAccount": "Přidat <PERSON>", "@addAccount": {"description": "Label of add account action"}, "switchAccount": "Přepnout", "@switchAccount": {"description": "Label for switch account action"}, "logout": "Odhlásit", "@logout": {"description": "Generic Logout label"}, "switchAccountTitle": "Přepnout účet", "@switchAccountTitle": {"description": "Title of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "switchToNewAccount": "Do you want to switch to the new account? You can switch back at any time.", "@switchToNewAccount": {"description": "Content of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "sourceCode": "Source Code", "findTheSourceCodeOn": "Find the source code on", "@findTheSourceCodeOn": {"description": "Text before link to Paperless Mobile GitHub"}, "rememberDecision": "Remember my decision", "defaultDownloadFileType": "Default Download File Type", "@defaultDownloadFileType": {"description": "Label indicating the default filetype to download (one of archived, original and always ask)"}, "defaultShareFileType": "Default Share File Type", "@defaultShareFileType": {"description": "Label indicating the default filetype to share (one of archived, original and always ask)"}, "alwaysAsk": "Always ask", "@alwaysAsk": {"description": "Option to choose when the app should always ask the user which filetype to use"}, "disableMatching": "Do not tag documents automatically", "@disableMatching": {"description": "One of the options for automatic tagging of documents"}, "none": "None", "@none": {"description": "One of available enum values of matching algorithm for tags"}, "logInToExistingAccount": "Log in to existing account", "@logInToExistingAccount": {"description": "Title shown on login page if at least one user is already known to the app."}, "print": "Print", "@print": {"description": "Tooltip for print button"}, "managePermissions": "Manage permissions", "@managePermissions": {"description": "Button which leads user to manage permissions page"}, "errorRetrievingServerVersion": "An error occurred trying to resolve the server version.", "@errorRetrievingServerVersion": {"description": "Message shown at the bottom of the settings page when the remote server version could not be resolved."}, "resolvingServerVersion": "Resolving server version...", "@resolvingServerVersion": {"description": "Message shown while the app is loading the remote server version."}, "goToLogin": "Go to login", "@goToLogin": {"description": "Label of the button shown on the login page to skip logging in to existing accounts and navigate user to login page"}, "export": "Export", "@export": {"description": "Label for button that exports scanned images to pdf (before upload)"}, "invalidFilenameCharacter": "Invalid character(s) found in filename: {characters}", "@invalidFilenameCharacter": {"description": "For validating filename in export dialogue"}, "exportScansToPdf": "Export scans to PDF", "@exportScansToPdf": {"description": "title of the alert dialog when exporting scans to pdf"}, "allScansWillBeMerged": "All scans will be merged into a single PDF file.", "behavior": "Behavior", "@behavior": {"description": "Title of the settings concerning app beahvior"}, "theme": "Theme", "@theme": {"description": "Title of the theme mode setting"}, "clearCache": "Clear cache", "@clearCache": {"description": "Title of the clear cache setting"}, "freeBytes": "Free {byteString}", "@freeBytes": {"description": "Text shown for clear storage settings"}, "calculatingDots": "Calculating...", "@calculatingDots": {"description": "Text shown when the byte size is still being calculated"}, "freedDiskSpace": "Successfully freed {bytes} of disk space.", "@freedDiskSpace": {"description": "Message shown after clearing storage"}, "uploadScansAsPdf": "Upload scans as PDF", "@uploadScansAsPdf": {"description": "Title of the setting which toggles whether scans are always uploaded as pdf"}, "convertSinglePageScanToPdf": "Always convert single page scans to PDF before uploading", "@convertSinglePageScanToPdf": {"description": "description of the upload scans as pdf setting"}, "loginRequiredPermissionsHint": "Using Paperless Mobile requires a minimum set of user permissions since paperless-ngx 1.14.0 and higher. Therefore, please make sure that the user to be logged in has the permission to view other users (User → View) and the settings (UISettings → View). If you do not have these permissions, please contact an administrator of your paperless-ngx server.", "@loginRequiredPermissionsHint": {"description": "Hint shown on the login page informing the user of the required permissions to use the app."}, "missingPermissions": "You do not have the necessary permissions to perform this action.", "@missingPermissions": {"description": "Message shown in a snackbar when a user without the reequired permissions performs an action."}, "editView": "Edit View", "@editView": {"description": "Title of the edit saved view page"}, "donate": "Donate", "@donate": {"description": "Label of the in-app donate button"}, "donationDialogContent": "Thank you for considering to support this app! Due to both Google's and Apple's Payment Policies, no links leading to donations may be displayed in-app. Not even linking to the project's repository page appears to be allowed in this context. Therefore, maybe have a look at the 'Donations' section in the project's README. Your support is much appreciated and keeps the development of this app alive. Thanks!", "@donationDialogContent": {"description": "Text displayed in the donation dialog"}, "noDocumentsFound": "No documents found.", "@noDocumentsFound": {"description": "Message shown when no documents were found."}, "couldNotDeleteCorrespondent": "Could not delete correspondent, please try again.", "@couldNotDeleteCorrespondent": {"description": "Message shown in snackbar when a correspondent could not be deleted."}, "couldNotDeleteDocumentType": "Could not delete document type, please try again.", "@couldNotDeleteDocumentType": {"description": "Message shown when a document type could not be deleted"}, "couldNotDeleteTag": "Could not delete tag, please try again.", "@couldNotDeleteTag": {"description": "Message shown when a tag could not be deleted"}, "couldNotDeleteStoragePath": "Could not delete storage path, please try again.", "@couldNotDeleteStoragePath": {"description": "Message shown when a storage path could not be deleted"}, "couldNotUpdateCorrespondent": "Could not update correspondent, please try again.", "@couldNotUpdateCorrespondent": {"description": "Message shown when a correspondent could not be updated"}, "couldNotUpdateDocumentType": "Could not update document type, please try again.", "@couldNotUpdateDocumentType": {"description": "Message shown when a document type could not be updated"}, "couldNotUpdateTag": "Could not update tag, please try again.", "@couldNotUpdateTag": {"description": "Message shown when a tag could not be updated"}, "couldNotLoadServerInformation": "Could not load server information.", "@couldNotLoadServerInformation": {"description": "Message shown when the server information could not be loaded"}, "couldNotLoadStatistics": "Could not load server statistics.", "@couldNotLoadStatistics": {"description": "Message shown when the server statistics could not be loaded"}, "couldNotLoadUISettings": "Could not load UI settings.", "@couldNotLoadUISettings": {"description": "Message shown when the UI settings could not be loaded"}, "couldNotLoadTasks": "Could not load tasks.", "@couldNotLoadTasks": {"description": "Message shown when the tasks (e.g. document consumed) could not be loaded"}, "userNotFound": "User could not be found.", "@userNotFound": {"description": "Message shown when the specified user (e.g. by id) could not be found"}, "couldNotUpdateSavedView": "Could not update saved view, please try again.", "@couldNotUpdateSavedView": {"description": "Message shown when a saved view could not be updated"}, "couldNotUpdateStoragePath": "Could not update storage path, please try again.", "savedViewSuccessfullyUpdated": "Saved view successfully updated.", "@savedViewSuccessfullyUpdated": {"description": "Message shown when a saved view was successfully updated."}, "discardChanges": "Discard changes?", "@discardChanges": {"description": "Title of the alert dialog shown when a user tries to close a view with unsaved changes."}, "savedViewChangedDialogContent": "The filter conditions of the active view have changed. By resetting the filter, these changes will be lost. Do you still wish to continue?", "@savedViewChangedDialogContent": {"description": "Content of the alert dialog shown when all of the following applies:\r\n* User has saved view selected\r\n* User has performed changes to the current document filter\r\n* User now tries to reset this filter without having saved the changes to the view."}, "createFromCurrentFilter": "Create from current filter", "@createFromCurrentFilter": {"description": "Tooltip of the \"New saved view\" button"}, "home": "Home", "@home": {"description": "Label of the \"Home\" route"}, "welcomeUser": "Welcome, {name}!", "@welcomeUser": {"description": "Top message shown on the home page"}, "statistics": "Statistics", "documentsInInbox": "Documents in inbox", "totalDocuments": "Total documents", "totalCharacters": "Total characters", "showAll": "Show all", "@showAll": {"description": "Button label shown on a saved view preview to open this view in the documents page"}, "userAlreadyExists": "This user already exists.", "@userAlreadyExists": {"description": "Error message shown when the user tries to add an already existing account."}, "youDidNotSaveAnyViewsYet": "You did not save any views yet, create one and it will be shown here.", "@youDidNotSaveAnyViewsYet": {"description": "Message shown when there are no saved views yet."}, "tryAgain": "Try again", "discardFile": "Discard file?", "discard": "Discard", "backToLogin": "Back to login", "skipEditingReceivedFiles": "Skip editing received files", "uploadWithoutPromptingUploadForm": "Always upload without prompting the upload form when sharing files with the app.", "authenticatingDots": "Authenticating...", "@authenticatingDots": {"description": "Message shown when the app is authenticating the user"}, "persistingUserInformation": "Persisting user information...", "fetchingUserInformation": "Fetching user information...", "@fetchingUserInformation": {"description": "Message shown when the app loads user data from the server"}, "restoringSession": "Restoring session...", "@restoringSession": {"description": "Message shown when the user opens the app and the previous user is tried to be authenticated and logged in"}, "documentsAssigned": "{count, plural, zero{No documents} one{1 document} other{{count} documents}}", "@documentsAssigned": {"description": "Text shown with a correspondent, document type etc. to indicate the number of documents this filter will maximally yield."}, "discardChangesWarning": "You have unsaved changes. By continuing, all changes will be lost. Do you want to discard these changes?", "@discardChangesWarning": {"description": "Warning message shown when the user tries to close a route without saving the changes."}, "changelog": "Changelog", "noLogsFoundOn": "No logs found on {date}.", "logfileBottomReached": "You have reached the bottom of this logfile.", "appLogs": "App logs {date}", "saveLogsToFile": "Save logs to file", "copyToClipboard": "Copy to clipboard", "couldNotLoadLogfileFrom": "Could not load logfile from {date}.", "loadingLogsFrom": "Loading logs from {date}...", "clearLogs": "Clear logs from {date}", "showPdf": "Show PDF", "@showPdf": {"description": "Tooltip shown on the \"show pdf\" button on the document edit page"}, "hidePdf": "Hide PDF", "@hidePdf": {"description": "Tooltip shown on the \"show pdf\" icon button on the document edit page"}, "misc": "Miscellaneous", "loggingOut": "Logging out...", "testingConnection": "Testing connection...", "@testingConnection": {"description": "Text shown while the app tries to establish a connection to the specified host."}, "version": "Version {versionCode}", "notes": "{count, plural, zero{Notes} one{Note} other{Notes}}", "addNote": "Add note", "newerVersionAvailable": "Newer version available:", "dateOutOfRange": "Date must be between {firstDate} and {lastDate}.", "@dateOutOfRange": {"description": "Error message shown when the user tries to select a date outside of the allowed range.", "placeholders": {"firstDate": {"type": "DateTime", "format": "yMd"}, "lastDate": {"type": "DateTime", "format": "yMd"}}}, "permissions": "Permissions", "newNote": "New note", "notesMarkdownSyntaxSupportHint": "Paperless Mobile can render notes using basic markdown syntax. Try it out!"}
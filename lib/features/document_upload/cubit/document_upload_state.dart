import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:equatable/equatable.dart';
import 'package:paperless_api/paperless_api.dart';



part 'document_upload_state.g.dart';

@CopyWith()
class DocumentUploadState extends Equatable {
  final double? uploadProgress;
  final List<UserPermission> userViews;
  final List<UserPermission> userEditor;
  final List<Department> departmentViews;
  final List<Department> departmentEditor;
  final List<Project> projects;

  const DocumentUploadState({
    this.userViews = const [],
    this.userEditor = const [],
    this.departmentEditor = const [],
    this.departmentViews = const [],
    this.uploadProgress,
    this.projects = const [],
  });

  @override
  List<Object?> get props => [
        userViews,
        uploadProgress,
        userEditor,
        departmentEditor,
        departmentViews,
        projects
      ];
}

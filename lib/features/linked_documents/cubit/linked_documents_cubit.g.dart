// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'linked_documents_cubit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LinkedDocumentsState _$LinkedDocumentsStateFromJson(
        Map<String, dynamic> json) =>
    LinkedDocumentsState(
      viewType: $enumDecodeNullable(_$ViewTypeEnumMap, json['viewType']) ??
          ViewType.list,
    );

Map<String, dynamic> _$LinkedDocumentsStateToJson(
        LinkedDocumentsState instance) =>
    <String, dynamic>{
      'viewType': _$ViewTypeEnumMap[instance.viewType]!,
    };

const _$ViewTypeEnumMap = {
  ViewType.grid: 'grid',
  ViewType.list: 'list',
  ViewType.detailed: 'detailed',
};

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'date_range_unit.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DateRangeUnitAdapter extends TypeAdapter<DateRangeUnit> {
  @override
  final int typeId = 112;

  @override
  DateRangeUnit read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DateRangeUnit.day;
      case 1:
        return DateRangeUnit.week;
      case 2:
        return DateRangeUnit.month;
      case 3:
        return DateRangeUnit.year;
      default:
        return DateRangeUnit.day;
    }
  }

  @override
  void write(BinaryWriter writer, DateRangeUnit obj) {
    switch (obj) {
      case DateRangeUnit.day:
        writer.writeByte(0);
        break;
      case DateRangeUnit.week:
        writer.writeByte(1);
        break;
      case DateRangeUnit.month:
        writer.writeByte(2);
        break;
      case DateRangeUnit.year:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DateRangeUnitAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

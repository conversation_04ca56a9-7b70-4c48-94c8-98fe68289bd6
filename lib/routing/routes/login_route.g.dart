// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $loginRoute,
    ];

RouteBase get $loginRoute => GoRouteData.$route(
      path: '/login',
      name: 'login',
      parentNavigatorKey: LoginRoute.$parentNavigatorKey,
      factory: $LoginRouteExtension._fromState,
      routes: [
        GoRouteData.$route(
          path: 'switching-account',
          name: 'switchingAccount',
          parentNavigatorKey: SwitchingAccountsRoute.$parentNavigatorKey,
          factory: $SwitchingAccountsRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: 'authenticating',
          name: 'authenticating',
          parentNavigatorKey: AuthenticatingRoute.$parentNavigatorKey,
          factory: $AuthenticatingRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: 'verify-identity',
          name: 'verifyIdentity',
          parentNavigatorKey: VerifyIdentityRoute.$parentNavigatorKey,
          factory: $VerifyIdentityRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: 'existing',
          name: 'loginToExistingAccount',
          parentNavigatorKey: LoginToExistingAccountRoute.$parentNavigatorKey,
          factory: $LoginToExistingAccountRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: 'restoring-session',
          name: 'restoringSession',
          parentNavigatorKey: RestoringSessionRoute.$parentNavigatorKey,
          factory: $RestoringSessionRouteExtension._fromState,
        ),
      ],
    );

extension $LoginRouteExtension on LoginRoute {
  static LoginRoute _fromState(GoRouterState state) => LoginRoute(
        serverUrl: state.uri.queryParameters['server-url'],
        username: state.uri.queryParameters['username'],
        password: state.uri.queryParameters['password'],
        $extra: state.extra as ClientCertificate?,
      );

  String get location => GoRouteData.$location(
        '/login',
        queryParams: {
          if (serverUrl != null) 'server-url': serverUrl,
          if (username != null) 'username': username,
          if (password != null) 'password': password,
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $SwitchingAccountsRouteExtension on SwitchingAccountsRoute {
  static SwitchingAccountsRoute _fromState(GoRouterState state) =>
      const SwitchingAccountsRoute();

  String get location => GoRouteData.$location(
        '/login/switching-account',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $AuthenticatingRouteExtension on AuthenticatingRoute {
  static AuthenticatingRoute _fromState(GoRouterState state) =>
      AuthenticatingRoute(
        state.uri.queryParameters['check-login-stage-name']!,
      );

  String get location => GoRouteData.$location(
        '/login/authenticating',
        queryParams: {
          'check-login-stage-name': checkLoginStageName,
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $VerifyIdentityRouteExtension on VerifyIdentityRoute {
  static VerifyIdentityRoute _fromState(GoRouterState state) =>
      VerifyIdentityRoute(
        userId: state.uri.queryParameters['user-id']!,
      );

  String get location => GoRouteData.$location(
        '/login/verify-identity',
        queryParams: {
          'user-id': userId,
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $LoginToExistingAccountRouteExtension on LoginToExistingAccountRoute {
  static LoginToExistingAccountRoute _fromState(GoRouterState state) =>
      const LoginToExistingAccountRoute();

  String get location => GoRouteData.$location(
        '/login/existing',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $RestoringSessionRouteExtension on RestoringSessionRoute {
  static RestoringSessionRoute _fromState(GoRouterState state) =>
      const RestoringSessionRoute();

  String get location => GoRouteData.$location(
        '/login/restoring-session',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

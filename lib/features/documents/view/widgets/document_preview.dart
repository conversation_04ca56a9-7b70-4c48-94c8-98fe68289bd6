import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/accessibility/accessibility_utils.dart';
import 'package:paperless_mobile/helpers/connectivity_aware_action_wrapper.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class DocumentPreview extends StatelessWidget {
  final int documentId;
  final String? title;
  final BoxFit fit;
  final Alignment alignment;
  final double borderRadius;
  final bool enableHero;
  final double scale;
  final bool isClickable;
  final double? width;
  final double? height;

  const DocumentPreview(
      {super.key,
      required this.documentId,
      this.fit = BoxFit.cover,
      this.alignment = Alignment.topCenter,
      this.borderRadius = 12.0,
      this.enableHero = true,
      this.scale = 1.1,
      this.isClickable = true,
      this.title,
      this.height,
      this.width});

  @override
  Widget build(BuildContext context) {
    return ConnectivityAwareActionWrapper(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: isClickable
            ? () => DocumentPreviewRoute(
                  id: documentId,
                  title: title,
                ).push(context)
            : null,
        child: Builder(builder: (context) {
          if (enableHero) {
            return Hero(
              tag: "thumb_$documentId",
              child: _buildPreview(context),
            ).accessible();
          }
          return _buildPreview(context);
        }),
      ),
    );
  }

  Widget _buildPreview(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: SizedBox(
          width: width,
          height: height,
          child: CachedNetworkImage(
            fit: fit,
            alignment: alignment,
            cacheKey: "thumb_$documentId",
            imageUrl: context
                .read<PaperlessDocumentsApi>()
                .getThumbnailUrl(documentId),
            errorWidget: (ctxt, msg, __) => Text(msg),
            placeholder: (context, value) => Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: SizedBox(
                height: height ?? 180,
                width: width ?? 200,
              ),
            ),
            cacheManager: context.watch<CacheManager>(),
          ),
        ),
      ),
    );
  }
}

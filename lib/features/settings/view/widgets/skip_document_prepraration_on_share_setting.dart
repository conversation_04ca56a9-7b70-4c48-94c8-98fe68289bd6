import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/features/settings/view/widgets/global_settings_builder.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class SkipDocumentPreprationOnShareSetting extends StatelessWidget {
  const SkipDocumentPreprationOnShareSetting({super.key});

  @override
  Widget build(BuildContext context) {
    return GlobalSettingsBuilder(
      builder: (context, settings) {
        return ListTile(
          title: Text(S.of(context)!.skipEditingReceivedFiles),
          subtitle: Text(S.of(context)!.uploadWithoutPromptingUploadForm),
          trailing: CupertinoSwitch(
            value: settings.skipDocumentPreprarationOnUpload,
            activeColor: AppColor.primary,
            // trackColor: AppColor.white,
            onChanged: (value) {
              settings.skipDocumentPreprarationOnUpload = value;
              settings.save();
            },
          ),
          onTap: () {
            final value = !settings.skipDocumentPreprarationOnUpload;
            settings.skipDocumentPreprarationOnUpload = value;
            settings.save();
          },
        );
      },
    );
  }
}

{"developedBy": "<PERSON><PERSON><PERSON><PERSON> przez {name}.", "@developedBy": {"placeholders": {"name": {}}}, "addAnotherAccount": "<PERSON><PERSON><PERSON> kole<PERSON>e konto", "@addAnotherAccount": {}, "account": "Ko<PERSON>", "@account": {}, "addCorrespondent": "<PERSON><PERSON><PERSON>", "@addCorrespondent": {"description": "Title when adding a new correspondent"}, "addDocumentType": "Nowy rodzaj dokumentu", "@addDocumentType": {"description": "Title when adding a new document type"}, "addStoragePath": "Do<PERSON>j <PERSON> zapisu", "@addStoragePath": {"description": "Title when adding a new storage path"}, "addTag": "Nowy tag", "@addTag": {"description": "Title when adding a new tag"}, "aboutThisApp": "O ap<PERSON>ji", "@aboutThisApp": {"description": "Label for about this app tile displayed in the drawer"}, "loggedInAs": "<PERSON><PERSON><PERSON><PERSON> jako {name}", "@loggedInAs": {"placeholders": {"name": {}}}, "disconnect": "Rozłącz się", "@disconnect": {"description": "Logout button label"}, "reportABug": "Zgł<PERSON>ś błąd", "@reportABug": {}, "settings": "Ustawienia", "@settings": {}, "authenticateOnAppStart": "Uwierzytelnij przy starcie aplikacji", "@authenticateOnAppStart": {"description": "Description of the biometric authentication settings tile"}, "biometricAuthentication": "Uwierzytelnianie biometryczne", "@biometricAuthentication": {}, "authenticateToToggleBiometricAuthentication": "{mode, select, enable{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby wł<PERSON><PERSON><PERSON><PERSON> uwierzytelnianie biometryczne} disable{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby wyłą<PERSON><PERSON>ć uwierzytelnianie biometryczne} other{}}", "@authenticateToToggleBiometricAuthentication": {"placeholders": {"mode": {}}}, "documents": "Documents", "@documents": {}, "inbox": "Skrzynka odbiorcza", "@inbox": {}, "labels": "Etykiety", "@labels": {}, "scanner": "<PERSON><PERSON><PERSON>", "@scanner": {}, "startTyping": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>...", "@startTyping": {}, "doYouReallyWantToDeleteThisView": "<PERSON>zy na pewno chcesz usunąć ten widok?", "@doYouReallyWantToDeleteThisView": {}, "deleteView": "<PERSON><PERSON><PERSON> widok {name}?", "@deleteView": {}, "addedAt": "Dodano", "@addedAt": {}, "archiveSerialNumber": "<PERSON><PERSON><PERSON>", "@archiveSerialNumber": {}, "asn": "ASN", "@asn": {}, "correspondent": "Korespondent", "@correspondent": {}, "createdAt": "Utworzono", "@createdAt": {}, "documentSuccessfullyDeleted": "Dokument pomyślnie usunięty.", "@documentSuccessfullyDeleted": {}, "assignAsn": "Przypisz ASN", "@assignAsn": {}, "deleteDocumentTooltip": "Usuń", "@deleteDocumentTooltip": {"description": "Tooltip shown for the delete button on details page"}, "downloadDocumentTooltip": "<PERSON><PERSON><PERSON>", "@downloadDocumentTooltip": {"description": "Tooltip shown for the download button on details page"}, "editDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@editDocumentTooltip": {"description": "Tooltip shown for the edit button on details page"}, "loadFullContent": "Załaduj pełną treść", "@loadFullContent": {}, "noAppToDisplayPDFFilesFound": "Nie znaleziono aplikacji do wyświetlania plików PDF", "@noAppToDisplayPDFFilesFound": {}, "openInSystemViewer": "Otwórz w przeglądarce systemowej", "@openInSystemViewer": {}, "couldNotOpenFilePermissionDenied": "Nie można otworzyć pliku: ", "@couldNotOpenFilePermissionDenied": {}, "previewTooltip": "Podgląd", "@previewTooltip": {"description": "Tooltip shown for the preview button on details page"}, "shareTooltip": "Udostępnij", "@shareTooltip": {"description": "Tooltip shown for the share button on details page"}, "similarDocuments": "Podobne Do<PERSON>menty", "@similarDocuments": {"description": "Label shown in the tabbar on details page"}, "content": "<PERSON><PERSON><PERSON><PERSON>", "@content": {"description": "Label shown in the tabbar on details page"}, "metaData": "<PERSON>a dane", "@metaData": {"description": "Label shown in the tabbar on details page"}, "overview": "Przegląd", "@overview": {"description": "Label shown in the tabbar on details page"}, "documentType": "Rodzaj dokumentu", "@documentType": {}, "archivedPdf": "Zarchiwizowany (pdf)", "@archivedPdf": {"description": "Option to chose when downloading a document"}, "chooseFiletype": "Wybierz typ pliku", "@chooseFiletype": {}, "original": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@original": {"description": "Option to chose when downloading a document"}, "documentSuccessfullyDownloaded": "Pobieranie dokumentu udane.", "@documentSuccessfullyDownloaded": {}, "suggestions": "Sugestie: ", "@suggestions": {}, "editDocument": "Edyt<PERSON>j <PERSON>", "@editDocument": {}, "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@advanced": {}, "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@apply": {}, "extended": "Rozszerz", "@extended": {}, "titleAndContent": "Tytuł i treść", "@titleAndContent": {}, "title": "<PERSON><PERSON><PERSON>", "@title": {}, "reset": "Zresetuj", "@reset": {}, "filterDocuments": "Filtrowanie dokumentów", "@filterDocuments": {"description": "Title of the document filter"}, "originalMD5Checksum": "MD5-Checksum (suma kontrolna) oryginału", "@originalMD5Checksum": {}, "mediaFilename": "Nazwa pliku", "@mediaFilename": {}, "originalFileSize": "Rozmiar oryginalnego pliku", "@originalFileSize": {}, "originalMIMEType": "MIME-Type oryginału", "@originalMIMEType": {}, "modifiedAt": "Zmodyfikowan<PERSON>", "@modifiedAt": {}, "preview": "Podgląd", "@preview": {"description": "Title of the document preview page"}, "scanADocument": "Zeskanuj dokument", "@scanADocument": {}, "noDocumentsScannedYet": "Brak zeskanowanych dokumentów.", "@noDocumentsScannedYet": {}, "or": "lub", "@or": {"description": "Used on the scanner page between both main actions when no scans have been captured."}, "deleteAllScans": "Usuń wszystkie skany", "@deleteAllScans": {}, "uploadADocumentFromThisDevice": "Prześlij dokument z tego urządzenia", "@uploadADocumentFromThisDevice": {"description": "Button label on scanner page"}, "noMatchesFound": "Nie znaleziono pasujących elementów.", "@noMatchesFound": {"description": "Displayed when no documents were found in the document search."}, "removeFromSearchHistory": "<PERSON><PERSON>ąć z historii wyszukiwania?", "@removeFromSearchHistory": {}, "results": "Wyniki", "@results": {"description": "Label displayed above search results in document search."}, "searchDocuments": "Szukaj dokumentów", "@searchDocuments": {}, "resetFilter": "Zresetuj filtr", "@resetFilter": {}, "lastMonth": "Ostatni <PERSON>", "@lastMonth": {}, "last7Days": "Ostatnie 7 dni", "@last7Days": {}, "last3Months": "Ostatnie 3 miesiące", "@last3Months": {}, "lastYear": "Ostatni rok", "@lastYear": {}, "search": "Szukaj", "@search": {}, "documentsSuccessfullyDeleted": "Dokument pomyślnie usunięty.", "@documentsSuccessfullyDeleted": {}, "thereSeemsToBeNothingHere": "Wygląda na to, że nic tu nie ma...", "@thereSeemsToBeNothingHere": {}, "oops": "Ups.", "@oops": {}, "newDocumentAvailable": "Nowy dokument dostępny!", "@newDocumentAvailable": {}, "orderBy": "<PERSON><PERSON><PERSON><PERSON>", "@orderBy": {}, "thisActionIsIrreversibleDoYouWishToProceedAnyway": "<PERSON> czynn<PERSON> jest nieodwracalna. <PERSON><PERSON> mimo to ch<PERSON>z kontynuować?", "@thisActionIsIrreversibleDoYouWishToProceedAnyway": {}, "confirmDeletion": "Potwierdź usunięcie", "@confirmDeletion": {}, "areYouSureYouWantToDeleteTheFollowingDocuments": "{count,plural, one{<PERSON><PERSON> na pewno chcesz usunąć ten plik?} few {<PERSON>zy na pewno chcesz usunąć {count} pliki?} other {<PERSON><PERSON> na pewno chcesz usunąć {count} plików?}}", "@areYouSureYouWantToDeleteTheFollowingDocuments": {"placeholders": {"count": {}}}, "countSelected": "<PERSON><PERSON><PERSON><PERSON> {count}", "@countSelected": {"description": "Displayed in the appbar when at least one document is selected.", "placeholders": {"count": {}}}, "storagePath": "Ścieżka zapisu", "@storagePath": {}, "prepareDocument": "Przygotuj dokument", "@prepareDocument": {}, "tags": "Tagi", "@tags": {}, "documentSuccessfullyUpdated": "Dokument został pomyślnie zaktualizowany ", "@documentSuccessfullyUpdated": {}, "fileName": "Nazwa <PERSON>u", "@fileName": {}, "synchronizeTitleAndFilename": "Synchronizuj tytuł i nazwę pliku", "@synchronizeTitleAndFilename": {}, "reload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@reload": {}, "documentSuccessfullyUploadedProcessing": "Dokument pomyślnie przesłany, przetwarzam...", "@documentSuccessfullyUploadedProcessing": {}, "deleteLabelWarningText": "Ta etykieta zawiera odniesienia do innych dokumentów. Usuwając tę etykietę, wszystkie odniesienia zostaną usunięte. Kontynuować?", "@deleteLabelWarningText": {}, "couldNotAcknowledgeTasks": "<PERSON>e udało się potwierdzić zadań.", "@couldNotAcknowledgeTasks": {}, "authenticationFailedPleaseTryAgain": "Uwierzytelnienie nie powiodło się, spróbuj ponownie.", "@authenticationFailedPleaseTryAgain": {}, "anErrorOccurredWhileTryingToAutocompleteYourQuery": "Wystąpił błąd podczas próby automatycznego uzupełniania zapytania.", "@anErrorOccurredWhileTryingToAutocompleteYourQuery": {}, "biometricAuthenticationFailed": "Uwierzytelnianie biometryczne nie powiodło się.", "@biometricAuthenticationFailed": {}, "biometricAuthenticationNotSupported": "Uwierzytelnianie biometryczne nie jest obsługiwane na tym urządzeniu.", "@biometricAuthenticationNotSupported": {}, "couldNotBulkEditDocuments": "<PERSON>e udało się edytować wielu dokumentów.", "@couldNotBulkEditDocuments": {}, "couldNotCreateCorrespondent": "<PERSON><PERSON> udało się utworzyć korespondenta, spróbuj ponownie.", "@couldNotCreateCorrespondent": {}, "couldNotLoadCorrespondents": "<PERSON>e udało się załadować korespondentów.", "@couldNotLoadCorrespondents": {}, "couldNotCreateSavedView": "<PERSON>e udało się utworzyć zapisanego widoku, spróbuj ponownie.", "@couldNotCreateSavedView": {}, "couldNotDeleteSavedView": "<PERSON><PERSON> udało się usunąć zapisanego widoku, spróbuj ponownie", "@couldNotDeleteSavedView": {}, "youAreCurrentlyOffline": "Je<PERSON>ś obecnie w trybie offline. Upew<PERSON>j się, że jesteś połączony z Internetem.", "@youAreCurrentlyOffline": {}, "couldNotAssignArchiveSerialNumber": "<PERSON><PERSON> udało się przypisać numeru seryjnego archiwum (ASN).", "@couldNotAssignArchiveSerialNumber": {}, "couldNotDeleteDocument": "<PERSON><PERSON> udało się usunąć dokumentu, spróbuj ponownie.", "@couldNotDeleteDocument": {}, "couldNotLoadDocuments": "<PERSON>e udało się załadować dokumentów, spróbuj ponownie.", "@couldNotLoadDocuments": {}, "couldNotLoadDocumentPreview": "Nie udało się załadować podglądu dokumentu.", "@couldNotLoadDocumentPreview": {}, "couldNotCreateDocument": "<PERSON>e udało się utworzyć dokumentu, spróbuj ponownie.", "@couldNotCreateDocument": {}, "couldNotLoadDocumentTypes": "<PERSON>e udało się załadować typów dokumentów, spróbuj ponownie.", "@couldNotLoadDocumentTypes": {}, "couldNotUpdateDocument": "<PERSON><PERSON> udało się zmodyfikować dokumentu, spróbuj ponownie.", "@couldNotUpdateDocument": {}, "couldNotUploadDocument": "<PERSON><PERSON> udało się przesłać dokumentu, spróbuj ponownie.", "@couldNotUploadDocument": {}, "invalidCertificateOrMissingPassphrase": "Certyfikat jest nieprawidłowy lub braku<PERSON> has<PERSON>, spróbuj ponownie", "@invalidCertificateOrMissingPassphrase": {}, "couldNotLoadSavedViews": "Nie udało się załadować zapisanych widoków.", "@couldNotLoadSavedViews": {}, "aClientCertificateWasExpectedButNotSent": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>ego certyfikatu klienta. <PERSON><PERSON><PERSON> podać prawidłowy certyfikat.", "@aClientCertificateWasExpectedButNotSent": {}, "userIsNotAuthenticated": "Użytkownik nie jest uwierzytelniony.", "@userIsNotAuthenticated": {}, "requestTimedOut": "Przekroczono limit czasu żądania do serwera.", "@requestTimedOut": {}, "anErrorOccurredRemovingTheScans": "Wys<PERSON>ą<PERSON>ł błąd podczas usuwania skanów.", "@anErrorOccurredRemovingTheScans": {}, "couldNotReachYourPaperlessServer": "Nie można połączyć się z Twoim serwerem Paperless, czy jest uruchomiony i dostępny?", "@couldNotReachYourPaperlessServer": {}, "couldNotLoadSimilarDocuments": "Nie udało się wczytać podobnych dokumentów.", "@couldNotLoadSimilarDocuments": {}, "couldNotCreateStoragePath": "<PERSON>e udało się utworzyć ścieżki zapisu, spróbuj ponownie.", "@couldNotCreateStoragePath": {}, "couldNotLoadStoragePaths": "Nie udało się załadować ścieżek zapisu.", "@couldNotLoadStoragePaths": {}, "couldNotLoadSuggestions": "<PERSON>e udało się załadować sugestii.", "@couldNotLoadSuggestions": {}, "couldNotCreateTag": "<PERSON><PERSON> udało się utworzyć taga, spróbuj ponownie.", "@couldNotCreateTag": {}, "couldNotLoadTags": "<PERSON>e udało się załadować tagów.", "@couldNotLoadTags": {}, "anUnknownErrorOccurred": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł nieznany błąd.", "@anUnknownErrorOccurred": {}, "fileFormatNotSupported": "Ten format pliku nie jest obsługiwany.", "@fileFormatNotSupported": {}, "report": "ZGŁOSZENIE", "@report": {}, "absolute": "Bezwzględna", "@absolute": {}, "hintYouCanAlsoSpecifyRelativeValues": "Wskazówka: Poza konkretnymi datami, możesz również określić zakres czasowy w stosunku do bieżącej daty.", "@hintYouCanAlsoSpecifyRelativeValues": {"description": "Displayed in the extended date range picker"}, "amount": "<PERSON><PERSON><PERSON><PERSON>", "@amount": {}, "relative": "Względna", "@relative": {}, "last": "Ostatnie", "@last": {}, "timeUnit": "Jednostka czasu", "@timeUnit": {}, "selectDateRange": "<PERSON><PERSON><PERSON><PERSON> zak<PERSON> dat", "@selectDateRange": {}, "after": "Po", "@after": {}, "before": "<PERSON><PERSON><PERSON>", "@before": {}, "days": "{count, plural,  one{d<PERSON><PERSON>} other{dni}}", "@days": {"placeholders": {"count": {}}}, "lastNDays": "{count, plural, one{<PERSON><PERSON><PERSON><PERSON>} other{Ostatnie {count} dni}}", "@lastNDays": {"placeholders": {"count": {}}}, "lastNMonths": "{count, plural, one{<PERSON><PERSON><PERSON><PERSON>} other{Ostatnie {count} miesi<PERSON>ce}}", "@lastNMonths": {"placeholders": {"count": {}}}, "lastNWeeks": "{count, plural, one{Ostatni tydzień} other{Ostatnie {count} tygodnie}}", "@lastNWeeks": {"placeholders": {"count": {}}}, "lastNYears": "{count, plural, one {Osta<PERSON>ni rok} few {Ostatnie {count} lata} many {Ostatnie {count} lat} other {Ostatnie {count} lat}}", "@lastNYears": {"placeholders": {"count": {}}}, "months": "{count, plural,  one{miesiąc} other{miesiące}}", "@months": {"placeholders": {"count": {}}}, "weeks": "{count, plural, one{t<PERSON>zie<PERSON>} few {tygodnie} many {tygodnie} other{tygodnie}}", "@weeks": {"placeholders": {"count": {}}}, "years": "{count, plural, one{rok} few {lat} many {lat} other{lat}}", "@years": {"placeholders": {"count": {}}}, "gotIt": "OK!", "@gotIt": {}, "cancel": "<PERSON><PERSON><PERSON>", "@cancel": {}, "close": "Zamknij", "@close": {}, "create": "<PERSON><PERSON><PERSON>", "@create": {}, "delete": "Usuń", "@delete": {}, "edit": "<PERSON><PERSON><PERSON><PERSON>", "@edit": {}, "ok": "Ok", "@ok": {}, "save": "<PERSON><PERSON><PERSON><PERSON>", "@save": {}, "select": "<PERSON><PERSON><PERSON><PERSON>", "@select": {}, "saveChanges": "Zapisz zmiany", "@saveChanges": {}, "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@upload": {}, "youreOffline": "Je<PERSON>ś<PERSON> w trybie offline.", "@youreOffline": {}, "deleteDocument": "Usuń dokument", "@deleteDocument": {"description": "Used as an action label on each inbox item"}, "removeDocumentFromInbox": "Dokument usunięty ze skrzynki odbiorczej", "@removeDocumentFromInbox": {}, "areYouSureYouWantToMarkAllDocumentsAsSeen": "<PERSON>zy na pewno chcesz oznaczyć wszystkie dokumenty jako przeczytane? Spowoduje to edycję wszystkich dokumentów, usuwając znaczniki skrzynki odbiorczej. Ta akcja nie jest odwracalna! Czy na pewno chcesz kontynuować?", "@areYouSureYouWantToMarkAllDocumentsAsSeen": {}, "markAllAsSeen": "Oznaczyć wszystkie jako przeczytane?", "@markAllAsSeen": {}, "allSeen": "Wszystkie dokumenty zostały przeczytane", "@allSeen": {}, "markAsSeen": "Oznacz jako prz<PERSON>e", "@markAsSeen": {}, "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@refresh": {}, "youDoNotHaveUnseenDocuments": "<PERSON>e masz nieprzeczytanych dokumentów.", "@youDoNotHaveUnseenDocuments": {}, "quickAction": "Szybka akcja", "@quickAction": {}, "suggestionSuccessfullyApplied": "Pomyślnie zastosowano sugestię.", "@suggestionSuccessfullyApplied": {}, "today": "<PERSON><PERSON><PERSON><PERSON>", "@today": {}, "undo": "Cof<PERSON>j", "@undo": {}, "nUnseen": "{count} ni<PERSON><PERSON><PERSON><PERSON><PERSON>e", "@nUnseen": {"placeholders": {"count": {}}}, "swipeLeftToMarkADocumentAsSeen": "Wskazówka: Przesuń palcem w lewo, aby oznaczyć dokument jako przeczytany i usunąć wszystkie znaczniki skrzynki odbiorczej z dokumentu.", "@swipeLeftToMarkADocumentAsSeen": {}, "yesterday": "<PERSON><PERSON><PERSON><PERSON>", "@yesterday": {}, "anyAssigned": "Przypisano etykietę", "@anyAssigned": {}, "noItemsFound": "Nie znaleziono żadnych rekordów!", "@noItemsFound": {}, "caseIrrelevant": "Nie rozróżniaj wielkich i małych liter", "@caseIrrelevant": {}, "matchingAlgorithm": "Algorytm dopasowania", "@matchingAlgorithm": {}, "match": "Dopasowanie", "@match": {}, "name": "Nazwa", "@name": {}, "notAssigned": "<PERSON><PERSON>", "@notAssigned": {}, "addNewCorrespondent": "<PERSON><PERSON><PERSON> k<PERSON>", "@addNewCorrespondent": {}, "noCorrespondentsSetUp": "Nie masz żadnych ustawionych korespondentów.", "@noCorrespondentsSetUp": {}, "correspondents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@correspondents": {}, "addNewDocumentType": "Dodaj nowy rodzaj dokumentu", "@addNewDocumentType": {}, "noDocumentTypesSetUp": "<PERSON>e masz skonfigurowanych typów dokumentów.", "@noDocumentTypesSetUp": {}, "documentTypes": "Rodzaje dokumentów", "@documentTypes": {}, "addNewStoragePath": "Dodaj nową ścieżkę zapisu", "@addNewStoragePath": {}, "noStoragePathsSetUp": "Wygląda na to, że nie masz ustawionych ścieżek zapisu.", "@noStoragePathsSetUp": {}, "storagePaths": "Ścieżki zapisu", "@storagePaths": {}, "addNewTag": "<PERSON><PERSON><PERSON> nowy tag", "@addNewTag": {}, "noTagsSetUp": "Wygląda na to, że nie masz skonfigurowanych tagów.", "@noTagsSetUp": {}, "linkedDocuments": "Powiązane dokumenty", "@linkedDocuments": {}, "advancedSettings": "Ustawi<PERSON>e", "@advancedSettings": {}, "passphrase": "<PERSON><PERSON><PERSON>", "@passphrase": {}, "configureMutualTLSAuthentication": "Skonfiguruj wzajemne uwierzytelnianie TLS", "@configureMutualTLSAuthentication": {}, "invalidCertificateFormat": "Nieprawidłowy format certyfikatu, dozwolony jest tylko .pfx", "@invalidCertificateFormat": {}, "clientcertificate": "Certyfikat klienta", "@clientcertificate": {}, "selectFile": "Wybierz plik...", "@selectFile": {}, "continueLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@continueLabel": {}, "incorrectOrMissingCertificatePassphrase": "Błędne lub brakuj<PERSON>ce hasło certyfikatu.", "@incorrectOrMissingCertificatePassphrase": {}, "connect": "Polącz", "@connect": {}, "password": "<PERSON><PERSON><PERSON>", "@password": {}, "passwordMustNotBeEmpty": "<PERSON>ło nie może być puste.", "@passwordMustNotBeEmpty": {}, "connectionTimedOut": "Upłynął czas połączenia.", "@connectionTimedOut": {}, "loginPageReachabilityMissingClientCertificateText": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>ego certyfikatu klienta. <PERSON><PERSON><PERSON> podać prawidłowy certyfikat.", "@loginPageReachabilityMissingClientCertificateText": {}, "couldNotEstablishConnectionToTheServer": "Nie udało się nawiązać połączenia z serwerem.", "@couldNotEstablishConnectionToTheServer": {}, "connectionSuccessfulylEstablished": "Połączenie zostało ustanowione pomyślnie.", "@connectionSuccessfulylEstablished": {}, "hostCouldNotBeResolved": "Nie udało się ustalić hosta. Sprawdź adres serwera i połączenie z Internetem. ", "@hostCouldNotBeResolved": {}, "serverAddress": "<PERSON><PERSON> ser<PERSON>", "@serverAddress": {}, "invalidAddress": "Nieprawidłowy adres.", "@invalidAddress": {}, "serverAddressMustIncludeAScheme": "Adres serwera musi zawierać schemat.", "@serverAddressMustIncludeAScheme": {}, "serverAddressMustNotBeEmpty": "Adres serwera nie może być pusty.", "@serverAddressMustNotBeEmpty": {}, "signIn": "<PERSON><PERSON><PERSON><PERSON>", "@signIn": {}, "loginPageSignInTitle": "<PERSON><PERSON><PERSON><PERSON>", "@loginPageSignInTitle": {}, "signInToServer": "<PERSON><PERSON><PERSON><PERSON> si<PERSON> do {serverAddress}", "@signInToServer": {"placeholders": {"serverAddress": {}}}, "connectToPaperless": "Połącz z Paperless", "@connectToPaperless": {}, "username": "Nazwa użytkownika", "@username": {}, "usernameMustNotBeEmpty": "Nazwa użytkownika nie może być pusta.", "@usernameMustNotBeEmpty": {}, "documentContainsAllOfTheseWords": "Dokument zawiera wszystkie poniższe słowa", "@documentContainsAllOfTheseWords": {}, "all": "Wszystkie", "@all": {}, "documentContainsAnyOfTheseWords": "Dokument zawiera którekolwiek z poniższych słów", "@documentContainsAnyOfTheseWords": {}, "any": "Dowolny", "@any": {}, "learnMatchingAutomatically": "Ucz się automatycznie dopasowywania", "@learnMatchingAutomatically": {}, "auto": "Auto", "@auto": {}, "documentContainsThisString": "Dokładne: Dokument zawiera ten ciąg znaków", "@documentContainsThisString": {}, "exact": "Dokładne", "@exact": {}, "documentContainsAWordSimilarToThisWord": "Dokument zawiera słowo podobne do tego słowa", "@documentContainsAWordSimilarToThisWord": {}, "fuzzy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Fuzzy)", "@fuzzy": {}, "documentMatchesThisRegularExpression": "Dokument pasuje do wyrażenia regularnego", "@documentMatchesThisRegularExpression": {}, "regularExpression": "Wyrażenie regularne", "@regularExpression": {}, "anInternetConnectionCouldNotBeEstablished": "Nie można było <PERSON> połączenia internetowego.", "@anInternetConnectionCouldNotBeEstablished": {}, "done": "Wykonano", "@done": {}, "next": "Następne", "@next": {}, "couldNotAccessReceivedFile": "Nie można uzyskać dostępu do otrzymanego pliku. Spróbuj otworzyć aplikację przed udostępnieniem.", "@couldNotAccessReceivedFile": {}, "newView": "Nowy widok", "@newView": {}, "createsASavedViewBasedOnTheCurrentFilterCriteria": "Tworzy nowy widok oparty na aktualnych kryteriach filtrowania.", "@createsASavedViewBasedOnTheCurrentFilterCriteria": {}, "createViewsToQuicklyFilterYourDocuments": "Utwórz widoki by s<PERSON><PERSON><PERSON> dokumenty.", "@createViewsToQuicklyFilterYourDocuments": {}, "nFiltersSet": "{count, plural, one{{count} filtr ustawiony} few {{count} filtry ustawione} many {{count} filtry ustawione} other{{count} filtry ustawione}}", "@nFiltersSet": {"placeholders": {"count": {}}}, "showInSidebar": "Pokaż w panelu bocznym", "@showInSidebar": {}, "showOnDashboard": "Pokaż na pulpicie", "@showOnDashboard": {}, "views": "<PERSON><PERSON><PERSON>", "@views": {}, "clearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko", "@clearAll": {}, "scan": "<PERSON><PERSON><PERSON><PERSON>", "@scan": {}, "previewScan": "Podgląd", "@previewScan": {}, "scrollToTop": "Przewiń do góry", "@scrollToTop": {}, "paperlessServerVersion": "<PERSON><PERSON><PERSON> ser<PERSON>a Paperless", "@paperlessServerVersion": {}, "darkTheme": "Motyw ciemny", "@darkTheme": {}, "lightTheme": "<PERSON><PERSON><PERSON> jasny", "@lightTheme": {}, "systemTheme": "<PERSON>ż<PERSON>j motywu systemu", "@systemTheme": {}, "appearance": "Wygląd", "@appearance": {}, "languageAndVisualAppearance": "Język i wygląd", "@languageAndVisualAppearance": {}, "applicationSettings": "Aplikacja", "@applicationSettings": {}, "colorSchemeHint": "Wybierz między klasycznym schematem kolorów zainspirowanym tradycyjnym zielonym Paperless, lub uż<PERSON>j dynamicznego schematu kolorów na podstawie motywu systemu.", "@colorSchemeHint": {}, "colorSchemeNotSupportedWarning": "Dynamiczny motyw jest obsługiwany tylko dla urządzeń z systemem Android 12 i nowszym. Wybór opcji 'Dynamiczny' może nie mieć żadnego wpływu w zależności od implementacji systemu operacyjnego.", "@colorSchemeNotSupportedWarning": {}, "colors": "<PERSON><PERSON><PERSON>", "@colors": {}, "language": "Język", "@language": {}, "security": "Zabezpieczenia", "@security": {}, "mangeFilesAndStorageSpace": "Zarządzaj plikami i przestrzenią dyskową", "@mangeFilesAndStorageSpace": {}, "storage": "<PERSON><PERSON><PERSON><PERSON>", "@storage": {}, "dark": "Ciemny", "@dark": {}, "light": "<PERSON><PERSON><PERSON>", "@light": {}, "system": "System", "@system": {}, "ascending": "Rosnąco", "@ascending": {}, "descending": "Malejąco", "@descending": {}, "storagePathDay": "dzień", "@storagePathDay": {}, "storagePathMonth": "<PERSON><PERSON><PERSON><PERSON>", "@storagePathMonth": {}, "storagePathYear": "rok", "@storagePathYear": {}, "color": "<PERSON><PERSON>", "@color": {}, "filterTags": "Filtruj tagi...", "@filterTags": {}, "inboxTag": "Tag skrzynki odbiorczej", "@inboxTag": {}, "uploadInferValuesHint": "<PERSON><PERSON><PERSON> okre<PERSON><PERSON> warto<PERSON> dla tych pól, <PERSON><PERSON> instancja Paperless nie będzie automatycznie ustalać ich wartości. <PERSON><PERSON><PERSON>, aby te wartości były automatycznie wypełniane przez serwer, pozostaw puste pola.", "@uploadInferValuesHint": {}, "useTheConfiguredBiometricFactorToAuthenticate": "Użyj skonfigurowanego czynnika biometrycznego, aby uwierzytel<PERSON>ć i odblokować dokumenty.", "@useTheConfiguredBiometricFactorToAuthenticate": {}, "verifyYourIdentity": "Zweryfikuj swoją to<PERSON><PERSON>", "@verifyYourIdentity": {}, "verifyIdentity": "Zweryfiku<PERSON>", "@verifyIdentity": {}, "detailed": "Widok szczegółowy", "@detailed": {}, "grid": "Siatka", "@grid": {}, "list": "Lista", "@list": {}, "remove": "Usuń", "removeQueryFromSearchHistory": "<PERSON><PERSON><PERSON>ć zapytanie z historii wyszukiwania?", "dynamicColorScheme": "Dynamiczny", "@dynamicColorScheme": {}, "classicColorScheme": "Klasyczny", "@classicColorScheme": {}, "notificationDownloadComplete": "Pobieranie uk<PERSON>ńczone", "@notificationDownloadComplete": {"description": "Notification title when a download has been completed."}, "notificationDownloadingDocument": "Pobieranie dokumentu", "@notificationDownloadingDocument": {"description": "Notification title shown when a document download is pending"}, "archiveSerialNumberUpdated": "Numer ASN zmodyfikowany.", "@archiveSerialNumberUpdated": {"description": "Message shown when the ASN has been updated."}, "donateCoffee": "Kup mi kawę", "@donateCoffee": {"description": "Label displayed in the app drawer"}, "thisFieldIsRequired": "To pole jest wymagane!", "@thisFieldIsRequired": {"description": "Message shown below the form field when a required field has not been filled out."}, "confirm": "Potwierdź", "confirmAction": "Zatwierdź akcje", "@confirmAction": {"description": "Typically used as a title to confirm a previously selected action"}, "areYouSureYouWantToContinue": "<PERSON>zy na pewno chcesz kontynuować?", "bulkEditTagsAddMessage": "{count, plural, one{Ta operacja doda tagi {tags} do wybranego dokumentu.} few {Ta operacja doda tagi {tags} do {count} wybranych dokumentów.} many {Ta operacja doda tagi {tags} do {count} wybranych dokumentów.} other{Ta operacja doda tagi {tags} do {count} wybranych dokumentów.}}", "@bulkEditTagsAddMessage": {"description": "Message of the confirmation dialog when bulk adding tags."}, "bulkEditTagsRemoveMessage": "{count, plural, one{Ta operacja usunie tagi {tags} z wybranego dokumentu.}  other{Ta operacja usunie tagi {tags} z {count} wybranych dokumentów.}}", "@bulkEditTagsRemoveMessage": {"description": "Message of the confirmation dialog when bulk removing tags."}, "bulkEditTagsModifyMessage": "{count, plural, one{Ta operacja doda znaczniki {addTags} i usunie znaczniki {removeTags} z wybranego dokumentu.} other{Ta operacja doda tagi {addTags} i usunie tagi {removeTags} z {count} wybranych dokumentów.}}", "@bulkEditTagsModifyMessage": {"description": "Message of the confirmation dialog when both adding and removing tags."}, "bulkEditCorrespondentAssignMessage": "{count, plural, one{Ta operacja przypisze korespondenta {correspondent} do wybranego dokumentu.}  other{Ta operacja przypisze korespondenta {correspondent} do {count} wybranych dokumentów.}}", "bulkEditDocumentTypeAssignMessage": "{count, plural, one{Ta operacja przypisze typ dokumentu {docType} do wybranego dokumentu.}  other{Ta operacja przypisze typ dokumentu {docType} do {count} wybranych dokumentów.}}", "bulkEditStoragePathAssignMessage": "{count, plural, one{Ta operacja przypisze ścieżkę zapisu {path} do wybranego dokumentu.} other{Ta operacja przypisze ścieżkę zapisu {path} do {count} wybranych dokumentów.}}", "bulkEditCorrespondentRemoveMessage": "{count, plural, one{Ta operacja usunie korespondenta z wybranego dokumentu.}  other{Ta operacja usunie korespondenta z {count} wybranych dokumentów.}}", "bulkEditDocumentTypeRemoveMessage": "{count, plural, one{Ta operacja usunie typ dokumentu z wybranego dokumentu.}  other{Ta operacja usunie typ dokumentu z {count} wybranych dokumentów.}}", "bulkEditStoragePathRemoveMessage": "{count, plural, one{Ta operacja usunie tagi z wybranego dokumentu.}  other{Ta operacja usunie tagi z {count} wybranych dokumentów.}}", "anyTag": "Dowolny", "@anyTag": {"description": "Label shown when any tag should be filtered"}, "allTags": "Wszystkie", "@allTags": {"description": "Label shown when a document has to be assigned to all selected tags"}, "switchingAccountsPleaseWait": "Przełączanie kont. Proszę czekać...", "@switchingAccountsPleaseWait": {"description": "Message shown while switching accounts is in progress."}, "testConnection": "Test połączenia", "@testConnection": {"description": "Button label shown on login page. Allows user to test whether the server is reachable or not."}, "accounts": "Konta", "@accounts": {"description": "Title of the account management dialog"}, "addAccount": "<PERSON><PERSON><PERSON> k<PERSON>", "@addAccount": {"description": "Label of add account action"}, "switchAccount": "Przełącz", "@switchAccount": {"description": "Label for switch account action"}, "logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@logout": {"description": "Generic Logout label"}, "switchAccountTitle": "Zmień konto", "@switchAccountTitle": {"description": "Title of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "switchToNewAccount": "<PERSON><PERSON> chcesz przełączyć się na nowe konto? Możesz przełączyć się w dowolnym momencie.", "@switchToNewAccount": {"description": "Content of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "sourceCode": "Kod źródłowy", "findTheSourceCodeOn": "Znajdź kod źródłowy na", "@findTheSourceCodeOn": {"description": "Text before link to Paperless Mobile GitHub"}, "rememberDecision": "Zapamiętaj mój wybór", "defaultDownloadFileType": "Domyślny typ pliku do pobrania", "@defaultDownloadFileType": {"description": "Label indicating the default filetype to download (one of archived, original and always ask)"}, "defaultShareFileType": "Domyślny typ udostępnianego pliku", "@defaultShareFileType": {"description": "Label indicating the default filetype to share (one of archived, original and always ask)"}, "alwaysAsk": "<PERSON><PERSON><PERSON> p<PERSON>", "@alwaysAsk": {"description": "Option to choose when the app should always ask the user which filetype to use"}, "disableMatching": "Nie oznaczaj dokumentów automatycznie", "@disableMatching": {"description": "One of the options for automatic tagging of documents"}, "none": "Nie oznaczaj automatycznie", "@none": {"description": "One of available enum values of matching algorithm for tags"}, "logInToExistingAccount": "Zaloguj się do istniejącego konta", "@logInToExistingAccount": {"description": "Title shown on login page if at least one user is already known to the app."}, "print": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@print": {"description": "Tooltip for print button"}, "managePermissions": "Zarządzaj uprawnieniami", "@managePermissions": {"description": "Button which leads user to manage permissions page"}, "errorRetrievingServerVersion": "Wystąpił błąd w trakcie sprawdzania wersji serwera.", "@errorRetrievingServerVersion": {"description": "Message shown at the bottom of the settings page when the remote server version could not be resolved."}, "resolvingServerVersion": "Sprawd<PERSON><PERSON> wers<PERSON> serwera...", "@resolvingServerVersion": {"description": "Message shown while the app is loading the remote server version."}, "goToLogin": "Przejdź do logowania", "@goToLogin": {"description": "Label of the button shown on the login page to skip logging in to existing accounts and navigate user to login page"}, "export": "Eksport", "@export": {"description": "Label for button that exports scanned images to pdf (before upload)"}, "invalidFilenameCharacter": "Znaleziono niedozwolony znak w nazwie pliku", "@invalidFilenameCharacter": {"description": "For validating filename in export dialogue"}, "exportScansToPdf": "Export scans to PDF", "@exportScansToPdf": {"description": "title of the alert dialog when exporting scans to pdf"}, "allScansWillBeMerged": "All scans will be merged into a single PDF file.", "behavior": "Behavior", "@behavior": {"description": "Title of the settings concerning app beahvior"}, "theme": "Theme", "@theme": {"description": "Title of the theme mode setting"}, "clearCache": "Clear cache", "@clearCache": {"description": "Title of the clear cache setting"}, "freeBytes": "Free {byteString}", "@freeBytes": {"description": "Text shown for clear storage settings"}, "calculatingDots": "Calculating...", "@calculatingDots": {"description": "Text shown when the byte size is still being calculated"}, "freedDiskSpace": "Successfully freed {bytes} of disk space.", "@freedDiskSpace": {"description": "Message shown after clearing storage"}, "uploadScansAsPdf": "Upload scans as PDF", "@uploadScansAsPdf": {"description": "Title of the setting which toggles whether scans are always uploaded as pdf"}, "convertSinglePageScanToPdf": "Always convert single page scans to PDF before uploading", "@convertSinglePageScanToPdf": {"description": "description of the upload scans as pdf setting"}, "loginRequiredPermissionsHint": "Using Paperless Mobile requires a minimum set of user permissions since paperless-ngx 1.14.0 and higher. Therefore, please make sure that the user to be logged in has the permission to view other users (User → View) and the settings (UISettings → View). If you do not have these permissions, please contact an administrator of your paperless-ngx server.", "@loginRequiredPermissionsHint": {"description": "Hint shown on the login page informing the user of the required permissions to use the app."}, "missingPermissions": "You do not have the necessary permissions to perform this action.", "@missingPermissions": {"description": "Message shown in a snackbar when a user without the reequired permissions performs an action."}, "editView": "Edit View", "@editView": {"description": "Title of the edit saved view page"}, "donate": "", "@donate": {"description": "Label of the in-app donate button"}, "donationDialogContent": "Thank you for considering to support this app! Due to both Google's and Apple's Payment Policies, no links leading to donations may be displayed in-app. Not even linking to the project's repository page appears to be allowed in this context. Therefore, maybe have a look at the 'Donations' section in the project's README. Your support is much appreciated and keeps the development of this app alive. Thanks!", "@donationDialogContent": {"description": "Text displayed in the donation dialog"}, "noDocumentsFound": "No documents found.", "@noDocumentsFound": {"description": "Message shown when no documents were found."}, "couldNotDeleteCorrespondent": "Could not delete correspondent, please try again.", "@couldNotDeleteCorrespondent": {"description": "Message shown in snackbar when a correspondent could not be deleted."}, "couldNotDeleteDocumentType": "Could not delete document type, please try again.", "@couldNotDeleteDocumentType": {"description": "Message shown when a document type could not be deleted"}, "couldNotDeleteTag": "Could not delete tag, please try again.", "@couldNotDeleteTag": {"description": "Message shown when a tag could not be deleted"}, "couldNotDeleteStoragePath": "Could not delete storage path, please try again.", "@couldNotDeleteStoragePath": {"description": "Message shown when a storage path could not be deleted"}, "couldNotUpdateCorrespondent": "Could not update correspondent, please try again.", "@couldNotUpdateCorrespondent": {"description": "Message shown when a correspondent could not be updated"}, "couldNotUpdateDocumentType": "Could not update document type, please try again.", "@couldNotUpdateDocumentType": {"description": "Message shown when a document type could not be updated"}, "couldNotUpdateTag": "Could not update tag, please try again.", "@couldNotUpdateTag": {"description": "Message shown when a tag could not be updated"}, "couldNotLoadServerInformation": "Could not load server information.", "@couldNotLoadServerInformation": {"description": "Message shown when the server information could not be loaded"}, "couldNotLoadStatistics": "Could not load server statistics.", "@couldNotLoadStatistics": {"description": "Message shown when the server statistics could not be loaded"}, "couldNotLoadUISettings": "Could not load UI settings.", "@couldNotLoadUISettings": {"description": "Message shown when the UI settings could not be loaded"}, "couldNotLoadTasks": "Could not load tasks.", "@couldNotLoadTasks": {"description": "Message shown when the tasks (e.g. document consumed) could not be loaded"}, "userNotFound": "User could not be found.", "@userNotFound": {"description": "Message shown when the specified user (e.g. by id) could not be found"}, "couldNotUpdateSavedView": "Could not update saved view, please try again.", "@couldNotUpdateSavedView": {"description": "Message shown when a saved view could not be updated"}, "couldNotUpdateStoragePath": "Could not update storage path, please try again.", "savedViewSuccessfullyUpdated": "Saved view successfully updated.", "@savedViewSuccessfullyUpdated": {"description": "Message shown when a saved view was successfully updated."}, "discardChanges": "Discard changes?", "@discardChanges": {"description": "Title of the alert dialog shown when a user tries to close a view with unsaved changes."}, "savedViewChangedDialogContent": "The filter conditions of the active view have changed. By resetting the filter, these changes will be lost. Do you still wish to continue?", "@savedViewChangedDialogContent": {"description": "Content of the alert dialog shown when all of the following applies:\r\n* User has saved view selected\r\n* User has performed changes to the current document filter\r\n* User now tries to reset this filter without having saved the changes to the view."}, "createFromCurrentFilter": "Create from current filter", "@createFromCurrentFilter": {"description": "Tooltip of the \"New saved view\" button"}, "home": "Home", "@home": {"description": "Label of the \"Home\" route"}, "welcomeUser": "Welcome, {name}!", "@welcomeUser": {"description": "Top message shown on the home page"}, "statistics": "Statistics", "documentsInInbox": "Documents in inbox", "totalDocuments": "Total documents", "totalCharacters": "Total characters", "showAll": "Show all", "@showAll": {"description": "Button label shown on a saved view preview to open this view in the documents page"}, "userAlreadyExists": "This user already exists.", "@userAlreadyExists": {"description": "Error message shown when the user tries to add an already existing account."}, "youDidNotSaveAnyViewsYet": "You did not save any views yet, create one and it will be shown here.", "@youDidNotSaveAnyViewsYet": {"description": "Message shown when there are no saved views yet."}, "tryAgain": "Try again", "discardFile": "Discard file?", "discard": "Discard", "backToLogin": "Back to login", "skipEditingReceivedFiles": "Skip editing received files", "uploadWithoutPromptingUploadForm": "Always upload without prompting the upload form when sharing files with the app.", "authenticatingDots": "Authenticating...", "@authenticatingDots": {"description": "Message shown when the app is authenticating the user"}, "persistingUserInformation": "Persisting user information...", "fetchingUserInformation": "Fetching user information...", "@fetchingUserInformation": {"description": "Message shown when the app loads user data from the server"}, "restoringSession": "Restoring session...", "@restoringSession": {"description": "Message shown when the user opens the app and the previous user is tried to be authenticated and logged in"}, "documentsAssigned": "{count, plural, zero{No documents} one{1 document} other{{count} documents}}", "@documentsAssigned": {"description": "Text shown with a correspondent, document type etc. to indicate the number of documents this filter will maximally yield."}, "discardChangesWarning": "You have unsaved changes. By continuing, all changes will be lost. Do you want to discard these changes?", "@discardChangesWarning": {"description": "Warning message shown when the user tries to close a route without saving the changes."}, "changelog": "Changelog", "noLogsFoundOn": "No logs found on {date}.", "logfileBottomReached": "You have reached the bottom of this logfile.", "appLogs": "App logs {date}", "saveLogsToFile": "Save logs to file", "copyToClipboard": "Copy to clipboard", "couldNotLoadLogfileFrom": "Could not load logfile from {date}.", "loadingLogsFrom": "Loading logs from {date}...", "clearLogs": "Clear logs from {date}", "showPdf": "Show PDF", "@showPdf": {"description": "Tooltip shown on the \"show pdf\" button on the document edit page"}, "hidePdf": "Hide PDF", "@hidePdf": {"description": "Tooltip shown on the \"show pdf\" icon button on the document edit page"}, "misc": "Miscellaneous", "loggingOut": "Logging out...", "testingConnection": "Testing connection...", "@testingConnection": {"description": "Text shown while the app tries to establish a connection to the specified host."}, "version": "Version {versionCode}", "notes": "{count, plural, zero{Notes} one{Note} other{Notes}}", "addNote": "Add note", "newerVersionAvailable": "Newer version available:", "dateOutOfRange": "Date must be between {firstDate} and {lastDate}.", "@dateOutOfRange": {"description": "Error message shown when the user tries to select a date outside of the allowed range.", "placeholders": {"firstDate": {"type": "DateTime", "format": "yMd"}, "lastDate": {"type": "DateTime", "format": "yMd"}}}, "permissions": "Permissions", "newNote": "New note", "notesMarkdownSyntaxSupportHint": "Paperless Mobile can render notes using basic markdown syntax. Try it out!"}
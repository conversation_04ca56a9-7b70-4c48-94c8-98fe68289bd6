# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Android related files
android/key.properties

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
.vscode/
*.code-workspace

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Fastlane related
android/fastlane/report.xml

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# build_runner generated files


# mockito generated files
*.mocks.dart

# l10n generated files:
lib/generated/*
untranslated_messages.txt

#lakos generated files
**/dot_images/*

docker/

crowdin_credentials.yml

# Changelogs
assets/changelogs/*.md

# FVM Version Cache
.fvm/
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'client_certificate.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ClientCertificateAdapter extends TypeAdapter<ClientCertificate> {
  @override
  final int typeId = 5;

  @override
  ClientCertificate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ClientCertificate(
      bytes: fields[0] as Uint8List,
      filename: fields[2] == null ? 'cert.pfx' : fields[2] as String,
      passphrase: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ClientCertificate obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.bytes)
      ..writeByte(2)
      ..write(obj.filename)
      ..writeByte(1)
      ..write(obj.passphrase);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ClientCertificateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'open_directory_notification_response_payload.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OpenDirectoryNotificationResponsePayload
    _$OpenDirectoryNotificationResponsePayloadFromJson(
            Map<String, dynamic> json) =>
        OpenDirectoryNotificationResponsePayload(
          filePath: json['filePath'] as String,
          type: $enumDecodeNullable(
                  _$NotificationResponseOpenActionEnumMap, json['type']) ??
              NotificationResponseOpenAction.openDirectory,
        );

Map<String, dynamic> _$OpenDirectoryNotificationResponsePayloadToJson(
        OpenDirectoryNotificationResponsePayload instance) =>
    <String, dynamic>{
      'type': _$NotificationResponseOpenActionEnumMap[instance.type]!,
      'filePath': instance.filePath,
    };

const _$NotificationResponseOpenActionEnumMap = {
  NotificationResponseOpenAction.openDirectory: 'openDirectory',
};

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';

class OwnerShipPage extends StatefulWidget {
  const OwnerShipPage({super.key});

  @override
  State<OwnerShipPage> createState() => _OwnerShipPageState();
}

class _OwnerShipPageState extends State<OwnerShipPage> {
  int indexSelected = -1;
  final List<String> owners = ['Nobody', 'Admin', 'Super admin'];

  void selectOwner(int index) {
    setState(() {
      indexSelected = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title: const Row(
          children: [
            Text(
              'Owner',
              style: AppTextStyles.textStyleAppBar,
            )
          ],
        ),
      ),
      body: Padding(
          padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
          child: ListView.separated(
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () => selectOwner(index),
                  child: Container(
                    height: 46,
                    width: size.width,
                    padding: const EdgeInsets.symmetric(horizontal: 14),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: indexSelected == index
                            ? AppColor.blue_024CAA.withOpacity(0.1)
                            : AppColor.white),
                    child: Row(
                      children: [
                        Text(
                          owners[index],
                          style: AppTextStyles.textStyle14
                              .copyWith(fontWeight: FontWeight.w500),
                        ),
                        const Spacer(),
                        if (indexSelected == index)
                          SvgPicture.asset('assets/svgs/tick.svg')
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) => const Gap(16),
              itemCount: owners.length)),
    );
  }
}

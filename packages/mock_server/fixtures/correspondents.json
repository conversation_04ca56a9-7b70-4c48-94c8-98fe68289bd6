{"count": 27, "next": "http://localhost:8000/api/correspondents/?page=2", "previous": null, "results": [{"id": 9, "slug": "abc-test-correspondent", "name": "ABC Test Correspondent", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 13, "slug": "corresp-10", "name": "Corresp 10", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 14, "slug": "corresp-11", "name": "Corresp 11", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 15, "slug": "corresp-12", "name": "Corresp 12", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 16, "slug": "corresp-13", "name": "Corresp 13", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 18, "slug": "corresp-15", "name": "Corresp 15", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 19, "slug": "corresp-16", "name": "Corresp 16", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 20, "slug": "corresp-17", "name": "Corresp 17", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 21, "slug": "corresp-18", "name": "Corresp 18", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 22, "slug": "corresp-19", "name": "Corresp 19", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 23, "slug": "corresp-20", "name": "Corresp 20", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 24, "slug": "corresp-21", "name": "Corresp 21", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 25, "slug": "corresp-22", "name": "Corresp 22", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 26, "slug": "corresp-23", "name": "Corresp 23", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 5, "slug": "corresp-3", "name": "Corresp 3", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 6, "slug": "corresp-4", "name": "Corresp 4", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 7, "slug": "corresp-5", "name": "Corresp 5", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 8, "slug": "corresp-6", "name": "Corresp 6", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 10, "slug": "corresp-7", "name": "Corresp 7", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 11, "slug": "corresp-8", "name": "Corresp 8", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 12, "slug": "corresp-9", "name": "Corresp 9", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 17, "slug": "correspondent-14", "name": "Correspondent 14", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 0, "last_correspondence": null}, {"id": 2, "slug": "correspondent-2", "name": "Correspondent 2", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 7, "last_correspondence": "2021-01-20T23:37:58.204614Z"}, {"id": 27, "slug": "correspondent-slug", "name": "Correspond<PERSON> Slug", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 1, "last_correspondence": "2022-03-16T03:48:50.089624Z"}, {"id": 4, "slug": "newest-correspondent", "name": "Newest Correspondent", "match": "", "matching_algorithm": 1, "is_insensitive": true, "document_count": 1, "last_correspondence": "2021-02-07T08:00:00Z"}]}
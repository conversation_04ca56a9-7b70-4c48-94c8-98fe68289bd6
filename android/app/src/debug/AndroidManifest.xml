<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="de.astubenbord.paperless_mobile">
    <application 
        android:requestLegacyExternalStorage="true"/>
    <!-- Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29"/>
    <!-- <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> -->
</manifest>

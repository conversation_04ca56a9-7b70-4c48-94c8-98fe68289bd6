// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'changelog_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $changelogRoute,
    ];

RouteBase get $changelogRoute => GoRouteData.$route(
      path: '/changelogs',
      parentNavigatorKey: ChangelogRoute.$parentNavigatorKey,
      factory: $ChangelogRouteExtension._fromState,
    );

extension $ChangelogRouteExtension on ChangelogRoute {
  static ChangelogRoute _fromState(GoRouterState state) => ChangelogRoute();

  String get location => GoRouteData.$location(
        '/changelogs',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

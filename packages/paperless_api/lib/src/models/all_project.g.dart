// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'all_project.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$AllProjectCWProxy {
  AllProject count(int count);

  AllProject next(dynamic next);

  AllProject previous(dynamic previous);

  AllProject all(List<int> all);

  AllProject results(List<Project> results);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AllProject(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AllProject(...).copyWith(id: 12, name: "My name")
  /// ````
  AllProject call({
    int? count,
    dynamic next,
    dynamic previous,
    List<int>? all,
    List<Project>? results,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfAllProject.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfAllProject.copyWith.fieldName(...)`
class _$AllProjectCWProxyImpl implements _$AllProjectCWProxy {
  const _$AllProjectCWProxyImpl(this._value);

  final AllProject _value;

  @override
  AllProject count(int count) => this(count: count);

  @override
  AllProject next(dynamic next) => this(next: next);

  @override
  AllProject previous(dynamic previous) => this(previous: previous);

  @override
  AllProject all(List<int> all) => this(all: all);

  @override
  AllProject results(List<Project> results) => this(results: results);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `AllProject(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// AllProject(...).copyWith(id: 12, name: "My name")
  /// ````
  AllProject call({
    Object? count = const $CopyWithPlaceholder(),
    Object? next = const $CopyWithPlaceholder(),
    Object? previous = const $CopyWithPlaceholder(),
    Object? all = const $CopyWithPlaceholder(),
    Object? results = const $CopyWithPlaceholder(),
  }) {
    return AllProject(
      count: count == const $CopyWithPlaceholder() || count == null
          ? _value.count
          // ignore: cast_nullable_to_non_nullable
          : count as int,
      next: next == const $CopyWithPlaceholder() || next == null
          ? _value.next
          // ignore: cast_nullable_to_non_nullable
          : next as dynamic,
      previous: previous == const $CopyWithPlaceholder() || previous == null
          ? _value.previous
          // ignore: cast_nullable_to_non_nullable
          : previous as dynamic,
      all: all == const $CopyWithPlaceholder() || all == null
          ? _value.all
          // ignore: cast_nullable_to_non_nullable
          : all as List<int>,
      results: results == const $CopyWithPlaceholder() || results == null
          ? _value.results
          // ignore: cast_nullable_to_non_nullable
          : results as List<Project>,
    );
  }
}

extension $AllProjectCopyWith on AllProject {
  /// Returns a callable class that can be used as follows: `instanceOfAllProject.copyWith(...)` or like so:`instanceOfAllProject.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$AllProjectCWProxy get copyWith => _$AllProjectCWProxyImpl(this);
}

abstract class _$ProjectCWProxy {
  Project id(int id);

  Project slug(String slug);

  Project name(String name);

  Project match(String match);

  Project matchingAlgorithm(int matchingAlgorithm);

  Project isInsensitive(bool isInsensitive);

  Project documentCount(int documentCount);

  Project owner(dynamic owner);

  Project userCanChange(bool userCanChange);

  Project isSelected(bool isSelected);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Project(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Project(...).copyWith(id: 12, name: "My name")
  /// ````
  Project call({
    int? id,
    String? slug,
    String? name,
    String? match,
    int? matchingAlgorithm,
    bool? isInsensitive,
    int? documentCount,
    dynamic owner,
    bool? userCanChange,
    bool? isSelected,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfProject.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfProject.copyWith.fieldName(...)`
class _$ProjectCWProxyImpl implements _$ProjectCWProxy {
  const _$ProjectCWProxyImpl(this._value);

  final Project _value;

  @override
  Project id(int id) => this(id: id);

  @override
  Project slug(String slug) => this(slug: slug);

  @override
  Project name(String name) => this(name: name);

  @override
  Project match(String match) => this(match: match);

  @override
  Project matchingAlgorithm(int matchingAlgorithm) =>
      this(matchingAlgorithm: matchingAlgorithm);

  @override
  Project isInsensitive(bool isInsensitive) =>
      this(isInsensitive: isInsensitive);

  @override
  Project documentCount(int documentCount) =>
      this(documentCount: documentCount);

  @override
  Project owner(dynamic owner) => this(owner: owner);

  @override
  Project userCanChange(bool userCanChange) =>
      this(userCanChange: userCanChange);

  @override
  Project isSelected(bool isSelected) => this(isSelected: isSelected);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `Project(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// Project(...).copyWith(id: 12, name: "My name")
  /// ````
  Project call({
    Object? id = const $CopyWithPlaceholder(),
    Object? slug = const $CopyWithPlaceholder(),
    Object? name = const $CopyWithPlaceholder(),
    Object? match = const $CopyWithPlaceholder(),
    Object? matchingAlgorithm = const $CopyWithPlaceholder(),
    Object? isInsensitive = const $CopyWithPlaceholder(),
    Object? documentCount = const $CopyWithPlaceholder(),
    Object? owner = const $CopyWithPlaceholder(),
    Object? userCanChange = const $CopyWithPlaceholder(),
    Object? isSelected = const $CopyWithPlaceholder(),
  }) {
    return Project(
      id: id == const $CopyWithPlaceholder() || id == null
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as int,
      slug: slug == const $CopyWithPlaceholder() || slug == null
          ? _value.slug
          // ignore: cast_nullable_to_non_nullable
          : slug as String,
      name: name == const $CopyWithPlaceholder() || name == null
          ? _value.name
          // ignore: cast_nullable_to_non_nullable
          : name as String,
      match: match == const $CopyWithPlaceholder() || match == null
          ? _value.match
          // ignore: cast_nullable_to_non_nullable
          : match as String,
      matchingAlgorithm: matchingAlgorithm == const $CopyWithPlaceholder() ||
              matchingAlgorithm == null
          ? _value.matchingAlgorithm
          // ignore: cast_nullable_to_non_nullable
          : matchingAlgorithm as int,
      isInsensitive:
          isInsensitive == const $CopyWithPlaceholder() || isInsensitive == null
              ? _value.isInsensitive
              // ignore: cast_nullable_to_non_nullable
              : isInsensitive as bool,
      documentCount:
          documentCount == const $CopyWithPlaceholder() || documentCount == null
              ? _value.documentCount
              // ignore: cast_nullable_to_non_nullable
              : documentCount as int,
      owner: owner == const $CopyWithPlaceholder() || owner == null
          ? _value.owner
          // ignore: cast_nullable_to_non_nullable
          : owner as dynamic,
      userCanChange:
          userCanChange == const $CopyWithPlaceholder() || userCanChange == null
              ? _value.userCanChange
              // ignore: cast_nullable_to_non_nullable
              : userCanChange as bool,
      isSelected:
          isSelected == const $CopyWithPlaceholder() || isSelected == null
              ? _value.isSelected
              // ignore: cast_nullable_to_non_nullable
              : isSelected as bool,
    );
  }
}

extension $ProjectCopyWith on Project {
  /// Returns a callable class that can be used as follows: `instanceOfProject.copyWith(...)` or like so:`instanceOfProject.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$ProjectCWProxy get copyWith => _$ProjectCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AllProject _$AllProjectFromJson(Map<String, dynamic> json) => AllProject(
      count: (json['count'] as num).toInt(),
      next: json['next'],
      previous: json['previous'],
      all: (json['all'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      results: (json['results'] as List<dynamic>)
          .map((e) => Project.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$AllProjectToJson(AllProject instance) =>
    <String, dynamic>{
      'count': instance.count,
      'next': instance.next,
      'previous': instance.previous,
      'all': instance.all,
      'results': instance.results,
    };

Project _$ProjectFromJson(Map<String, dynamic> json) => Project(
      id: (json['id'] as num).toInt(),
      slug: json['slug'] as String,
      name: json['name'] as String,
      match: json['match'] as String,
      matchingAlgorithm: (json['matching_algorithm'] as num).toInt(),
      isInsensitive: json['is_insensitive'] as bool,
      documentCount: (json['document_count'] as num).toInt(),
      owner: json['owner'],
      userCanChange: json['user_can_change'] as bool,
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$ProjectToJson(Project instance) => <String, dynamic>{
      'id': instance.id,
      'slug': instance.slug,
      'name': instance.name,
      'match': instance.match,
      'matching_algorithm': instance.matchingAlgorithm,
      'is_insensitive': instance.isInsensitive,
      'document_count': instance.documentCount,
      'owner': instance.owner,
      'user_can_change': instance.userCanChange,
      'isSelected': instance.isSelected,
    };

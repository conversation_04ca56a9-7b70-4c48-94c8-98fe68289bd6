import 'package:bloc/bloc.dart';

import 'package:paperless_mobile/features/users/cubit/user_state.dart';
import 'package:paperless_api/src/modules/user_api/user_api_impl.dart';




class UserCubit extends Cubit<UserState> {
  final UserApiImpl api;

  UserCubit({ required this.api}) : super(UserState());

  Future<void> getStorage()async{
  final storage = await api.getStorage();
  emit(state.copyWith(storage: storage));
  }
  
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/repository/label_repository.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/core/widgets/form_builder_fields/extended_date_range_form_field/form_builder_extended_date_range_picker.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/department_page.dart';
import 'package:paperless_mobile/features/document_upload/view/permissions_page.dart';
import 'package:paperless_mobile/features/document_upload/view/project_page.dart';
import 'package:paperless_mobile/features/document_upload/view/select_project_page.dart';
import 'package:paperless_mobile/features/document_upload/view/users_page.dart';
import 'package:paperless_mobile/features/labels/tags/view/widgets/tags_form_field.dart';
import 'package:paperless_mobile/features/labels/view/widgets/label_form_field.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

import 'text_query_form_field.dart';

class DocumentFilterForm extends StatefulWidget {
  static const fkCorrespondent = DocumentModel.correspondentKey;
  static const fkDocumentType = DocumentModel.documentTypeKey;
  static const fkStoragePath = DocumentModel.storagePathKey;
  static const fkQuery = "query";
  static const fkCreatedAt = DocumentModel.createdKey;
  static const fkAddedAt = DocumentModel.addedKey;

  static DocumentFilter assembleFilter(
    GlobalKey<FormBuilderState> formKey,
    DocumentFilter initialFilter,
  ) {
    formKey.currentState?.save();
    final v = formKey.currentState!.value;
    return initialFilter.copyWith(
      correspondent:
          v[DocumentFilterForm.fkCorrespondent] as IdQueryParameter? ??
              DocumentFilter.initial.correspondent,
      documentType: v[DocumentFilterForm.fkDocumentType] as IdQueryParameter? ??
          DocumentFilter.initial.documentType,
      storagePath: v[DocumentFilterForm.fkStoragePath] as IdQueryParameter? ??
          DocumentFilter.initial.storagePath,
      tags:
          v[DocumentModel.tagsKey] as TagsQuery? ?? DocumentFilter.initial.tags,
      query: v[DocumentFilterForm.fkQuery] as TextQuery? ??
          DocumentFilter.initial.query,
      created: (v[DocumentFilterForm.fkCreatedAt] as DateRangeQuery),
      added: (v[DocumentFilterForm.fkAddedAt] as DateRangeQuery),
      page: 1,
    );
  }

  final Widget? header;
  final GlobalKey<FormBuilderState> formKey;
  final DocumentFilter initialFilter;
  final ScrollController? scrollController;
  final EdgeInsets padding;
  final DocumentUploadCubit documentUploadCubit;

  const DocumentFilterForm({
    super.key,
    this.header,
    required this.documentUploadCubit,
    required this.formKey,
    required this.initialFilter,
    this.scrollController,
    this.padding = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
  });

  @override
  State<DocumentFilterForm> createState() => _DocumentFilterFormState();
}

class _DocumentFilterFormState extends State<DocumentFilterForm> {
  late bool _allowOnlyExtendedQuery;
  late TextQuery _currentQuery;

  @override
  void initState() {
    super.initState();
    _allowOnlyExtendedQuery = widget.initialFilter.forceExtendedQuery;
    _currentQuery =
        const TextQuery(queryText: '', queryType: QueryType.titleAndContent);
  }

  @override
  Widget build(BuildContext context) {
    final labelRepository = context.watch<LabelRepository>();
    return FormBuilder(
      key: widget.formKey,
      onChanged: () {
        final newQuery = widget.formKey.currentState
            ?.fields[DocumentFilterForm.fkQuery]?.value as TextQuery?;
        if (newQuery != null && newQuery != _currentQuery) {
          setState(() {
            _currentQuery = newQuery;
          });
        }
      },
      child: CustomScrollView(
        controller: widget.scrollController,
        slivers: [
          if (widget.header != null) widget.header!,
          ..._buildFormFieldList(labelRepository),
        ],
      ),
    );
  }

  List<Widget> _buildFormFieldList(LabelRepository labelRepository) {
    return [
      _buildQueryFormField().paddedSymmetrically(horizontal: 12),
      Align(
        alignment: Alignment.centerLeft,
        child: Text(
          S.of(context)!.advanced.toUpperCase(),
          style:
              AppTextStyles.textStyle14.copyWith(color: AppColor.grey_909090),
        ),
      ).paddedLTRB(12, 16, 12, 10),
      Text(S.of(context)!.createdAt,
              style: AppTextStyles.textStyle14
                  .copyWith(fontWeight: FontWeight.w600))
          .paddedOnly(left: 12, top: 10),
      FormBuilderExtendedDateRangePicker(
        name: DocumentFilterForm.fkCreatedAt,
        initialValue: widget.initialFilter.created,
        labelText: 'MM/DD/YYYY',
        suffixIcon: Padding(
          padding: const EdgeInsets.all(12.0),
          child: SvgPicture.asset('assets/svgs/calendar.svg'),
        ),
        onChanged: (_) {
          _checkQueryConstraints();
        },
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      ),
      Text(S.of(context)!.addedAt,
              style: AppTextStyles.textStyle14
                  .copyWith(fontWeight: FontWeight.w600))
          .paddedOnly(left: 12, top: 10),
      FormBuilderExtendedDateRangePicker(
        name: DocumentFilterForm.fkAddedAt,
        initialValue: widget.initialFilter.added,
        labelText: 'MM/DD/YYYY',
        suffixIcon: Padding(
          padding: const EdgeInsets.all(12.0),
          child: SvgPicture.asset('assets/svgs/calendar.svg'),
        ),
        onChanged: (_) {
          _checkQueryConstraints();
        },
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      ),
      _buildCorrespondentFormField(labelRepository.correspondents)
          .paddedSymmetrically(
        horizontal: 12,
        vertical: 4,
      ),
      _buildDocumentTypeFormField(labelRepository.documentTypes)
          .paddedSymmetrically(
        horizontal: 12,
        vertical: 4,
      ),
      _buildStoragePathFormField(labelRepository.storagePaths)
          .paddedSymmetrically(
        horizontal: 12,
        vertical: 4,
      ),
      _buildTagsFormField(labelRepository.tags).paddedSymmetrically(
        horizontal: 12,
        vertical: 4,
      ),
      const Text(
        'Project',
        style: AppTextStyles.textStyleBold14,
      ).paddedOnly(left: 12),
      const Gap(8),
      GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ProjectPage(
                    documentUploadCubit: widget.documentUploadCubit),
              ));
        },
        child: Container(
          height: 46,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
              color: AppColor.white, borderRadius: BorderRadius.circular(10)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Select project',
                style: AppTextStyles.textStyle14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColor.black_3C3C43.withOpacity(0.6)),
              ),
              const Icon(Icons.chevron_right)
            ],
          ),
        ).paddedOnly(left: 12, right: 12),
      ),
      const Gap(24),
      const Text(
        'Permissions',
        style: AppTextStyles.textStyleBold14,
      ).paddedOnly(left: 12),
      const Gap(8),
      GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DepartmentPage(
                    type: UserType.view,
                    documentUploadCubit: widget.documentUploadCubit),
              ));
        },
        child: Container(
          height: 46,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
              color: AppColor.white, borderRadius: BorderRadius.circular(10)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Select project',
                style: AppTextStyles.textStyle14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColor.black_3C3C43.withOpacity(0.6)),
              ),
              const Icon(Icons.chevron_right)
            ],
          ),
        ).paddedOnly(left: 12, right: 12),
      ),
      const Gap(30)
    ].map((e) => SliverToBoxAdapter(child: e)).toList();
  }

  void _checkQueryConstraints() {
    final filter =
        DocumentFilterForm.assembleFilter(widget.formKey, widget.initialFilter);
    if (filter.forceExtendedQuery) {
      setState(() => _allowOnlyExtendedQuery = true);
      final queryField =
          widget.formKey.currentState?.fields[DocumentFilterForm.fkQuery];
      queryField?.didChange(
        (queryField.value as TextQuery?)
            ?.copyWith(queryType: QueryType.extended),
      );
    } else {
      setState(() => _allowOnlyExtendedQuery = false);
    }
  }

  Widget _buildDocumentTypeFormField(Map<int, DocumentType> documentTypes) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.documentType,
          style:
              AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
        ).paddedOnly(top: 10, bottom: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
          ),
          child: LabelFormField<DocumentType>(
            titleAppBar: S.of(context)!.documentType,
            name: DocumentFilterForm.fkDocumentType,
            options: documentTypes,
            labelText: S.of(context)!.documentType,
            initialValue: widget.initialFilter.documentType,
            prefixIcon: const Icon(Icons.description_outlined),
            allowSelectUnassigned: false,
            canCreateNewLabel: context
                .watch<LocalUserAccount>()
                .paperlessUser
                .canCreateDocumentTypes,
          ),
        ),
      ],
    );
  }

  Widget _buildCorrespondentFormField(Map<int, Correspondent> correspondents) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.correspondent,
          style:
              AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
        ).paddedOnly(top: 10, bottom: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
          ),
          child: LabelFormField<Correspondent>(
            titleAppBar: S.of(context)!.correspondent,
            name: DocumentFilterForm.fkCorrespondent,
            options: correspondents,
            labelText: S.of(context)!.correspondent,
            initialValue: widget.initialFilter.correspondent,
            prefixIcon: const Icon(Icons.person_outline),
            allowSelectUnassigned: false,
            canCreateNewLabel: context
                .watch<LocalUserAccount>()
                .paperlessUser
                .canCreateCorrespondents,
          ),
        ),
      ],
    );
  }

  Widget _buildStoragePathFormField(Map<int, StoragePath> storagePaths) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.storagePath,
          style:
              AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
        ).paddedOnly(top: 10, bottom: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
          ),
          child: LabelFormField<StoragePath>(
            titleAppBar: S.of(context)!.storagePath,
            name: DocumentFilterForm.fkStoragePath,
            options: storagePaths,
            labelText: S.of(context)!.storagePath,
            initialValue: widget.initialFilter.storagePath,
            prefixIcon: const Icon(Icons.folder_outlined),
            allowSelectUnassigned: false,
            canCreateNewLabel: context
                .watch<LocalUserAccount>()
                .paperlessUser
                .canCreateStoragePaths,
          ),
        ),
      ],
    );
  }

  Widget _buildQueryFormField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(getTitle(_currentQuery.queryType),
            style: AppTextStyles.textStyle14
                .copyWith(fontWeight: FontWeight.w600)),
        const Gap(8),
        TextQueryFormField(
          name: DocumentFilterForm.fkQuery,
          hinText: S.of(context)!.search,
          onlyExtendedQueryAllowed: _allowOnlyExtendedQuery,
          initialValue: _currentQuery,
        ),
      ],
    );
  }

  String getTitle(QueryType currentQuery) {
    if (currentQuery == QueryType.titleAndContent) {
      return S.of(context)!.titleAndContent;
    } else if (currentQuery == QueryType.title) {
      return S.of(context)!.title;
    } else {
      return S.of(context)!.extended;
    }
  }

  Widget _buildTagsFormField(Map<int, Tag> tags) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.of(context)!.tags,
          style:
              AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
        ).paddedOnly(top: 10, bottom: 8),
        TagsFormField(
          name: DocumentModel.tagsKey,
          initialValue: widget.initialFilter.tags,
          options: tags,
          allowExclude: false,
          allowOnlySelection: false,
          allowCreation: false,
        ),
        const Gap(30)
      ],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/department_page.dart';
import 'package:paperless_mobile/features/document_upload/view/owner_ship_page.dart';
import 'package:paperless_mobile/features/document_upload/view/users_page.dart';

class PermissionsPage extends StatelessWidget {
  const PermissionsPage({super.key, required this.documentUploadCubit});
  final DocumentUploadCubit documentUploadCubit;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Scaffold(
        appBar: AppBar(
          iconTheme: const IconThemeData(color: AppColor.primary),
          title: const Row(
            children: [
              Text(
                'Permission',
                style: AppTextStyles.textStyleAppBar,
              )
            ],
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16, top: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'OWNERSHIP',
                style: AppTextStyles.textStyleBold14
                    .copyWith(color: AppColor.grey_909090),
              ),
              const Gap(8),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const OwnerShipPage(),
                      ));
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  height: 46,
                  width: size.width,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: AppColor.white),
                  child: Row(
                    children: [
                      Text('Owner',
                          style: AppTextStyles.textStyle14
                              .copyWith(fontWeight: FontWeight.w500)),
                      const Spacer(),
                      Text('Admin',
                          style: AppTextStyles.textStyle14.copyWith(
                              fontWeight: FontWeight.w500,
                              color: AppColor.black_3C3C43.withOpacity(0.6))),
                      Icon(
                        Icons.chevron_right,
                        color: AppColor.black_3C3C43.withOpacity(0.3),
                      ),
                    ],
                  ),
                ),
              ),
              const Gap(8),
              Text(
                'Objects without an owner can be viewed and edited by all users.',
                style: AppTextStyles.textStyle14
                    .copyWith(color: AppColor.grey_909090),
              ),
              const Gap(32),
              Text(
                'VIEWERS',
                style: AppTextStyles.textStyleBold14
                    .copyWith(color: AppColor.grey_909090),
              ),
              const Gap(8),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => UsersPage(
                          documentUploadCubit: documentUploadCubit,
                          type: UserType.view,
                        ),
                      ));
                },
                child: Container(
                  height: 46,
                  width: size.width,
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  decoration: BoxDecoration(
                      color: AppColor.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                      border: Border(
                          bottom: BorderSide(
                              color: AppColor.black_3C3C43.withOpacity(0.36)))),
                  child: Row(
                    children: [
                      Text('Users',
                          style: AppTextStyles.textStyle14
                              .copyWith(fontWeight: FontWeight.w500)),
                      const Spacer(),
                      Icon(
                        Icons.chevron_right,
                        color: AppColor.black_3C3C43.withOpacity(0.3),
                      ),
                    ],
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DepartmentPage(
                            documentUploadCubit: documentUploadCubit, type:  UserType.view,),
                      ));
                },
                child: Container(
                  height: 46,
                  width: size.width,
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  decoration: const BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    ),
                    // border: Border(
                    //   bottom: BorderSide(color: AppColor.grey_909090),
                    // ),
                  ),
                  child: Row(
                    children: [
                      Text('Departments',
                          style: AppTextStyles.textStyle14
                              .copyWith(fontWeight: FontWeight.w500)),
                      const Spacer(),
                      Icon(
                        Icons.chevron_right,
                        color: AppColor.black_3C3C43.withOpacity(0.3),
                      ),
                    ],
                  ),
                ),
              ),
              const Gap(32),
              Text(
                'EDITORS',
                style: AppTextStyles.textStyleBold14
                    .copyWith(color: AppColor.grey_909090),
              ),
              const Gap(8),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => UsersPage(
                          documentUploadCubit: documentUploadCubit,
                          type: UserType.edit,
                        ),
                      ));
                },
                child: Container(
                  height: 46,
                  width: size.width,
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  decoration: BoxDecoration(
                      color: AppColor.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                      border: Border(
                          bottom: BorderSide(
                              color: AppColor.black_3C3C43.withOpacity(0.36)))),
                  child: Row(
                    children: [
                      Text('Users',
                          style: AppTextStyles.textStyle14
                              .copyWith(fontWeight: FontWeight.w500)),
                      const Spacer(),
                      Icon(
                        Icons.chevron_right,
                        color: AppColor.black_3C3C43.withOpacity(0.3),
                      ),
                    ],
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                            Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DepartmentPage(
                            documentUploadCubit: documentUploadCubit, type:  UserType.edit,),
                      ));
                },
                child: Container(
                  height: 46,
                  width: size.width,
                  padding: const EdgeInsets.symmetric(horizontal: 14),
                  decoration: const BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    ),
                    // border: Border(
                    //   bottom: BorderSide(color: AppColor.grey_909090),
                    // ),
                  ),
                  child: Row(
                    children: [
                      Text('Departments',
                          style: AppTextStyles.textStyle14
                              .copyWith(fontWeight: FontWeight.w500)),
                      const Spacer(),
                      Icon(
                        Icons.chevron_right,
                        color: AppColor.black_3C3C43.withOpacity(0.3),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}

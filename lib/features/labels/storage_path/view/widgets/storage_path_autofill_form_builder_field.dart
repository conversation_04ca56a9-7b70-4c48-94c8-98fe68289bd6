import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';

import 'package:paperless_mobile/core/workarounds/colored_chip.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class StoragePathAutofillFormBuilderField extends StatefulWidget {
  final String name;
  final String? initialValue;
  const StoragePathAutofillFormBuilderField({
    super.key,
    required this.name,
    this.initialValue,
  });

  @override
  State<StoragePathAutofillFormBuilderField> createState() =>
      _StoragePathAutofillFormBuilderFieldState();
}

class _StoragePathAutofillFormBuilderFieldState
    extends State<StoragePathAutofillFormBuilderField> {
  late final TextEditingController _textEditingController;

  late bool _showClearIcon;
  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController.fromValue(
      TextEditingValue(text: widget.initialValue ?? ''),
    )..addListener(() {
        setState(() {
          _showClearIcon = _textEditingController.text.isNotEmpty;
        });
      });
    _showClearIcon = widget.initialValue?.isNotEmpty ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return FormBuilderField<String>(
      name: widget.name,
      initialValue: widget.initialValue ?? '',
      builder: (field) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context)!.storagePath,
            style:
                AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
          ),
          TextFormField(
            controller: _textEditingController,
            validator: (value) {
              if (value?.trim().isEmpty ?? true) {
                return S.of(context)!.thisFieldIsRequired;
              }
              return null;
            },
            decoration: InputDecoration(
                fillColor: AppColor.white,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide.none,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 0, horizontal: 14),
                hintText: 'Enter here',
                hintStyle:
                    const TextStyle(color: AppColor.grey_BABABA, fontSize: 14),
                suffixIcon: _showClearIcon
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () => _resetfield(field),
                      )
                    : null),
            onChanged: field.didChange,
          ),
          const SizedBox(height: 8.0),
          Text(
            "Select to autofill path variable",
            style:
                AppTextStyles.textStyle12.copyWith(color: AppColor.grey_909090),
          ),
          const Gap(4),
          ColoredChipWrapper(
            child: Wrap(
              alignment: WrapAlignment.start,
              spacing: 4.0,
              runSpacing: 4.0,
              children: [
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  surfaceTintColor: Colors.transparent,
                  label: Text(
                    S.of(context)!.archiveSerialNumber,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{asn}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    S.of(context)!.correspondent,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () =>
                      _addParameterToInput("{correspondent}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    S.of(context)!.documentType,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () =>
                      _addParameterToInput("{document_type}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(
                    AppColor.white,
                  ),
                  label: Text(
                    S.of(context)!.tags,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{tag_list}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    S.of(context)!.title,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{title}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    S.of(context)!.createdAt,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{created}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    "${S.of(context)!.createdAt} (${S.of(context)!.storagePathYear})",
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () =>
                      _addParameterToInput("{created_year}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    "${S.of(context)!.createdAt} (${S.of(context)!.storagePathMonth})",
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () =>
                      _addParameterToInput("{created_month}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    "${S.of(context)!.createdAt} (${S.of(context)!.storagePathDay})",
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{created_day}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    S.of(context)!.createdAt,
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{added}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    "${S.of(context)!.createdAt} (${S.of(context)!.storagePathYear})",
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{added_year}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    "${S.of(context)!.createdAt} (${S.of(context)!.storagePathMonth})",
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{added_month}", field),
                ),
                InputChip(
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(23),
                  ),
                  color: const MaterialStatePropertyAll(AppColor.white),
                  label: Text(
                    "${S.of(context)!.createdAt} (${S.of(context)!.storagePathDay})",
                    style: const TextStyle(color: AppColor.primary),
                  ),
                  onPressed: () => _addParameterToInput("{added_day}", field),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void _addParameterToInput(String param, FormFieldState<String> field) {
    final text = (field.value ?? "") + param;
    field.didChange(text);
    _textEditingController.text = text;
    _textEditingController.selection = TextSelection.fromPosition(
        TextPosition(offset: _textEditingController.text.length));
  }

  void _resetfield(FormFieldState<String> field) {
    field.didChange("");
    _textEditingController.clear();
  }
}

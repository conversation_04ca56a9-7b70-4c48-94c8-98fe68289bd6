import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:open_filex/open_filex.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/accessibility/accessibility_utils.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/bloc/connectivity_cubit.dart';
import 'package:paperless_mobile/core/bloc/loading_status.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/core/translation/error_code_localization_mapper.dart';
import 'package:paperless_mobile/core/widgets/material/colored_tab_bar.dart';
import 'package:paperless_mobile/features/document_details/cubit/document_details_cubit.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_content_widget.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_download_button.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_meta_data_widget.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_notes_widget.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_overview_widget.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_permissions_widget.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_project_widget.dart';
import 'package:paperless_mobile/features/document_details/view/widgets/document_share_button.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/documents/view/widgets/delete_document_confirmation_dialog.dart';
import 'package:paperless_mobile/features/documents/view/widgets/document_preview.dart';
import 'package:paperless_mobile/features/similar_documents/cubit/similar_documents_cubit.dart';
import 'package:paperless_mobile/features/similar_documents/view/similar_documents_view.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/connectivity_aware_action_wrapper.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';
import 'package:paperless_mobile/theme.dart';

class DocumentDetailsPage extends StatefulWidget {
  final int id;
  final String? title;
  final bool isLabelClickable;
  final String? titleAndContentQueryString;
  final String? thumbnailUrl;
  final String? heroTag;

  const DocumentDetailsPage({
    super.key,
    this.isLabelClickable = true,
    this.titleAndContentQueryString,
    this.thumbnailUrl,
    required this.id,
    this.heroTag,
    this.title,
  });

  @override
  State<DocumentDetailsPage> createState() => _DocumentDetailsPageState();
}

class _DocumentDetailsPageState extends State<DocumentDetailsPage> {
  static const double _itemSpacing = 24;

  final _pagingScrollController = ScrollController();
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    initializeDateFormatting(Localizations.localeOf(context).toString());
  }

  @override
  Widget build(BuildContext context) {
    final DocumentUploadCubit documentUploadCubit = context.read();
    final disableAnimations = MediaQuery.disableAnimationsOf(context);
    debugPrint(disableAnimations.toString());
    final hasMultiUserSupport =
        context.watch<LocalUserAccount>().hasMultiUserSupport;
    const tabLength = 7;
    return AnnotatedRegion(
      value: buildOverlayStyle(
        Theme.of(context),
        systemNavigationBarColor: Theme.of(context).bottomAppBarTheme.color,
      ),
      child: BlocBuilder<DocumentDetailsCubit, DocumentDetailsState>(
        builder: (context, state) {
          return DefaultTabController(
            length: tabLength,
            child: Scaffold(
              backgroundColor: AppColor.backgroundColor,
              appBar: AppBar(
                backgroundColor: AppColor.white,
                iconTheme: const IconThemeData(color: AppColor.primary),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(widget.title ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyles.textStyleAppBar),
                    ),
                  ],
                ),
                actions: [
                  IconButton(
                    onPressed: () {
                      EditDocumentRoute(state.document!).push(context);
                    },
                    icon: SvgPicture.asset('assets/svgs/edit.svg'),
                  ),
                  const Gap(16)
                ],
              ),
              // extendBodyBehindAppBar: false,
              // floatingActionButtonLocation:
              //     FloatingActionButtonLocation.endDocked,
              // floatingActionButton: switch (state.status) {
              //   LoadingStatus.loaded => _buildEditButton(state.document!),
              //   _ => null
              // },
              bottomNavigationBar: _buildBottomAppBar(),
              body: BlocBuilder<DocumentDetailsCubit, DocumentDetailsState>(
                builder: (context, state) {
                  final title = switch (state.status) {
                    LoadingStatus.loaded => state.document!.title,
                    _ => widget.title ?? '',
                  };

                  return BlocProvider(
                    create: (context) => SimilarDocumentsCubit(
                      context.read(),
                      context.read(),
                      context.read(),
                      documentId: widget.id,
                    ),
                    child: DefaultTabController(
                      length: tabLength,
                      child: Column(
                        children: [
                          // Header với hình ảnh và TabBar
                          SizedBox(
                            height: 161,
                            child: Stack(
                              children: [
                                // Document preview
                                Positioned.fill(
                                  child: Hero(
                                    tag: widget.heroTag ?? "thumb_${widget.id}",
                                    child: GestureDetector(
                                      onTap: () {
                                        DocumentPreviewRoute(
                                          id: widget.id,
                                          title: title,
                                        ).push(context);
                                      },
                                      child: DocumentPreview(
                                        documentId: widget.id,
                                        title: title,
                                        enableHero: false,
                                        fit: BoxFit.cover,
                                        borderRadius: 0,
                                        alignment: Alignment.topCenter,
                                      ),
                                    ),
                                  ).accessible(),
                                ),
                              ],
                            ),
                          ),
                          // TabBar
                          ColoredTabBar(
                            color: Colors.white,
                            tabBar: TabBar(
                              isScrollable: true,
                              indicatorColor: AppColor.primary,
                              tabAlignment: TabAlignment.start,
                              labelColor: AppColor
                                  .primary, // Màu của text khi tab được chọn
                              unselectedLabelColor: AppColor.black_333333, //
                              tabs: [
                                Tab(
                                  child: Text(
                                    S.of(context)!.overview,
                                  ),
                                ),
                                Tab(
                                  child: Text(
                                    S.of(context)!.content,
                                  ),
                                ),
                                Tab(
                                  child: Text(
                                    S.of(context)!.metaData,
                                  ),
                                ),
                                Tab(
                                  child: Text(
                                    S.of(context)!.similarDocuments,
                                  ),
                                ),
                                Tab(
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        S.of(context)!.notes(0),
                                      ),
                                      if ((state.document?.notes.length ?? 0) >
                                          0)
                                        Card(
                                          color: AppColor.orange_EC8305,
                                          surfaceTintColor: Colors.transparent,
                                          child: Text(
                                            state.document!.notes.length
                                                .toString(),
                                            style: const TextStyle(
                                                color: AppColor.white),
                                          ).paddedSymmetrically(
                                              horizontal: 8, vertical: 2),
                                        ),
                                    ],
                                  ),
                                ),

                                // if (hasMultiUserSupport)
                                Tab(
                                  child: Text(
                                    S.of(context)!.permissions,
                                  ),
                                ),
                                const Tab(
                                  child: Text(
                                    'Projects',
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // TabBarView với CustomScrollView
                          Expanded(
                            child: TabBarView(
                              children:
                                  _buildTabViews(state, hasMultiUserSupport),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEditButton(DocumentModel document) {
    final currentUser = context.watch<LocalUserAccount>();

    bool canEdit = context.watchInternetConnection &&
        currentUser.paperlessUser.canEditDocuments;
    if (!canEdit) {
      return const SizedBox.shrink();
    }
    return Tooltip(
      message: S.of(context)!.editDocumentTooltip,
      preferBelow: false,
      verticalOffset: 40,
      child: FloatingActionButton(
        heroTag: "fab_document_details",
        child: const Icon(Icons.edit),
        onPressed: () => EditDocumentRoute(document).push(context),
      ),
    );
  }

  Widget _buildErrorState() {
    return const SliverToBoxAdapter(
      child: Center(
        child: Text("Could not load document."),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const SliverFillRemaining(
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  BlocBuilder<DocumentDetailsCubit, DocumentDetailsState> _buildBottomAppBar() {
    return BlocBuilder<DocumentDetailsCubit, DocumentDetailsState>(
      builder: (context, state) {
        final currentUser = context.watch<LocalUserAccount>();
        return BottomAppBar(
          color: Colors.white,
          surfaceTintColor: Colors.transparent,
          child: Builder(
            builder: (context) {
              return switch (state.status) {
                LoadingStatus.loaded => Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      ConnectivityAwareActionWrapper(
                        disabled: !currentUser.paperlessUser.canDeleteDocuments,
                        offlineBuilder: (context, child) {
                          return IconButton(
                            icon: SvgPicture.asset(
                              'assets/svgs/trash.svg',
                              color: AppColor.black_333333,
                            ),
                            onPressed: null,
                          ).paddedSymmetrically(horizontal: 4);
                        },
                        child: IconButton(
                          tooltip: S.of(context)!.deleteDocumentTooltip,
                          icon: SvgPicture.asset(
                            'assets/svgs/trash.svg',
                            color: AppColor.black_333333,
                          ),
                          onPressed: () => _onDelete(state.document!),
                        ).paddedSymmetrically(horizontal: 4),
                      ),
                      ConnectivityAwareActionWrapper(
                        offlineBuilder: (context, child) =>
                            const DocumentDownloadButton(
                          document: null,
                          enabled: false,
                        ),
                        child: DocumentDownloadButton(
                          document: state.document,
                        ),
                      ),
                      ConnectivityAwareActionWrapper(
                        offlineBuilder: (context, child) => const IconButton(
                          icon: Icon(Icons.open_in_new),
                          onPressed: null,
                        ),
                        child: IconButton(
                          tooltip: S.of(context)!.openInSystemViewer,
                          icon: SvgPicture.asset(
                            'assets/svgs/push.svg',
                            width: 22,
                          ),
                          onPressed: _onOpenFileInSystemViewer,
                        ).paddedOnly(right: 4.0),
                      ),
                      DocumentShareButton(document: state.document),
                      IconButton(
                        tooltip: S.of(context)!.print,
                        onPressed: () => context
                            .read<DocumentDetailsCubit>()
                            .printDocument(),
                        icon: SvgPicture.asset('assets/svgs/Printer.svg'),
                      ),
                    ],
                  ),
                _ => const SizedBox.shrink(),
              };
            },
          ),
        );
      },
    );
  }

  void _onOpenFileInSystemViewer() async {
    final status =
        await context.read<DocumentDetailsCubit>().openDocumentInSystemViewer();
    if (status == ResultType.done) return;
    if (status == ResultType.noAppToOpen) {
      showGenericError(context, S.of(context)!.noAppToDisplayPDFFilesFound);
    }
    if (status == ResultType.fileNotFound) {
      showGenericError(context, translateError(context, ErrorCode.unknown));
    }
    if (status == ResultType.permissionDenied) {
      showGenericError(
          context, S.of(context)!.couldNotOpenFilePermissionDenied);
    }
  }

  void _onDelete(DocumentModel document) async {
    final delete = await showDialog(
          context: context,
          builder: (context) =>
              DeleteDocumentConfirmationDialog(document: document),
        ) ??
        false;
    if (delete) {
      try {
        await context.read<DocumentDetailsCubit>().delete(document);
        // showSnackBar(context, S.of(context)!.documentSuccessfullyDeleted);
      } on PaperlessApiException catch (error, stackTrace) {
        showErrorMessage(context, error, stackTrace);
      } finally {
        do {
          context.pop();
        } while (context.canPop());
      }
    }
  }

  List<Widget> _buildTabViews(
      DocumentDetailsState state, bool hasMultiUserSupport) {
    final views = [
      // Overview tab
      CustomScrollView(
        slivers: [
          switch (state.status) {
            LoadingStatus.loaded => DocumentOverviewWidget(
                document: state.document!,
                itemSpacing: _itemSpacing,
                queryString: widget.titleAndContentQueryString,
              ).paddedSymmetrically(
                vertical: 16,
                sliver: true,
              ),
            LoadingStatus.error => _buildErrorState(),
            _ => _buildLoadingState(),
          },
        ],
      ),
      // Content tab
      CustomScrollView(
        slivers: [
          switch (state.status) {
            LoadingStatus.loaded => DocumentContentWidget(
                document: state.document!,
                queryString: widget.titleAndContentQueryString,
              ).paddedSymmetrically(
                vertical: 16,
                sliver: true,
              ),
            LoadingStatus.error => _buildErrorState(),
            _ => _buildLoadingState(),
          }
        ],
      ),
      // Metadata tab
      CustomScrollView(
        slivers: [
          switch (state.status) {
            LoadingStatus.loaded => DocumentMetaDataWidget(
                document: state.document!,
                itemSpacing: _itemSpacing,
                metaData: state.metaData!,
              ).paddedSymmetrically(
                vertical: 16,
                sliver: true,
              ),
            LoadingStatus.error => _buildErrorState(),
            _ => _buildLoadingState(),
          },
        ],
      ),
      // Similar documents tab
      CustomScrollView(
        controller: _pagingScrollController,
        slivers: [
          SimilarDocumentsView(
            pagingScrollController: _pagingScrollController,
          ).paddedSymmetrically(
            vertical: 16,
            sliver: true,
          ),
        ],
      ),
      // Notes tab
      CustomScrollView(
        slivers: [
          switch (state.status) {
            LoadingStatus.loaded => DocumentNotesWidget(
                document: state.document!,
              ).paddedSymmetrically(
                vertical: 16,
                sliver: true,
              ),
            LoadingStatus.error => _buildErrorState(),
            _ => _buildLoadingState(),
          },
        ],
      ),

      CustomScrollView(
        controller: _pagingScrollController,
        slivers: [
          switch (state.status) {
            LoadingStatus.loaded => DocumentPermissionsWidget(
                documentDetail: state.document!,
              ).paddedSymmetrically(
                vertical: 16,
                sliver: true,
              ),
            LoadingStatus.error => _buildErrorState(),
            _ => _buildLoadingState(),
          }
        ],
      ),
      CustomScrollView(
        controller: _pagingScrollController,
        slivers: [
          switch (state.status) {
            LoadingStatus.loaded => DocumentProjectWidget(
                documentDetail: state.document!,
              ).paddedSymmetrically(
                vertical: 16,
                sliver: true,
              ),
            LoadingStatus.error => _buildErrorState(),
            _ => _buildLoadingState(),
          }
        ],
      ),
    ];

    // // Add permissions tab if needed
    // if (hasMultiUserSupport) {
    //   views.add(
    //     CustomScrollView(
    //       controller: _pagingScrollController,
    //       slivers: [
    //         switch (state.status) {
    //           LoadingStatus.loaded => DocumentPermissionsWidget(
    //               document: state.document!,
    //             ).paddedSymmetrically(
    //               vertical: 16,
    //               sliver: true,
    //             ),
    //           LoadingStatus.error => _buildErrorState(),
    //           _ => _buildLoadingState(),
    //         }
    //       ],
    //     ),
    //   );
    // }

    // Apply padding to all views
    return views
        .map(
          (child) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: child,
          ),
        )
        .toList();
  }
}

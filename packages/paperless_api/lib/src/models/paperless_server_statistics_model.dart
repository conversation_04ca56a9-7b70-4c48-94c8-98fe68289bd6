class PaperlessServerStatisticsModel {
  final int documentsTotal;
  final int documentsInInbox;
  final int? totalChars;
  final int inboxTag;
  final int characterCount;
  final int tagCount;
  final int correspondentCount;
  final int documentTypeCount;
  final int storagePathCount;
  final int currentAsn;
  final List<DocumentFileTypeCount> fileTypeCounts;

  PaperlessServerStatisticsModel({
    required this.documentsTotal,
    required this.documentsInInbox,
    this.totalChars,
    required this.inboxTag,
    required this.characterCount,
    required this.tagCount,
    required this.correspondentCount,
    required this.documentTypeCount,
    required this.storagePathCount,
    required this.currentAsn,
    this.fileTypeCounts = const [],
  });

  factory PaperlessServerStatisticsModel.fromJson(Map<String, dynamic> json) {
    return PaperlessServerStatisticsModel(
      documentsTotal: json['documents_total'] ?? 0,
      documentsInInbox: json['documents_inbox'] ?? 0,
      totalChars: json['character_count'],
      inboxTag: json['inbox_tag'] ?? 0,
      characterCount: json['character_count'] ?? 0,
      tagCount: json['tag_count'] ?? 0,
      correspondentCount: json['correspondent_count'] ?? 0,
      documentTypeCount: json['document_type_count'] ?? 0,
      storagePathCount: json['storage_path_count'] ?? 0,
      currentAsn: json['current_asn'] ?? 0,
      fileTypeCounts: _parseFileTypeCounts(json['document_file_type_counts']),
    );
  }

  static List<DocumentFileTypeCount> _parseFileTypeCounts(dynamic value) {
    if (value is List) {
      return value.map((e) => DocumentFileTypeCount.fromJson(e)).toList();
    }
    return [];
  }
}

class DocumentFileTypeCount {
  final String mimeType;
  final int count;

  DocumentFileTypeCount({
    required this.mimeType,
    required this.count,
  });

  factory DocumentFileTypeCount.fromJson(Map<String, dynamic> json) {
    return DocumentFileTypeCount(
      mimeType: json['mime_type'] ?? '',
      count: json['mime_type_count'] ?? 0,
    );
  }
}

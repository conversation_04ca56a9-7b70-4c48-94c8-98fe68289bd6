import 'package:flutter/material.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

class DocumentTypeWidget extends StatelessWidget {
  final DocumentType? documentType;
  final bool isClickable;
  final TextStyle? textStyle;
  final void Function(int? id)? onSelected;
  final Color? textColor;

  const DocumentTypeWidget({
    super.key,
    this.textColor,
    required this.documentType,
    this.isClickable = true,
    this.textStyle,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: !isClickable,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(4),
          onTap: () => onSelected?.call(documentType?.id),
          child: Text(
            documentType?.toString() ?? "-",
            style: (textStyle ?? Theme.of(context).textTheme.bodyMedium)
                ?.copyWith(
                    color: textColor ?? AppColor.black_333333),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ),
    );
  }
}

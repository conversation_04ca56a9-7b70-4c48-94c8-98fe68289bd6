import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_mobile/features/login/services/authentication_service.dart';
import 'package:paperless_mobile/features/settings/view/widgets/user_settings_builder.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class BiometricAuthenticationSetting extends StatelessWidget {
  const BiometricAuthenticationSetting({super.key});

  @override
  Widget build(BuildContext context) {
    return UserAccountBuilder(
      builder: (context, account) {
        if (account == null) {
          return const SizedBox.shrink();
        }
        return ListTile(
          title: Text(S.of(context)!.biometricAuthentication),
          subtitle: Text(S.of(context)!.authenticateOnAppStart),
          trailing: CupertinoSwitch(
            value: account.settings.isBiometricAuthenticationEnabled,
            onChanged: (val) async {
              final String localizedReason =
                  S.of(context)!.authenticateToToggleBiometricAuthentication(
                        val ? 'enable' : 'disable',
                      );

              final isAuthenticated = await context
                  .read<LocalAuthenticationService>()
                  .authenticateLocalUser(localizedReason);
              if (isAuthenticated) {
                account.settings.isBiometricAuthenticationEnabled = val;
                account.save();
              }
            },
          ),
          onTap: () async {
            // Cũng cho phép toggle bằng cách tap cả dòng
            final newVal = !account.settings.isBiometricAuthenticationEnabled;
            final String localizedReason =
                S.of(context)!.authenticateToToggleBiometricAuthentication(
                      newVal ? 'enable' : 'disable',
                    );

            final isAuthenticated = await context
                .read<LocalAuthenticationService>()
                .authenticateLocalUser(localizedReason);
            if (isAuthenticated) {
              account.settings.isBiometricAuthenticationEnabled = newVal;
              account.save();
            }
          },
        );
      },
    );
  }
}

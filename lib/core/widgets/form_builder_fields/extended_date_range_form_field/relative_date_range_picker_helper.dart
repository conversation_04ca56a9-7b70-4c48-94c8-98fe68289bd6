import 'package:flutter/material.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class RelativeDateRangePickerHelper extends StatefulWidget {
  final FormFieldState<DateRangeQuery> field;
  final void Function(DateRangeQuery value)? onChanged;
  final EdgeInsets padding;

  const RelativeDateRangePickerHelper({
    super.key,
    required this.field,
    this.onChanged,
    required this.padding,
  });

  @override
  State<RelativeDateRangePickerHelper> createState() =>
      _RelativeDateRangePickerHelperState();
}

class _RelativeDateRangePickerHelperState
    extends State<RelativeDateRangePickerHelper> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: widget.padding,
        children: _options.map((option) {
          final isSelected = widget.field.value == option.value;
          return Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: InkWell(
              onTap: () {
                final value =
                    isSelected ? const RelativeDateRangeQuery() : option.value;
                widget.field.didChange(value);
                widget.onChanged?.call(value);
              },
              child: Text(
                option.title,
                style: const TextStyle(
                  color: AppColor.primary,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColor.primary,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<_ExtendedDateRangeQueryOption> get _options => [
        _ExtendedDateRangeQueryOption(
          S.of(context)!.lastNWeeks(1),
          const RelativeDateRangeQuery(1, DateRangeUnit.week),
        ),
        _ExtendedDateRangeQueryOption(
          S.of(context)!.lastNMonths(1),
          const RelativeDateRangeQuery(1, DateRangeUnit.month),
        ),
        _ExtendedDateRangeQueryOption(
          S.of(context)!.lastNMonths(3),
          const RelativeDateRangeQuery(3, DateRangeUnit.month),
        ),
        _ExtendedDateRangeQueryOption(
          S.of(context)!.lastNYears(1),
          const RelativeDateRangeQuery(1, DateRangeUnit.year),
        ),
      ];
}

class _ExtendedDateRangeQueryOption {
  final String title;
  final RelativeDateRangeQuery value;

  _ExtendedDateRangeQueryOption(this.title, this.value);
}

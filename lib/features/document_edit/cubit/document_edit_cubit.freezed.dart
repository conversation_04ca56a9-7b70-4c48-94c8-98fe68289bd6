// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_edit_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DocumentEditState {
  DocumentModel get document => throw _privateConstructorUsedError;
  FieldSuggestions? get suggestions => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DocumentEditStateCopyWith<DocumentEditState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentEditStateCopyWith<$Res> {
  factory $DocumentEditStateCopyWith(
          DocumentEditState value, $Res Function(DocumentEditState) then) =
      _$DocumentEditStateCopyWithImpl<$Res, DocumentEditState>;
  @useResult
  $Res call({DocumentModel document, FieldSuggestions? suggestions});
}

/// @nodoc
class _$DocumentEditStateCopyWithImpl<$Res, $Val extends DocumentEditState>
    implements $DocumentEditStateCopyWith<$Res> {
  _$DocumentEditStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? document = null,
    Object? suggestions = freezed,
  }) {
    return _then(_value.copyWith(
      document: null == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as DocumentModel,
      suggestions: freezed == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as FieldSuggestions?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DocumentEditStateImplCopyWith<$Res>
    implements $DocumentEditStateCopyWith<$Res> {
  factory _$$DocumentEditStateImplCopyWith(_$DocumentEditStateImpl value,
          $Res Function(_$DocumentEditStateImpl) then) =
      __$$DocumentEditStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DocumentModel document, FieldSuggestions? suggestions});
}

/// @nodoc
class __$$DocumentEditStateImplCopyWithImpl<$Res>
    extends _$DocumentEditStateCopyWithImpl<$Res, _$DocumentEditStateImpl>
    implements _$$DocumentEditStateImplCopyWith<$Res> {
  __$$DocumentEditStateImplCopyWithImpl(_$DocumentEditStateImpl _value,
      $Res Function(_$DocumentEditStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? document = null,
    Object? suggestions = freezed,
  }) {
    return _then(_$DocumentEditStateImpl(
      document: null == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as DocumentModel,
      suggestions: freezed == suggestions
          ? _value.suggestions
          : suggestions // ignore: cast_nullable_to_non_nullable
              as FieldSuggestions?,
    ));
  }
}

/// @nodoc

class _$DocumentEditStateImpl implements _DocumentEditState {
  const _$DocumentEditStateImpl({required this.document, this.suggestions});

  @override
  final DocumentModel document;
  @override
  final FieldSuggestions? suggestions;

  @override
  String toString() {
    return 'DocumentEditState(document: $document, suggestions: $suggestions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentEditStateImpl &&
            (identical(other.document, document) ||
                other.document == document) &&
            (identical(other.suggestions, suggestions) ||
                other.suggestions == suggestions));
  }

  @override
  int get hashCode => Object.hash(runtimeType, document, suggestions);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentEditStateImplCopyWith<_$DocumentEditStateImpl> get copyWith =>
      __$$DocumentEditStateImplCopyWithImpl<_$DocumentEditStateImpl>(
          this, _$identity);
}

abstract class _DocumentEditState implements DocumentEditState {
  const factory _DocumentEditState(
      {required final DocumentModel document,
      final FieldSuggestions? suggestions}) = _$DocumentEditStateImpl;

  @override
  DocumentModel get document;
  @override
  FieldSuggestions? get suggestions;
  @override
  @JsonKey(ignore: true)
  _$$DocumentEditStateImplCopyWith<_$DocumentEditStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_search_cubit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DocumentSearchState _$DocumentSearchStateFromJson(Map<String, dynamic> json) =>
    DocumentSearchState(
      viewType: $enumDecodeNullable(_$ViewTypeEnumMap, json['viewType']) ??
          ViewType.detailed,
    );

Map<String, dynamic> _$DocumentSearchStateToJson(
        DocumentSearchState instance) =>
    <String, dynamic>{
      'viewType': _$ViewTypeEnumMap[instance.viewType]!,
    };

const _$ViewTypeEnumMap = {
  ViewType.grid: 'grid',
  ViewType.list: 'list',
  ViewType.detailed: 'detailed',
};

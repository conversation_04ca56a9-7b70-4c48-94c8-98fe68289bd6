// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bottom_nav_bar_state.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$BottomNavBarStateCWProxy {
  BottomNavBarState indexNavBar(int indexNavBar);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `BottomNavBarState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// BottomNavBarState(...).copyWith(id: 12, name: "My name")
  /// ````
  BottomNavBarState call({
    int? indexNavBar,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfBottomNavBarState.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfBottomNavBarState.copyWith.fieldName(...)`
class _$BottomNavBarStateCWProxyImpl implements _$BottomNavBarStateCWProxy {
  const _$BottomNavBarStateCWProxyImpl(this._value);

  final BottomNavBarState _value;

  @override
  BottomNavBarState indexNavBar(int indexNavBar) =>
      this(indexNavBar: indexNavBar);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `BottomNavBarState(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// BottomNavBarState(...).copyWith(id: 12, name: "My name")
  /// ````
  BottomNavBarState call({
    Object? indexNavBar = const $CopyWithPlaceholder(),
  }) {
    return BottomNavBarState(
      indexNavBar:
          indexNavBar == const $CopyWithPlaceholder() || indexNavBar == null
              ? _value.indexNavBar
              // ignore: cast_nullable_to_non_nullable
              : indexNavBar as int,
    );
  }
}

extension $BottomNavBarStateCopyWith on BottomNavBarState {
  /// Returns a callable class that can be used as follows: `instanceOfBottomNavBarState.copyWith(...)` or like so:`instanceOfBottomNavBarState.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$BottomNavBarStateCWProxy get copyWith =>
      _$BottomNavBarStateCWProxyImpl(this);
}

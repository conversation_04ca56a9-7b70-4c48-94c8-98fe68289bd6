import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';
import 'package:paperless_mobile/features/logging/data/logger.dart';

class PdfThumbnail extends StatefulWidget {
  final Uint8List pdfData;
  final double width;
  final double height;

  const PdfThumbnail({
    super.key,
    required this.pdfData,
    this.width = 100,
    this.height = 100,
  });

  @override
  State<PdfThumbnail> createState() => _PdfThumbnailState();
}

class _PdfThumbnailState extends State<PdfThumbnail> {
  late Future<Image?> _imageFuture;

  @override
  void initState() {
    super.initState();
    logger.fd(
      "Initializing PDF thumbnail with data size: ${widget.pdfData.length} bytes",
      className: runtimeType.toString(),
      methodName: "initState",
    );
    _imageFuture = _renderThumbnail();
  }

  Future<Image?> _renderThumbnail() async {
    try {
      logger.fd(
        "Attempting to render PDF thumbnail",
        className: runtimeType.toString(),
        methodName: "_renderThumbnail",
      );

      final doc = await PdfDocument.openData(widget.pdfData);
      final page = await doc.getPage(1);

      logger.fd(
        "PDF page loaded, dimensions: ${page.width}x${page.height}",
        className: runtimeType.toString(),
        methodName: "_renderThumbnail",
      );

      final pageImage = await page.render(
        width: page.width,
        height: page.height,
        format: PdfPageImageFormat.png,
      );

      await page.close();
      await doc.close();

      if (pageImage != null) {
        logger.fd(
          "PDF thumbnail rendered successfully",
          className: runtimeType.toString(),
          methodName: "_renderThumbnail",
        );

        return Image.memory(
          pageImage.bytes,
          width: widget.width,
          height: widget.height,
          fit: BoxFit.fill,
        );
      } else {
        logger.fe(
          "Failed to render PDF thumbnail - pageImage is null",
          className: runtimeType.toString(),
          methodName: "_renderThumbnail",
        );
        return null;
      }
    } catch (e, stackTrace) {
      logger.fe(
        "Error rendering PDF thumbnail",
        className: runtimeType.toString(),
        methodName: "_renderThumbnail",
        error: e,
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Image?>(
      future: _imageFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          logger.fe(
            "Error in PDF thumbnail FutureBuilder",
            className: runtimeType.toString(),
            methodName: "build",
            error: snapshot.error,
            stackTrace: snapshot.stackTrace,
          );
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error_outline, color: Colors.red),
                const SizedBox(height: 8),
                Text(
                  'Error loading PDF preview',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          );
        }

        if (!snapshot.hasData) {
          return const Center(
            child: Text('No preview available'),
          );
        }

        return snapshot.data!;
      },
    );
  }
}

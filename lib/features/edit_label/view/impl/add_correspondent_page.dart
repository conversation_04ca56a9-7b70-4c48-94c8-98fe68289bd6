import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/edit_label/view/add_label_page.dart';
import 'package:paperless_mobile/features/labels/cubit/label_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class AddCorrespondentPage extends StatelessWidget {
  final String? initialName;
  const AddCorrespondentPage({super.key, this.initialName});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LabelCubit(
        context.read(),
      ),
      child: AddLabelPage<Correspondent>(
        pageTitle: Text(
          S.of(context)!.addCorrespondent,
          style: AppTextStyles.textStyleAppBar,
        ),
        hinText: 'Enter name',
        title: 'Name',
        subTitle: 'Matching Algorithm',
        fromJsonT: Correspondent.fromJson,
        initialName: initialName,
        onSubmit: (context, label) async {
          context.read<LabelCubit>().addCorrespondent(label);
          return label;
        },
      ),
    );
  }
}

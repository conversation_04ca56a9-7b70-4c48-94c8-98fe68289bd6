// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_account_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $addAccountRoute,
    ];

RouteBase get $addAccountRoute => GoRouteData.$route(
      path: '/add-account',
      name: 'addAccount',
      parentNavigatorKey: AddAccountRoute.$parentNavigatorKey,
      factory: $AddAccountRouteExtension._fromState,
    );

extension $AddAccountRouteExtension on AddAccountRoute {
  static AddAccountRoute _fromState(GoRouterState state) =>
      const AddAccountRoute();

  String get location => GoRouteData.$location(
        '/add-account',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

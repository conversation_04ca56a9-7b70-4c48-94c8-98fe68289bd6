{"developedBy": "Développé par {name}.", "@developedBy": {"placeholders": {"name": {}}}, "addAnotherAccount": "Ajouter un autre compte", "@addAnotherAccount": {}, "account": "<PERSON><PERSON><PERSON>", "@account": {}, "addCorrespondent": "Nouveau correspondant", "@addCorrespondent": {"description": "Title when adding a new correspondent"}, "addDocumentType": "Nouveau type de document", "@addDocumentType": {"description": "Title when adding a new document type"}, "addStoragePath": "Nouveau chemin de stockage", "@addStoragePath": {"description": "Title when adding a new storage path"}, "addTag": "Nouvelle étiquette", "@addTag": {"description": "Title when adding a new tag"}, "aboutThisApp": "À propos de cette application", "@aboutThisApp": {"description": "Label for about this app tile displayed in the drawer"}, "loggedInAs": "Connecté en tant que {name}", "@loggedInAs": {"placeholders": {"name": {}}}, "disconnect": "Se déconnecter", "@disconnect": {"description": "Logout button label"}, "reportABug": "Signaler un bug", "@reportABug": {}, "settings": "Paramètres", "@settings": {}, "authenticateOnAppStart": "S'authentifier au démarrage de l'application", "@authenticateOnAppStart": {"description": "Description of the biometric authentication settings tile"}, "biometricAuthentication": "Authentification biométrique", "@biometricAuthentication": {}, "authenticateToToggleBiometricAuthentication": "{mode, select, enable{Authentifiez-vous pour activer l'authentification biométrique} disable{Authentifiez-vous pour désactiver l'authentification biométrique} other{}}", "@authenticateToToggleBiometricAuthentication": {"placeholders": {"mode": {}}}, "documents": "Documents", "@documents": {}, "inbox": "<PERSON><PERSON><PERSON>", "@inbox": {}, "labels": "Étiquettes", "@labels": {}, "scanner": "<PERSON>anne<PERSON>", "@scanner": {}, "startTyping": "Commencez à écrire...", "@startTyping": {}, "doYouReallyWantToDeleteThisView": "Voulez-vous vraiment supprimer cette vue enregistrée ?", "@doYouReallyWantToDeleteThisView": {}, "deleteView": "Supprimer la vue enregistrée {name}?", "@deleteView": {}, "addedAt": "Date d’ajout", "@addedAt": {}, "archiveSerialNumber": "Numéro de série de l’archive", "@archiveSerialNumber": {}, "asn": "NSA", "@asn": {}, "correspondent": "Correspondant", "@correspondent": {}, "createdAt": "Date de création", "@createdAt": {}, "documentSuccessfullyDeleted": "Le document a bien été supprimé.", "@documentSuccessfullyDeleted": {}, "assignAsn": "Assigner un <PERSON>", "@assignAsn": {}, "deleteDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@deleteDocumentTooltip": {"description": "Tooltip shown for the delete button on details page"}, "downloadDocumentTooltip": "Télécharger", "@downloadDocumentTooltip": {"description": "Tooltip shown for the download button on details page"}, "editDocumentTooltip": "Modifier", "@editDocumentTooltip": {"description": "Tooltip shown for the edit button on details page"}, "loadFullContent": "Charger tout le contenu", "@loadFullContent": {}, "noAppToDisplayPDFFilesFound": "Aucune application trouvée pour afficher les fichiers PDF !", "@noAppToDisplayPDFFilesFound": {}, "openInSystemViewer": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans le lecteur système", "@openInSystemViewer": {}, "couldNotOpenFilePermissionDenied": "Impossible d'ouvrir le fichier : permission refusée.", "@couldNotOpenFilePermissionDenied": {}, "previewTooltip": "Prévisualisation", "@previewTooltip": {"description": "Tooltip shown for the preview button on details page"}, "shareTooltip": "Partager", "@shareTooltip": {"description": "Tooltip shown for the share button on details page"}, "similarDocuments": "Documents similaires", "@similarDocuments": {"description": "Label shown in the tabbar on details page"}, "content": "Contenu", "@content": {"description": "Label shown in the tabbar on details page"}, "metaData": "Métadonnées", "@metaData": {"description": "Label shown in the tabbar on details page"}, "overview": "Vue d’ensemble", "@overview": {"description": "Label shown in the tabbar on details page"}, "documentType": "Type de document", "@documentType": {}, "archivedPdf": "Archive (PDF)", "@archivedPdf": {"description": "Option to chose when downloading a document"}, "chooseFiletype": "Choisissez le type de fichier", "@chooseFiletype": {}, "original": "Original", "@original": {"description": "Option to chose when downloading a document"}, "documentSuccessfullyDownloaded": "Document téléchargé avec succès.", "@documentSuccessfullyDownloaded": {}, "suggestions": "Suggestions : ", "@suggestions": {}, "editDocument": "Modifier le document", "@editDocument": {}, "advanced": "<PERSON><PERSON><PERSON>", "@advanced": {}, "apply": "Appliquer", "@apply": {}, "extended": "Recherche avancée", "@extended": {}, "titleAndContent": "Titre & contenu", "@titleAndContent": {}, "title": "Titre", "@title": {}, "reset": "Réinitialiser", "@reset": {}, "filterDocuments": "Filtrer les documents", "@filterDocuments": {"description": "Title of the document filter"}, "originalMD5Checksum": "Somme de contrôle MD5 de l'original", "@originalMD5Checksum": {}, "mediaFilename": "Nom de fichier du média", "@mediaFilename": {}, "originalFileSize": "<PERSON><PERSON> de <PERSON> de l'original", "@originalFileSize": {}, "originalMIMEType": "Type mime de l'original", "@originalMIMEType": {}, "modifiedAt": "Date de modification", "@modifiedAt": {}, "preview": "Prévisualisation", "@preview": {"description": "Title of the document preview page"}, "scanADocument": "Scanner un document", "@scanADocument": {}, "noDocumentsScannedYet": "Aucun document scanné pour le moment.", "@noDocumentsScannedYet": {}, "or": "ou", "@or": {"description": "Used on the scanner page between both main actions when no scans have been captured."}, "deleteAllScans": "Supprimer tous les scans", "@deleteAllScans": {}, "uploadADocumentFromThisDevice": "Charger un document depuis cet appareil", "@uploadADocumentFromThisDevice": {"description": "Button label on scanner page"}, "noMatchesFound": "Aucune correspondance trouvée.", "@noMatchesFound": {"description": "Displayed when no documents were found in the document search."}, "removeFromSearchHistory": "Supprimer de l'historique des recherches ?", "@removeFromSearchHistory": {}, "results": "Résultats", "@results": {"description": "Label displayed above search results in document search."}, "searchDocuments": "Rechercher des documents", "@searchDocuments": {}, "resetFilter": "Réinitialiser le filtre", "@resetFilter": {}, "lastMonth": "Le mois dernier", "@lastMonth": {}, "last7Days": "Les 7 derniers jours", "@last7Days": {}, "last3Months": "Les 3 derniers mois", "@last3Months": {}, "lastYear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@lastYear": {}, "search": "Recherche", "@search": {}, "documentsSuccessfullyDeleted": "Les documents ont bien été supprimés.", "@documentsSuccessfullyDeleted": {}, "thereSeemsToBeNothingHere": "Il semble qu'il n'y a rien ici...", "@thereSeemsToBeNothingHere": {}, "oops": "Oups.", "@oops": {}, "newDocumentAvailable": "Nouveau document disponible !", "@newDocumentAvailable": {}, "orderBy": "Trier par", "@orderBy": {}, "thisActionIsIrreversibleDoYouWishToProceedAnyway": "Cette action est irréversible. Souhaitez-vous quand-même continuer ?", "@thisActionIsIrreversibleDoYouWishToProceedAnyway": {}, "confirmDeletion": "Confirmer la <PERSON>", "@confirmDeletion": {}, "areYouSureYouWantToDeleteTheFollowingDocuments": "{count, plural, one{Êtes-vous sûr de vouloir supprimer le document suivant?} other{Êtes-vous sûr de vouloir supprimer les documents suivants ?}}", "@areYouSureYouWantToDeleteTheFollowingDocuments": {"placeholders": {"count": {}}}, "countSelected": "{count} sélectionné(s)", "@countSelected": {"description": "Displayed in the appbar when at least one document is selected.", "placeholders": {"count": {}}}, "storagePath": "Chemins de Stockage", "@storagePath": {}, "prepareDocument": "Préparer le document", "@prepareDocument": {}, "tags": "Étiquettes", "@tags": {}, "documentSuccessfullyUpdated": "Le document a bien été modifié.", "@documentSuccessfullyUpdated": {}, "fileName": "Nom du fichier", "@fileName": {}, "synchronizeTitleAndFilename": "Synchroniser le titre et le nom du fichier", "@synchronizeTitleAndFilename": {}, "reload": "Recharger", "@reload": {}, "documentSuccessfullyUploadedProcessing": "Le document a bien été chargé, traitement en cours...", "@documentSuccessfullyUploadedProcessing": {}, "deleteLabelWarningText": "L'étiquette contient des références vers d'autres documents. En supprimant cette étiquette, toutes les références seront supprimées. Continuer ?", "@deleteLabelWarningText": {}, "couldNotAcknowledgeTasks": "Impossible de marquer la tâche comme vue.", "@couldNotAcknowledgeTasks": {}, "authenticationFailedPleaseTryAgain": "L'authentification a échouée, veuillez réessayer.", "@authenticationFailedPleaseTryAgain": {}, "anErrorOccurredWhileTryingToAutocompleteYourQuery": "Une erreur est survenue lors de l'autocomplétion de votre requête.", "@anErrorOccurredWhileTryingToAutocompleteYourQuery": {}, "biometricAuthenticationFailed": "L'authentification biométrique a échoué.", "@biometricAuthenticationFailed": {}, "biometricAuthenticationNotSupported": "L'Authentification biométrique n'est pas supportée sur cet appareil.", "@biometricAuthenticationNotSupported": {}, "couldNotBulkEditDocuments": "Impossible d'effectuer la modification groupée.", "@couldNotBulkEditDocuments": {}, "couldNotCreateCorrespondent": "Impossible de créer le correspondant, veuil<PERSON><PERSON> réessayer.", "@couldNotCreateCorrespondent": {}, "couldNotLoadCorrespondents": "Impossible de charger la liste des correspondants.", "@couldNotLoadCorrespondents": {}, "couldNotCreateSavedView": "Impossible de créer la vue, veuil<PERSON><PERSON> réessayer.", "@couldNotCreateSavedView": {}, "couldNotDeleteSavedView": "Impossible de supprimer la vue enregistrée, veuil<PERSON><PERSON> réessayer", "@couldNotDeleteSavedView": {}, "youAreCurrentlyOffline": "Vous êtes actuellement hors ligne. Veuillez vous assurer que vous êtes connecté à internet.", "@youAreCurrentlyOffline": {}, "couldNotAssignArchiveSerialNumber": "Impossible d'assigner le numéro de série d'archive.", "@couldNotAssignArchiveSerialNumber": {}, "couldNotDeleteDocument": "Impossible de supprimer le document, veuillez réessayer.", "@couldNotDeleteDocument": {}, "couldNotLoadDocuments": "Impossible de charger les documents, veuillez réessayer.", "@couldNotLoadDocuments": {}, "couldNotLoadDocumentPreview": "Impossible de charger la prévisualisation du document.", "@couldNotLoadDocumentPreview": {}, "couldNotCreateDocument": "Impossible de créer le document, veuil<PERSON>z réessayer.", "@couldNotCreateDocument": {}, "couldNotLoadDocumentTypes": "Impossible de charger la liste des types de documents, veuil<PERSON>z réessayer.", "@couldNotLoadDocumentTypes": {}, "couldNotUpdateDocument": "Impossible de mettre à jour le document, veuil<PERSON>z réessayer.", "@couldNotUpdateDocument": {}, "couldNotUploadDocument": "Impossible de charger le document, veuil<PERSON>z réessayer.", "@couldNotUploadDocument": {}, "invalidCertificateOrMissingPassphrase": "Certificat invalide ou phrase de passe manquante, veuil<PERSON><PERSON> réessayer", "@invalidCertificateOrMissingPassphrase": {}, "couldNotLoadSavedViews": "Impossible de charger les vues enregistrées.", "@couldNotLoadSavedViews": {}, "aClientCertificateWasExpectedButNotSent": "Un certificat client était attendu mais n'a pas été envoyé. Veuillez fournir un certificat client valide.", "@aClientCertificateWasExpectedButNotSent": {}, "userIsNotAuthenticated": "L'utilisateur n'est pas authentifié.", "@userIsNotAuthenticated": {}, "requestTimedOut": "La requête vers le serveur a expiré.", "@requestTimedOut": {}, "anErrorOccurredRemovingTheScans": "Une erreur est survenue en retirant les scans de votre appareil.", "@anErrorOccurredRemovingTheScans": {}, "couldNotReachYourPaperlessServer": "Impossible d'atteindre votre serveur paperless, êtes-vous sûr qu'il est opérationnel ?", "@couldNotReachYourPaperlessServer": {}, "couldNotLoadSimilarDocuments": "Impossible de charger la liste des documents similaires.", "@couldNotLoadSimilarDocuments": {}, "couldNotCreateStoragePath": "Impossible de créer le chemin de stockage, veuil<PERSON><PERSON> réessayer.", "@couldNotCreateStoragePath": {}, "couldNotLoadStoragePaths": "Impossible de charger le chemin de stockage.", "@couldNotLoadStoragePaths": {}, "couldNotLoadSuggestions": "Impossible de charger les suggestions.", "@couldNotLoadSuggestions": {}, "couldNotCreateTag": "Impossible de créer l'étiquette, veuil<PERSON><PERSON> réessayer.", "@couldNotCreateTag": {}, "couldNotLoadTags": "Impossible de charger les étiquettes.", "@couldNotLoadTags": {}, "anUnknownErrorOccurred": "Une erreur inconnue est survenue.", "@anUnknownErrorOccurred": {}, "fileFormatNotSupported": "Ce format de fichier n'est pas supporté.", "@fileFormatNotSupported": {}, "report": "RAPPORT", "@report": {}, "absolute": "Absolue", "@absolute": {}, "hintYouCanAlsoSpecifyRelativeValues": "Astuce: En dehors des dates, vous pouvez aussi spécifier un intervalle de temps relatif à la date actuelle.", "@hintYouCanAlsoSpecifyRelativeValues": {"description": "Displayed in the extended date range picker"}, "amount": "Quantité", "@amount": {}, "relative": "Relatif", "@relative": {}, "last": "<PERSON><PERSON>", "@last": {}, "timeUnit": "Unité de temps", "@timeUnit": {}, "selectDateRange": "Choisissez un intervalle de temps", "@selectDateRange": {}, "after": "<PERSON><PERSON>", "@after": {}, "before": "Avant", "@before": {}, "days": "{count, plural, zero{jours} one{jour} other{jours}}", "@days": {"placeholders": {"count": {}}}, "lastNDays": "{count, plural, zero{} one{<PERSON><PERSON>} other{{count} derniers jours}}", "@lastNDays": {"placeholders": {"count": {}}}, "lastNMonths": "{count, plural, zero{} one{<PERSON><PERSON>} other{{count} derniers mois}}", "@lastNMonths": {"placeholders": {"count": {}}}, "lastNWeeks": "{count, plural, zero{} one{<PERSON><PERSON><PERSON> der<PERSON>} other{{count} dernières semaines}}", "@lastNWeeks": {"placeholders": {"count": {}}}, "lastNYears": "{count, plural, zero{} one{<PERSON> dernier} other{{count} denri<PERSON> ann<PERSON>}}", "@lastNYears": {"placeholders": {"count": {}}}, "months": "{count, plural, zero{} one{mois} other{mois}}", "@months": {"placeholders": {"count": {}}}, "weeks": "{count, plural, zero{} one{semaine} other{semaines}}", "@weeks": {"placeholders": {"count": {}}}, "years": "{count, plural, zero{} one{an} other{ans}}", "@years": {"placeholders": {"count": {}}}, "gotIt": "D'accord !", "@gotIt": {}, "cancel": "Annuler", "@cancel": {}, "close": "<PERSON><PERSON><PERSON>", "@close": {}, "create": "<PERSON><PERSON><PERSON>", "@create": {}, "delete": "<PERSON><PERSON><PERSON><PERSON>", "@delete": {}, "edit": "Modifier", "@edit": {}, "ok": "Ok", "@ok": {}, "save": "<PERSON><PERSON><PERSON><PERSON>", "@save": {}, "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@select": {}, "saveChanges": "Sauvegarder les changements", "@saveChanges": {}, "upload": "Charger le document", "@upload": {}, "youreOffline": "Vous êtes hors ligne.", "@youreOffline": {}, "deleteDocument": "Supprimer le document", "@deleteDocument": {"description": "Used as an action label on each inbox item"}, "removeDocumentFromInbox": "Document retiré de la boîte de réception.", "@removeDocumentFromInbox": {}, "areYouSureYouWantToMarkAllDocumentsAsSeen": "Êtes-vous sûr de vouloir marquer tous les documents comme vus ? Cela va déclencher une opération de modification groupée retirant toutes les étiquettes de boîte de réception des documents. Cette action est irréversible ! Êtes-vous sûr de vouloir continuer ?", "@areYouSureYouWantToMarkAllDocumentsAsSeen": {}, "markAllAsSeen": "Tout marquer comme vu ?", "@markAllAsSeen": {}, "allSeen": "Marquer comme lus", "@allSeen": {}, "markAsSeen": "Marquer comme vu", "@markAsSeen": {}, "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@refresh": {}, "youDoNotHaveUnseenDocuments": "Vous n'avez pas de documents non-vus.", "@youDoNotHaveUnseenDocuments": {}, "quickAction": "Action Rapide", "@quickAction": {}, "suggestionSuccessfullyApplied": "Suggestion appliquée avec succès.", "@suggestionSuccessfullyApplied": {}, "today": "<PERSON><PERSON><PERSON>'hui", "@today": {}, "undo": "Annuler", "@undo": {}, "nUnseen": "{count} non-vu(s)", "@nUnseen": {"placeholders": {"count": {}}}, "swipeLeftToMarkADocumentAsSeen": "Astuce: G<PERSON><PERSON> vers la gauche pour marquer un document comme vu et retirer toutes ses étiquettes de boîte de réception.", "@swipeLeftToMarkADocumentAsSeen": {}, "yesterday": "<PERSON>er", "@yesterday": {}, "anyAssigned": "N'importe lequel d'assigné", "@anyAssigned": {}, "noItemsFound": "Aucun élément trouvé !", "@noItemsFound": {}, "caseIrrelevant": "Insensible à la casse", "@caseIrrelevant": {}, "matchingAlgorithm": "Algorithme de rapprochement", "@matchingAlgorithm": {}, "match": "<PERSON><PERSON><PERSON><PERSON> de rapprochement", "@match": {}, "name": "Nom", "@name": {}, "notAssigned": "Aucun d'assign<PERSON>", "@notAssigned": {}, "addNewCorrespondent": "Ajouter un correspondant", "@addNewCorrespondent": {}, "noCorrespondentsSetUp": "Vous ne semblez pas encore avoir de correspondants.", "@noCorrespondentsSetUp": {}, "correspondents": "Correspondants", "@correspondents": {}, "addNewDocumentType": "Ajouter un type de document", "@addNewDocumentType": {}, "noDocumentTypesSetUp": "Vous ne semblez pas encore avoir de type de document.", "@noDocumentTypesSetUp": {}, "documentTypes": "Types de Documents", "@documentTypes": {}, "addNewStoragePath": "Ajouter un chemin de stockage", "@addNewStoragePath": {}, "noStoragePathsSetUp": "Vous ne semblez pas encore avoir de chemin de stockage.", "@noStoragePathsSetUp": {}, "storagePaths": "Chemins de Stockage", "@storagePaths": {}, "addNewTag": "Ajouter une étiquette", "@addNewTag": {}, "noTagsSetUp": "Vous ne semblez pas encore avoir d'étiquettes.", "@noTagsSetUp": {}, "linkedDocuments": "Documents liés", "@linkedDocuments": {}, "advancedSettings": "Paramètres Avancés", "@advancedSettings": {}, "passphrase": "Phrase de passe", "@passphrase": {}, "configureMutualTLSAuthentication": "Configurer une Authentification TLS Mutuelle", "@configureMutualTLSAuthentication": {}, "invalidCertificateFormat": "Format de certificat invalide, seul le format .pfx est supporté", "@invalidCertificateFormat": {}, "clientcertificate": "Certificat Client", "@clientcertificate": {}, "selectFile": "Sélectionner un fichier...", "@selectFile": {}, "continueLabel": "<PERSON><PERSON><PERSON>", "@continueLabel": {}, "incorrectOrMissingCertificatePassphrase": "Phrase de passe du certificat incorrecte ou manquante.", "@incorrectOrMissingCertificatePassphrase": {}, "connect": "Se connecter", "@connect": {}, "password": "Mot de passe", "@password": {}, "passwordMustNotBeEmpty": "Le mot de passe ne doit pas être vide.", "@passwordMustNotBeEmpty": {}, "connectionTimedOut": "La connexion a expiré.", "@connectionTimedOut": {}, "loginPageReachabilityMissingClientCertificateText": "Un certificat client était attendu, mais n'a pas été envoyé. Veuillez fournir un certificat.", "@loginPageReachabilityMissingClientCertificateText": {}, "couldNotEstablishConnectionToTheServer": "Impossible d'établir la connexion jusqu'au serveur.", "@couldNotEstablishConnectionToTheServer": {}, "connectionSuccessfulylEstablished": "Connexion établie avec succès.", "@connectionSuccessfulylEstablished": {}, "hostCouldNotBeResolved": "L'hôte ne peut pas être résolu. Veuillez vérifier l'adresse du serveur et votre connexion internet. ", "@hostCouldNotBeResolved": {}, "serverAddress": "<PERSON><PERSON><PERSON> Serveur", "@serverAddress": {}, "invalidAddress": "<PERSON><PERSON><PERSON> invalide.", "@invalidAddress": {}, "serverAddressMustIncludeAScheme": "L'adresse du serveur doit respecter le schéma.", "@serverAddressMustIncludeAScheme": {}, "serverAddressMustNotBeEmpty": "L'adresse du serveur ne doit pas être vide.", "@serverAddressMustNotBeEmpty": {}, "signIn": "Se connecter", "@signIn": {}, "loginPageSignInTitle": "Se connecter", "@loginPageSignInTitle": {}, "signInToServer": "Se connecter à {serverAddress}", "@signInToServer": {"placeholders": {"serverAddress": {}}}, "connectToPaperless": "Connecté à Paperless", "@connectToPaperless": {}, "username": "Identifiant", "@username": {}, "usernameMustNotBeEmpty": "L'identifiant ne doit pas être vide.", "@usernameMustNotBeEmpty": {}, "documentContainsAllOfTheseWords": "Le document contient tous ces mots", "@documentContainsAllOfTheseWords": {}, "all": "<PERSON>ut", "@all": {}, "documentContainsAnyOfTheseWords": "Le document contient l'un de ces mots", "@documentContainsAnyOfTheseWords": {}, "any": "L'un des mots", "@any": {}, "learnMatchingAutomatically": "Apprentissage automatique du rapprochement", "@learnMatchingAutomatically": {}, "auto": "Auto", "@auto": {}, "documentContainsThisString": "Le document contient cette chaîne de caractère", "@documentContainsThisString": {}, "exact": "Exact", "@exact": {}, "documentContainsAWordSimilarToThisWord": "Le document contient un mot similaire à ce mot", "@documentContainsAWordSimilarToThisWord": {}, "fuzzy": "<PERSON><PERSON> approxima<PERSON>f", "@fuzzy": {}, "documentMatchesThisRegularExpression": "Le document correspond à cette expression régulière", "@documentMatchesThisRegularExpression": {}, "regularExpression": "Expression Régulière", "@regularExpression": {}, "anInternetConnectionCouldNotBeEstablished": "Impossible d'établir une connexion internet.", "@anInternetConnectionCouldNotBeEstablished": {}, "done": "Fait", "@done": {}, "next": "Suivant", "@next": {}, "couldNotAccessReceivedFile": "Impossible d'accéder au fichier reçu. Veuillez essayer d'ouvrir l'application avant le partage.", "@couldNotAccessReceivedFile": {}, "newView": "Nouvelle Vue", "@newView": {}, "createsASavedViewBasedOnTheCurrentFilterCriteria": "C<PERSON>er une nouvelle vue basée sur les critères de recherches actuels.", "@createsASavedViewBasedOnTheCurrentFilterCriteria": {}, "createViewsToQuicklyFilterYourDocuments": "Créer des vues pour filtrer rapidement vos documents.", "@createViewsToQuicklyFilterYourDocuments": {}, "nFiltersSet": "{count, plural, zero{{count} filtres actifs} one{{count} filtre actif} other{{count} filtres actifs}}", "@nFiltersSet": {"placeholders": {"count": {}}}, "showInSidebar": "Afficher dans la barre latérale", "@showInSidebar": {}, "showOnDashboard": "Afficher sur le tableau de bord", "@showOnDashboard": {}, "views": "<PERSON><PERSON>", "@views": {}, "clearAll": "<PERSON><PERSON> retirer", "@clearAll": {}, "scan": "Scanner", "@scan": {}, "previewScan": "Prévisualiser", "@previewScan": {}, "scrollToTop": "Remonter au début", "@scrollToTop": {}, "paperlessServerVersion": "Version du serveur Paperless", "@paperlessServerVersion": {}, "darkTheme": "Thème Sombre", "@darkTheme": {}, "lightTheme": "<PERSON>h<PERSON>", "@lightTheme": {}, "systemTheme": "Utiliser le thème du système", "@systemTheme": {}, "appearance": "Apparence", "@appearance": {}, "languageAndVisualAppearance": "Langage et apparence", "@languageAndVisualAppearance": {}, "applicationSettings": "Application", "@applicationSettings": {}, "colorSchemeHint": "Choisissez entre une palette de couleurs inspirée par le vert Paperless traditionnel, ou utilisez la palette de couleur dynamique basée sur le thème système.", "@colorSchemeHint": {}, "colorSchemeNotSupportedWarning": "Le thème dynamique n'est supporté que sur les appareils sous Android 12 ou plus. Sélectionner l'option 'Dynamique' pourrait ne pas avoir d'effet en fonction de l'implémentation de votre système d'exploitation.", "@colorSchemeNotSupportedWarning": {}, "colors": "Couleurs", "@colors": {}, "language": "Langage", "@language": {}, "security": "Sécurité", "@security": {}, "mangeFilesAndStorageSpace": "<PERSON><PERSON>rer les fichiers et l'espace de stockage", "@mangeFilesAndStorageSpace": {}, "storage": "Stockage", "@storage": {}, "dark": "Sombre", "@dark": {}, "light": "<PERSON>", "@light": {}, "system": "Système", "@system": {}, "ascending": "Croissant", "@ascending": {}, "descending": "Décroissant", "@descending": {}, "storagePathDay": "jour", "@storagePathDay": {}, "storagePathMonth": "mois", "@storagePathMonth": {}, "storagePathYear": "ann<PERSON>", "@storagePathYear": {}, "color": "<PERSON><PERSON><PERSON>", "@color": {}, "filterTags": "Filtrer les étiquettes...", "@filterTags": {}, "inboxTag": "Étiquette de boîte de ré<PERSON>", "@inboxTag": {}, "uploadInferValuesHint": "Si vous spécifiez des valeurs pour ces champs, votre instance Paperless ne dérivera pas automatiquement une valeur. Si vous voulez que ces valeurs soient automatiquement remplies par votre serveur, laissez les champs vides.", "@uploadInferValuesHint": {}, "useTheConfiguredBiometricFactorToAuthenticate": "Utiliser le facteur biométrique pour vous authentifier et déverrouiller vos documents.", "@useTheConfiguredBiometricFactorToAuthenticate": {}, "verifyYourIdentity": "Vérifiez votre identité", "@verifyYourIdentity": {}, "verifyIdentity": "Vérifier l'identité", "@verifyIdentity": {}, "detailed": "Dé<PERSON>lé", "@detailed": {}, "grid": "Grille", "@grid": {}, "list": "Liste", "@list": {}, "remove": "<PERSON><PERSON><PERSON>", "removeQueryFromSearchHistory": "Retirer la requête de l'historique de recherche ?", "dynamicColorScheme": "Dynamique", "@dynamicColorScheme": {}, "classicColorScheme": "Classique", "@classicColorScheme": {}, "notificationDownloadComplete": "Téléchargement terminé", "@notificationDownloadComplete": {"description": "Notification title when a download has been completed."}, "notificationDownloadingDocument": "Téléchargement du document", "@notificationDownloadingDocument": {"description": "Notification title shown when a document download is pending"}, "archiveSerialNumberUpdated": "Numéro de série de l’archive mis à jour.", "@archiveSerialNumberUpdated": {"description": "Message shown when the ASN has been updated."}, "donateCoffee": "Soutenez-moi", "@donateCoffee": {"description": "Label displayed in the app drawer"}, "thisFieldIsRequired": "Ce champ est obligatoire !", "@thisFieldIsRequired": {"description": "Message shown below the form field when a required field has not been filled out."}, "confirm": "Confirmer", "confirmAction": "Confirmer l’action", "@confirmAction": {"description": "Typically used as a title to confirm a previously selected action"}, "areYouSureYouWantToContinue": "Êtes-vous sûr(e) de vouloir continuer ?", "bulkEditTagsAddMessage": "{count, plural, one{Cette opération va ajouter les balises {tags} au document sélectionné} other{Cette opération va ajouter les balises {tags} à {count} documents sélectionnés!}}", "@bulkEditTagsAddMessage": {"description": "Message of the confirmation dialog when bulk adding tags."}, "bulkEditTagsRemoveMessage": "{count, plural, one{Cette opération supprimera les balises {tags} du document sélectionné.} other{Cette opération supprimera les balises {tags} de {count} documents sélectionnés!}}", "@bulkEditTagsRemoveMessage": {"description": "Message of the confirmation dialog when bulk removing tags."}, "bulkEditTagsModifyMessage": "{count, plural, one{Cette opération va ajouter les balises {addTags} et supprimer les balises {removeTags} du document sélectionné} other{Cette opération va ajouter les balises {addTags} et supprimer les balises {removeTags} de {count} documents sélectionnés.}}", "@bulkEditTagsModifyMessage": {"description": "Message of the confirmation dialog when both adding and removing tags."}, "bulkEditCorrespondentAssignMessage": "{count, plural, one{Cette opération assignera le correspondant {correspondent} au document sélectionné} other{Cette opération va assigner le correspondant {correspondent} à {count} documents sélectionnés!}}", "bulkEditDocumentTypeAssignMessage": "{count, plural, one{Cette opération assignera le type de document {docType} au document sélectionné.} other{Cette opération va assigner le type de document {docType} à {count} documents sélectionnés.}}", "bulkEditStoragePathAssignMessage": "{count, plural, one{Cette opération assignera le chemin de stockage {path} au document sélectionné.} other{Cette opération va assigner le chemin de stockage {path} à {count} documents sélectionnés.}}", "bulkEditCorrespondentRemoveMessage": "{count, plural, one{Cette opération va supprimer le correspondant du document sélectionné.} other{Cette opération va supprimer le correspondant de {count} documents sélectionnés.}}", "bulkEditDocumentTypeRemoveMessage": "{count, plural, one{Cette opération va supprimer le type de document du document sélectionné.} other{Cette opération va supprimer le type de document de {count} documents sélectionnés.}}", "bulkEditStoragePathRemoveMessage": "{count, plural, one{Cette opération supprimera le chemin de stockage du document sélectionné.} other{Cette opération supprimera le chemin de stockage de {count} documents sélectionnés.}}", "anyTag": "Tous", "@anyTag": {"description": "Label shown when any tag should be filtered"}, "allTags": "Tous", "@allTags": {"description": "Label shown when a document has to be assigned to all selected tags"}, "switchingAccountsPleaseWait": "Changement de compte. Veuillez patienter...", "@switchingAccountsPleaseWait": {"description": "Message shown while switching accounts is in progress."}, "testConnection": "Vérifier la connexion", "@testConnection": {"description": "Button label shown on login page. Allows user to test whether the server is reachable or not."}, "accounts": "<PERSON><PERSON><PERSON>", "@accounts": {"description": "Title of the account management dialog"}, "addAccount": "Ajouter un compte", "@addAccount": {"description": "Label of add account action"}, "switchAccount": "Basculer", "@switchAccount": {"description": "Label for switch account action"}, "logout": "Déconnexion", "@logout": {"description": "Generic Logout label"}, "switchAccountTitle": "Changer de compte", "@switchAccountTitle": {"description": "Title of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "switchToNewAccount": "Voulez-vous basculer vers le nouveau compte ? Vous pouvez revenir à tout moment.", "@switchToNewAccount": {"description": "Content of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "sourceCode": "Code source", "findTheSourceCodeOn": "Trouvez le code source sur", "@findTheSourceCodeOn": {"description": "Text before link to Paperless Mobile GitHub"}, "rememberDecision": "Se souvenir de mon choix", "defaultDownloadFileType": "Type de fichier par défaut", "@defaultDownloadFileType": {"description": "Label indicating the default filetype to download (one of archived, original and always ask)"}, "defaultShareFileType": "Type de fichier par défaut pour le partage", "@defaultShareFileType": {"description": "Label indicating the default filetype to share (one of archived, original and always ask)"}, "alwaysAsk": "Toujours demander", "@alwaysAsk": {"description": "Option to choose when the app should always ask the user which filetype to use"}, "disableMatching": "Ne pas étiqueter les documents automatiquement", "@disableMatching": {"description": "One of the options for automatic tagging of documents"}, "none": "Aucun", "@none": {"description": "One of available enum values of matching algorithm for tags"}, "logInToExistingAccount": "Se connecter à un compte existant", "@logInToExistingAccount": {"description": "Title shown on login page if at least one user is already known to the app."}, "print": "<PERSON><PERSON><PERSON><PERSON>", "@print": {"description": "Tooltip for print button"}, "managePermissions": "<PERSON><PERSON><PERSON> les permissions", "@managePermissions": {"description": "Button which leads user to manage permissions page"}, "errorRetrievingServerVersion": "Une erreur est survenue en essayant de résoudre la version du serveur.", "@errorRetrievingServerVersion": {"description": "Message shown at the bottom of the settings page when the remote server version could not be resolved."}, "resolvingServerVersion": "Résolution de la version du serveur...", "@resolvingServerVersion": {"description": "Message shown while the app is loading the remote server version."}, "goToLogin": "Se connecter", "@goToLogin": {"description": "Label of the button shown on the login page to skip logging in to existing accounts and navigate user to login page"}, "export": "Exporter", "@export": {"description": "Label for button that exports scanned images to pdf (before upload)"}, "invalidFilenameCharacter": "<PERSON><PERSON><PERSON>(s) invalide(s) trouvé dans le nom du fichier : {characters}", "@invalidFilenameCharacter": {"description": "For validating filename in export dialogue"}, "exportScansToPdf": "Exporter les scans en PDF", "@exportScansToPdf": {"description": "title of the alert dialog when exporting scans to pdf"}, "allScansWillBeMerged": "Tous les scans seront fusionnés en un seul fichier PDF.", "behavior": "Comportement", "@behavior": {"description": "Title of the settings concerning app beahvior"}, "theme": "Thème", "@theme": {"description": "Title of the theme mode setting"}, "clearCache": "Vider le cache", "@clearCache": {"description": "Title of the clear cache setting"}, "freeBytes": "{byteString} libres", "@freeBytes": {"description": "Text shown for clear storage settings"}, "calculatingDots": "Calcul en cours...", "@calculatingDots": {"description": "Text shown when the byte size is still being calculated"}, "freedDiskSpace": "{bytes} d'espace disque libérés avec succès.", "@freedDiskSpace": {"description": "Message shown after clearing storage"}, "uploadScansAsPdf": "Charger les scans au format PDF", "@uploadScansAsPdf": {"description": "Title of the setting which toggles whether scans are always uploaded as pdf"}, "convertSinglePageScanToPdf": "Toujours convertir les scans d'une page en PDF avant de charger le document", "@convertSinglePageScanToPdf": {"description": "description of the upload scans as pdf setting"}, "loginRequiredPermissionsHint": "L'utilisation de Paperless Mobile nécessite un ensemble minimal d'autorisations utilisateur depuis la version 1.14.0 et supérieure. Par conséquent, assurez-vous que l'utilisateur connecté a la permission de voir les autres utilisateurs (Utilisateur → Affichage) et les paramètres (UISettings → Affichage). Si vous ne disposez pas de ces autorisations, veuillez contacter un administrateur de votre serveur paperless-ngx.", "@loginRequiredPermissionsHint": {"description": "Hint shown on the login page informing the user of the required permissions to use the app."}, "missingPermissions": "Vous n'avez pas les permissions nécessaires pour faire cette action.", "@missingPermissions": {"description": "Message shown in a snackbar when a user without the reequired permissions performs an action."}, "editView": "", "@editView": {"description": "Title of the edit saved view page"}, "donate": "Donations", "@donate": {"description": "Label of the in-app donate button"}, "donationDialogContent": "Merci d'avoir envisagé de soutenir cette application ! En raison des politiques de paiement de Google et d'Apple, aucun lien menant aux dons ne peut être affiché dans l'application. Même un lien vers la page du dépôt du projet ne semble pas autorisé dans ce contexte. Par conséquent, jetez peut-être un coup d'oeil à la section « Donations » dans le README du projet. Votre soutien est très apprécié et maintient en vie le développement de cette application. Merci !", "@donationDialogContent": {"description": "Text displayed in the donation dialog"}, "noDocumentsFound": "Aucun document trouvé.", "@noDocumentsFound": {"description": "Message shown when no documents were found."}, "couldNotDeleteCorrespondent": "Impossible de supprimer le correspondant, veuil<PERSON>z réessayer.", "@couldNotDeleteCorrespondent": {"description": "Message shown in snackbar when a correspondent could not be deleted."}, "couldNotDeleteDocumentType": "Impossible de supprimer ce type de document, veuillez réessayer.", "@couldNotDeleteDocumentType": {"description": "Message shown when a document type could not be deleted"}, "couldNotDeleteTag": "Impossible de supprimer l'étiquette, veuil<PERSON><PERSON> réessayer.", "@couldNotDeleteTag": {"description": "Message shown when a tag could not be deleted"}, "couldNotDeleteStoragePath": "Impossible de supprimer le chemin de stockage, veuil<PERSON><PERSON> réessayer.", "@couldNotDeleteStoragePath": {"description": "Message shown when a storage path could not be deleted"}, "couldNotUpdateCorrespondent": "Impossible de mettre à jour le correspondant, veuil<PERSON><PERSON> réessayer.", "@couldNotUpdateCorrespondent": {"description": "Message shown when a correspondent could not be updated"}, "couldNotUpdateDocumentType": "Impossible de mettre à jour le type de document, veuil<PERSON>z réessayer.", "@couldNotUpdateDocumentType": {"description": "Message shown when a document type could not be updated"}, "couldNotUpdateTag": "Impossible de mettre à jour l'étiquette, ve<PERSON><PERSON><PERSON> réessayer.", "@couldNotUpdateTag": {"description": "Message shown when a tag could not be updated"}, "couldNotLoadServerInformation": "Impossible de charger les informations du serveur.", "@couldNotLoadServerInformation": {"description": "Message shown when the server information could not be loaded"}, "couldNotLoadStatistics": "Impossible de charger les statistiques du serveur.", "@couldNotLoadStatistics": {"description": "Message shown when the server statistics could not be loaded"}, "couldNotLoadUISettings": "Impossible de charger les paramètres de l'interface.", "@couldNotLoadUISettings": {"description": "Message shown when the UI settings could not be loaded"}, "couldNotLoadTasks": "Impossible de charger les tâches.", "@couldNotLoadTasks": {"description": "Message shown when the tasks (e.g. document consumed) could not be loaded"}, "userNotFound": "L'utilisateur ne peut pas être trouvé.", "@userNotFound": {"description": "Message shown when the specified user (e.g. by id) could not be found"}, "couldNotUpdateSavedView": "Impossible de mettre à jour la vue, veuil<PERSON><PERSON> réessayer.", "@couldNotUpdateSavedView": {"description": "Message shown when a saved view could not be updated"}, "couldNotUpdateStoragePath": "Impossible de mettre à jour le chemin de stockage, veuil<PERSON><PERSON> réessayer.", "savedViewSuccessfullyUpdated": "Vue enregistrée mise à jour avec succès.", "@savedViewSuccessfullyUpdated": {"description": "Message shown when a saved view was successfully updated."}, "discardChanges": "Annuler les modifications ?", "@discardChanges": {"description": "Title of the alert dialog shown when a user tries to close a view with unsaved changes."}, "savedViewChangedDialogContent": "Les conditions de filtre de la vue active ont changé. En réinitialisant le filtre, ces modifications seront perdues. Voulez-vous continuer ?", "@savedViewChangedDialogContent": {"description": "Content of the alert dialog shown when all of the following applies:\r\n* User has saved view selected\r\n* User has performed changes to the current document filter\r\n* User now tries to reset this filter without having saved the changes to the view."}, "createFromCurrentFilter": "Créer à partir du filtre actuel", "@createFromCurrentFilter": {"description": "Tooltip of the \"New saved view\" button"}, "home": "Accueil", "@home": {"description": "Label of the \"Home\" route"}, "welcomeUser": "Bienvenue {name} !", "@welcomeUser": {"description": "Top message shown on the home page"}, "statistics": "Statistiques", "documentsInInbox": "Documents dans la boîte de réception", "totalDocuments": "Nombre total de documents", "totalCharacters": "Nombre total de caractères", "showAll": "<PERSON><PERSON> afficher", "@showAll": {"description": "Button label shown on a saved view preview to open this view in the documents page"}, "userAlreadyExists": "Cet utilisateur existe déjà.", "@userAlreadyExists": {"description": "Error message shown when the user tries to add an already existing account."}, "youDidNotSaveAnyViewsYet": "Vous n'avez pas encore enregistré de vues, créez en une et elle sera affichée ici.", "@youDidNotSaveAnyViewsYet": {"description": "Message shown when there are no saved views yet."}, "tryAgain": "<PERSON><PERSON><PERSON>z réessayer", "discardFile": "Abandon<PERSON> le fichier ?", "discard": "<PERSON><PERSON><PERSON><PERSON>", "backToLogin": "Retour à la page de connexion", "skipEditingReceivedFiles": "Passer l'édition des fichiers reçus", "uploadWithoutPromptingUploadForm": "Toujours mettre en ligne sans montrer le formulaire de mise en ligne lors du partage de fichiers avec l'application.", "authenticatingDots": "Authentification en cours...", "@authenticatingDots": {"description": "Message shown when the app is authenticating the user"}, "persistingUserInformation": "Sauvegarde des informations utilisateur...", "fetchingUserInformation": "Récupération des informations utilisateur...", "@fetchingUserInformation": {"description": "Message shown when the app loads user data from the server"}, "restoringSession": "Restauration de la session...", "@restoringSession": {"description": "Message shown when the user opens the app and the previous user is tried to be authenticated and logged in"}, "documentsAssigned": "{count, plural, zero{Pas de document} one{1 document} other{{count} documents}}", "@documentsAssigned": {"description": "Text shown with a correspondent, document type etc. to indicate the number of documents this filter will maximally yield."}, "discardChangesWarning": "Vous avez des modifications non enregistrées. En continuant, toutes les modifications seront perdues. Voulez-vous abandonner ces modifications ?", "@discardChangesWarning": {"description": "Warning message shown when the user tries to close a route without saving the changes."}, "changelog": "Notes de version", "noLogsFoundOn": "Aucun journal trouvé sur {date}.", "logfileBottomReached": "Vous avez atteint le bas de ce fichier journal.", "appLogs": "Journaux d'application {date}", "saveLogsToFile": "Enregistrer le fichier journal", "copyToClipboard": "Copier dans le presse-papier", "couldNotLoadLogfileFrom": "Impossible de charger le fichier journal depuis {date}.", "loadingLogsFrom": "Chargement des journaux depuis {date}...", "clearLogs": "Effacer les journaux de {date}", "showPdf": "Afficher le PDF", "@showPdf": {"description": "Tooltip shown on the \"show pdf\" button on the document edit page"}, "hidePdf": "Masquer le PDF", "@hidePdf": {"description": "Tooltip shown on the \"show pdf\" icon button on the document edit page"}, "misc": "Sonstige", "loggingOut": "Déconnexion...", "testingConnection": "Vérifier la connexion...", "@testingConnection": {"description": "Text shown while the app tries to establish a connection to the specified host."}, "version": "Version {versionCode}", "notes": "{count, plural, zero{Notes} one{Note} other{Notes}}", "addNote": "Ajouter une note", "newerVersionAvailable": "Newer version available:", "dateOutOfRange": "Date must be between {firstDate} and {lastDate}.", "@dateOutOfRange": {"description": "Error message shown when the user tries to select a date outside of the allowed range.", "placeholders": {"firstDate": {"type": "DateTime", "format": "yMd"}, "lastDate": {"type": "DateTime", "format": "yMd"}}}, "permissions": "Permissions", "newNote": "New note", "notesMarkdownSyntaxSupportHint": "Paperless Mobile can render notes using basic markdown syntax. Try it out!"}
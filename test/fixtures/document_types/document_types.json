[{"id": 1, "slug": "rechnung", "name": "<PERSON><PERSON><PERSON><PERSON>", "match": "rechnung", "matching_algorithm": 1, "is_insensitive": true, "document_count": 5}, {"id": 2, "slug": "vertrag", "name": "Vertrag", "match": "vertrag", "matching_algorithm": 1, "is_insensitive": true, "document_count": 1}, {"id": 3, "slug": "finanzunterlagen", "name": "Finanzunterlagen", "match": "finanz", "matching_algorithm": 1, "is_insensitive": true, "document_count": 2}, {"id": 4, "slug": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "match": "<PERSON><PERSON><PERSON><PERSON>", "matching_algorithm": 1, "is_insensitive": true, "document_count": 1}, {"id": 5, "slug": "paper", "name": "Paper", "match": "paper", "matching_algorithm": 1, "is_insensitive": true, "document_count": 1}]
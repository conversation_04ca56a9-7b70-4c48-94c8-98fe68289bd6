{"developedBy": "Developed by {name}.", "@developedBy": {"placeholders": {"name": {}}}, "addAnotherAccount": "Add another account", "@addAnotherAccount": {}, "account": "Account", "@account": {}, "addCorrespondent": "Yeni ek yazar", "@addCorrespondent": {"description": "Title when adding a new correspondent"}, "addDocumentType": "Yeni Belge Türü", "@addDocumentType": {"description": "Title when adding a new document type"}, "addStoragePath": "<PERSON><PERSON>", "@addStoragePath": {"description": "Title when adding a new storage path"}, "addTag": "Yeni Etiket", "@addTag": {"description": "Title when adding a new tag"}, "aboutThisApp": "Bu uygulama hakkında", "@aboutThisApp": {"description": "Label for about this app tile displayed in the drawer"}, "loggedInAs": "<PERSON><PERSON><PERSON> giriş yapıldı {name}", "@loggedInAs": {"placeholders": {"name": {}}}, "disconnect": "Bağlantıyı kes", "@disconnect": {"description": "Logout button label"}, "reportABug": "<PERSON><PERSON>", "@reportABug": {}, "settings": "<PERSON><PERSON><PERSON>", "@settings": {}, "authenticateOnAppStart": "Uygulama başlangıcında kimlik doğrulaması yapın", "@authenticateOnAppStart": {"description": "Description of the biometric authentication settings tile"}, "biometricAuthentication": "Biyometrik kimlik doğrulama", "@biometricAuthentication": {}, "authenticateToToggleBiometricAuthentication": "{mode, select, enable{Biyometrik kimlik doğrulamayı etkinleştirmek için kimlik doğrulaması yapın} disable{Biyometrik kimlik doğrulamayı devre dışı bırakmak için kimlik doğrulaması yapın} other{}}", "@authenticateToToggleBiometricAuthentication": {"placeholders": {"mode": {}}}, "documents": "Dök<PERSON><PERSON><PERSON>", "@documents": {}, "inbox": "<PERSON><PERSON><PERSON>", "@inbox": {}, "labels": "<PERSON><PERSON><PERSON><PERSON>", "@labels": {}, "scanner": "Tarayıcı", "@scanner": {}, "startTyping": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>n...", "@startTyping": {}, "doYouReallyWantToDeleteThisView": "<PERSON>u görünümü gerçekten silmek istiyor musunuz?", "@doYouReallyWantToDeleteThisView": {}, "deleteView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sil {name}?", "@deleteView": {}, "addedAt": "Added at", "@addedAt": {}, "archiveSerialNumber": "Arşiv Seri Numarası", "@archiveSerialNumber": {}, "asn": "ASN", "@asn": {}, "correspondent": "<PERSON><PERSON>", "@correspondent": {}, "createdAt": "Created at", "@createdAt": {}, "documentSuccessfullyDeleted": "<PERSON>k<PERSON>man ba<PERSON><PERSON><PERSON><PERSON>.", "@documentSuccessfullyDeleted": {}, "assignAsn": "Assign ASN", "@assignAsn": {}, "deleteDocumentTooltip": "Delete", "@deleteDocumentTooltip": {"description": "Tooltip shown for the delete button on details page"}, "downloadDocumentTooltip": "Download", "@downloadDocumentTooltip": {"description": "Tooltip shown for the download button on details page"}, "editDocumentTooltip": "Edit", "@editDocumentTooltip": {"description": "Tooltip shown for the edit button on details page"}, "loadFullContent": "<PERSON><PERSON><PERSON> içeriği <PERSON>", "@loadFullContent": {}, "noAppToDisplayPDFFilesFound": "No app to display PDF files found!", "@noAppToDisplayPDFFilesFound": {}, "openInSystemViewer": "Open in system viewer", "@openInSystemViewer": {}, "couldNotOpenFilePermissionDenied": "Could not open file: Permission denied.", "@couldNotOpenFilePermissionDenied": {}, "previewTooltip": "Preview", "@previewTooltip": {"description": "Tooltip shown for the preview button on details page"}, "shareTooltip": "Share", "@shareTooltip": {"description": "Tooltip shown for the share button on details page"}, "similarDocuments": "<PERSON><PERSON>", "@similarDocuments": {"description": "Label shown in the tabbar on details page"}, "content": "İçerik", "@content": {"description": "Label shown in the tabbar on details page"}, "metaData": "<PERSON><PERSON>", "@metaData": {"description": "Label shown in the tabbar on details page"}, "overview": "<PERSON><PERSON>", "@overview": {"description": "Label shown in the tabbar on details page"}, "documentType": "Dökü<PERSON> tipi", "@documentType": {}, "archivedPdf": "Archived (pdf)", "@archivedPdf": {"description": "Option to chose when downloading a document"}, "chooseFiletype": "Choose filetype", "@chooseFiletype": {}, "original": "Original", "@original": {"description": "Option to chose when downloading a document"}, "documentSuccessfullyDownloaded": "Döküman başarıyla indirildi.", "@documentSuccessfullyDownloaded": {}, "suggestions": "Öneriler:", "@suggestions": {}, "editDocument": "Dökümanı Düzenle", "@editDocument": {}, "advanced": "Gelişmiş", "@advanced": {}, "apply": "<PERSON><PERSON><PERSON><PERSON>", "@apply": {}, "extended": "Genişletilmiş", "@extended": {}, "titleAndContent": "Başlık & İçerik", "@titleAndContent": {}, "title": "Başlık", "@title": {}, "reset": "Sıfırla", "@reset": {}, "filterDocuments": "Dökümanları Filtrele", "@filterDocuments": {"description": "Title of the document filter"}, "originalMD5Checksum": "Orijinal MD5-Sağlaması", "@originalMD5Checksum": {}, "mediaFilename": "<PERSON><PERSON><PERSON>", "@mediaFilename": {}, "originalFileSize": "Orijinal <PERSON><PERSON>", "@originalFileSize": {}, "originalMIMEType": "Orijinal MIME-Tipi", "@originalMIMEType": {}, "modifiedAt": "Modified at", "@modifiedAt": {}, "preview": "<PERSON><PERSON>", "@preview": {"description": "Title of the document preview page"}, "scanADocument": "Belge tarat", "@scanADocument": {}, "noDocumentsScannedYet": "<PERSON><PERSON><PERSON>z taranan belge yok.", "@noDocumentsScannedYet": {}, "or": "yada", "@or": {"description": "Used on the scanner page between both main actions when no scans have been captured."}, "deleteAllScans": "<PERSON><PERSON>m ta<PERSON>nları sil", "@deleteAllScans": {}, "uploadADocumentFromThisDevice": "<PERSON>u cihazdan bir d<PERSON><PERSON><PERSON>", "@uploadADocumentFromThisDevice": {"description": "Button label on scanner page"}, "noMatchesFound": "No matches found.", "@noMatchesFound": {"description": "Displayed when no documents were found in the document search."}, "removeFromSearchHistory": "Remove from search history?", "@removeFromSearchHistory": {}, "results": "Results", "@results": {"description": "Label displayed above search results in document search."}, "searchDocuments": "Search documents", "@searchDocuments": {}, "resetFilter": "<PERSON><PERSON><PERSON><PERSON> sı<PERSON>ırla", "@resetFilter": {}, "lastMonth": "Geçen Ay", "@lastMonth": {}, "last7Days": "Geçmiş 7 Gün", "@last7Days": {}, "last3Months": "Geçmiş 3 Ay", "@last3Months": {}, "lastYear": "Ge<PERSON>en sene", "@lastYear": {}, "search": "Ara", "@search": {}, "documentsSuccessfullyDeleted": "Doküman<PERSON> ba<PERSON><PERSON><PERSON><PERSON>.", "@documentsSuccessfullyDeleted": {}, "thereSeemsToBeNothingHere": "Burada hiçbir şey yok gibi görünüyor...", "@thereSeemsToBeNothingHere": {}, "oops": "<PERSON><PERSON>.", "@oops": {}, "newDocumentAvailable": "Yeni d<PERSON>üman mevcut!", "@newDocumentAvailable": {}, "orderBy": "Şuna göre sırala", "@orderBy": {}, "thisActionIsIrreversibleDoYouWishToProceedAnyway": "Bu işlem geri alınamaz. <PERSON><PERSON> de devam etmek istiyor musunuz?", "@thisActionIsIrreversibleDoYouWishToProceedAnyway": {}, "confirmDeletion": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>la", "@confirmDeletion": {}, "areYouSureYouWantToDeleteTheFollowingDocuments": "{count, plural, one{Aşağıdaki belgeyi silmek istediğinizden emin misiniz?} other{Aşağıdaki belgeleri silmek istediğinizden emin misiniz?}}", "@areYouSureYouWantToDeleteTheFollowingDocuments": {"placeholders": {"count": {}}}, "countSelected": "{count} seçilmiş", "@countSelected": {"description": "Displayed in the appbar when at least one document is selected.", "placeholders": {"count": {}}}, "storagePath": "<PERSON><PERSON><PERSON>", "@storagePath": {}, "prepareDocument": "Belge hazırla", "@prepareDocument": {}, "tags": "<PERSON><PERSON><PERSON><PERSON>", "@tags": {}, "documentSuccessfullyUpdated": "Doküman başarı<PERSON> gü<PERSON>llendi.", "@documentSuccessfullyUpdated": {}, "fileName": "<PERSON><PERSON><PERSON> adı", "@fileName": {}, "synchronizeTitleAndFilename": "Başlığı ve dosya adını senkronize et", "@synchronizeTitleAndFilename": {}, "reload": "<PERSON><PERSON><PERSON>", "@reload": {}, "documentSuccessfullyUploadedProcessing": "Doküman ba<PERSON><PERSON><PERSON><PERSON>, işleniyor...", "@documentSuccessfullyUploadedProcessing": {}, "deleteLabelWarningText": "<PERSON><PERSON> et<PERSON><PERSON>, di<PERSON><PERSON> belgelere referanslar içerir. <PERSON><PERSON> et<PERSON><PERSON> si<PERSON>, tüm referanslar kaldırılacaktır. <PERSON><PERSON> etmek ister misiniz?", "@deleteLabelWarningText": {}, "couldNotAcknowledgeTasks": "Görevler kabul edilemedi.", "@couldNotAcknowledgeTasks": {}, "authenticationFailedPleaseTryAgain": "Kimlik doğrulama başar<PERSON>s<PERSON>z <PERSON>u, lütfen tekrar deney<PERSON>.", "@authenticationFailedPleaseTryAgain": {}, "anErrorOccurredWhileTryingToAutocompleteYourQuery": "Sorgunuz otomatik olarak tamamlanmaya çalışılırken bir hata oluştu.", "@anErrorOccurredWhileTryingToAutocompleteYourQuery": {}, "biometricAuthenticationFailed": "Biyometrik doğrulama başarısız oldu.", "@biometricAuthenticationFailed": {}, "biometricAuthenticationNotSupported": "Biyometrik kimlik doğrulama bu cihazda desteklenmiyor.", "@biometricAuthenticationNotSupported": {}, "couldNotBulkEditDocuments": "Dokümanlar toplu düzenlenemedi.", "@couldNotBulkEditDocuments": {}, "couldNotCreateCorrespondent": "<PERSON>k <PERSON>, lütfen tekrar den<PERSON>.", "@couldNotCreateCorrespondent": {}, "couldNotLoadCorrespondents": "<PERSON>k <PERSON> yüklenemedi.", "@couldNotLoadCorrespondents": {}, "couldNotCreateSavedView": "<PERSON>ıtlı görün<PERSON>m oluşturulamadı, lütfen tekrar deneyin.", "@couldNotCreateSavedView": {}, "couldNotDeleteSavedView": "<PERSON><PERSON>tl<PERSON> gö<PERSON><PERSON><PERSON><PERSON><PERSON>, lütfen tekrar den<PERSON>in", "@couldNotDeleteSavedView": {}, "youAreCurrentlyOffline": "Şu anda çevrimdışısınız. Lütfen internete bağlı olduğunuzdan emin olun.", "@youAreCurrentlyOffline": {}, "couldNotAssignArchiveSerialNumber": "Arşiv seri numarası atanamadı.", "@couldNotAssignArchiveSerialNumber": {}, "couldNotDeleteDocument": "<PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> tekra<PERSON> den<PERSON>.", "@couldNotDeleteDocument": {}, "couldNotLoadDocuments": "<PERSON>k<PERSON>man<PERSON>, lü<PERSON><PERSON> tekrar den<PERSON>.", "@couldNotLoadDocuments": {}, "couldNotLoadDocumentPreview": "Doküman önizlemesi yüklenemedi.", "@couldNotLoadDocumentPreview": {}, "couldNotCreateDocument": "Dok<PERSON>man olu<PERSON>, lütfen tekrar den<PERSON>in.", "@couldNotCreateDocument": {}, "couldNotLoadDocumentTypes": "<PERSON>ge türleri <PERSON>, lü<PERSON><PERSON> tekrar den<PERSON>.", "@couldNotLoadDocumentTypes": {}, "couldNotUpdateDocument": "<PERSON>k<PERSON><PERSON>, lü<PERSON><PERSON> tekrar den<PERSON>.", "@couldNotUpdateDocument": {}, "couldNotUploadDocument": "<PERSON><PERSON><PERSON><PERSON>, lü<PERSON><PERSON> tekrar den<PERSON>.", "@couldNotUploadDocument": {}, "invalidCertificateOrMissingPassphrase": "Geçersiz sertifika veya eksik parola, lütfen tekrar deneyin", "@invalidCertificateOrMissingPassphrase": {}, "couldNotLoadSavedViews": "Kayıtlı görünümler yüklenemedi.", "@couldNotLoadSavedViews": {}, "aClientCertificateWasExpectedButNotSent": "Bir müşteri sertifikası bekleniyordu ancak gönderilmedi. Lütfen geçerli bir müşteri sertifikası sağlayın.", "@aClientCertificateWasExpectedButNotSent": {}, "userIsNotAuthenticated": "Kullanıcının kimliği doğrulanmadı.", "@userIsNotAuthenticated": {}, "requestTimedOut": "Sun<PERSON>uya yapılan istek zaman aşımına uğradı.", "@requestTimedOut": {}, "anErrorOccurredRemovingTheScans": "Taramalar kaldırılırken bir hata oluştu.", "@anErrorOccurredRemovingTheScans": {}, "couldNotReachYourPaperlessServer": "Paperless sunucunuza er<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>r durumda mı?", "@couldNotReachYourPaperlessServer": {}, "couldNotLoadSimilarDocuments": "<PERSON>er belgeler yüklenemedi.", "@couldNotLoadSimilarDocuments": {}, "couldNotCreateStoragePath": "<PERSON><PERSON><PERSON> yolu o<PERSON>, lütfen tekrar deneyin.", "@couldNotCreateStoragePath": {}, "couldNotLoadStoragePaths": "<PERSON><PERSON><PERSON> yolları yüklenemedi.", "@couldNotLoadStoragePaths": {}, "couldNotLoadSuggestions": "Öneriler y<PERSON>medi.", "@couldNotLoadSuggestions": {}, "couldNotCreateTag": "Etiket oluşturulamadı, lütfen tekrar deneyin.", "@couldNotCreateTag": {}, "couldNotLoadTags": "Etiketler yü<PERSON>medi.", "@couldNotLoadTags": {}, "anUnknownErrorOccurred": "Bilinmeyen bir hata o<PERSON>.", "@anUnknownErrorOccurred": {}, "fileFormatNotSupported": "Bu dosya formatı desteklenmiyor.", "@fileFormatNotSupported": {}, "report": "RAPORLA", "@report": {}, "absolute": "<PERSON><PERSON><PERSON>", "@absolute": {}, "hintYouCanAlsoSpecifyRelativeValues": "İpucu: <PERSON><PERSON><PERSON> ta<PERSON> yan<PERSON>ıra, ge<PERSON><PERSON><PERSON> tarihe göre bir zaman aralığı da belirleyebilirsiniz.", "@hintYouCanAlsoSpecifyRelativeValues": {"description": "Displayed in the extended date range picker"}, "amount": "<PERSON><PERSON><PERSON>", "@amount": {}, "relative": "Bağıl", "@relative": {}, "last": "Son", "@last": {}, "timeUnit": "<PERSON><PERSON>", "@timeUnit": {}, "selectDateRange": "<PERSON><PERSON><PERSON>", "@selectDateRange": {}, "after": "Sonra", "@after": {}, "before": "Önce", "@before": {}, "days": "{count, plural, zero{} one{gün} other{gün<PERSON>}}", "@days": {"placeholders": {"count": {}}}, "lastNDays": "{count, plural, zero{} one{<PERSON>ün} other{Geçmiş {count} gün}}", "@lastNDays": {"placeholders": {"count": {}}}, "lastNMonths": "{count, plural, zero{} one{Geçen ay} other{Geçmiş {count} ay}}", "@lastNMonths": {"placeholders": {"count": {}}}, "lastNWeeks": "{count, plural, zero{} one{Geçen hafta} other{Geçmiş {count} hafta}}", "@lastNWeeks": {"placeholders": {"count": {}}}, "lastNYears": "{count, plural, zero{} one{Geçen yıl} other{Geçmiş {count} yıl}}", "@lastNYears": {"placeholders": {"count": {}}}, "months": "{count, plural, zero{} one{ay} other{aylar}}", "@months": {"placeholders": {"count": {}}}, "weeks": "{count, plural, zero{} one{hafta} other{haftalar}}", "@weeks": {"placeholders": {"count": {}}}, "years": "{count, plural, zero{} one{yıl} other{yıllar}}", "@years": {"placeholders": {"count": {}}}, "gotIt": "Başarılı!", "@gotIt": {}, "cancel": "İptal", "@cancel": {}, "close": "Close", "@close": {}, "create": "<PERSON><PERSON>", "@create": {}, "delete": "Sil", "@delete": {}, "edit": "<PERSON><PERSON><PERSON><PERSON>", "@edit": {}, "ok": "<PERSON><PERSON>", "@ok": {}, "save": "<PERSON><PERSON>", "@save": {}, "select": "Seç", "@select": {}, "saveChanges": "Değişiklikleri Kaydet", "@saveChanges": {}, "upload": "<PERSON><PERSON><PERSON>", "@upload": {}, "youreOffline": "Çevrimdışısınız.", "@youreOffline": {}, "deleteDocument": "Delete document", "@deleteDocument": {"description": "Used as an action label on each inbox item"}, "removeDocumentFromInbox": "Döküman gelen kutusundan kaldırıldı.", "@removeDocumentFromInbox": {}, "areYouSureYouWantToMarkAllDocumentsAsSeen": "Tüm belgeleri görüntülendi olarak işaretlemek istediğinizden emin misiniz? Bu, tüm gelen kutusu etiketlerini belgelerden kaldırarak, toplu bir düzenleme işlemi gerçekleştirecektir. Bu eylem geri alınamaz! Devam etmek istediğine emin misiniz?", "@areYouSureYouWantToMarkAllDocumentsAsSeen": {}, "markAllAsSeen": "Tümü görüntülendi olarak işaretlensin mi?", "@markAllAsSeen": {}, "allSeen": "<PERSON><PERSON><PERSON>", "@allSeen": {}, "markAsSeen": "Görüntülendi olarak işaretle", "@markAsSeen": {}, "refresh": "<PERSON><PERSON><PERSON>", "@refresh": {}, "youDoNotHaveUnseenDocuments": "Görüntülenmemiş belgeniz yok.", "@youDoNotHaveUnseenDocuments": {}, "quickAction": "Hızlı eylemler", "@quickAction": {}, "suggestionSuccessfullyApplied": "Öneri başarıyla gönderildi.", "@suggestionSuccessfullyApplied": {}, "today": "<PERSON><PERSON><PERSON><PERSON>", "@today": {}, "undo": "<PERSON><PERSON> al", "@undo": {}, "nUnseen": "{count} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@nUnseen": {"placeholders": {"count": {}}}, "swipeLeftToMarkADocumentAsSeen": "İpucu: B<PERSON> belgeyi görüntülendi olarak işaretlemek ve tüm gelen kutusu etiketlerini belgeden kaldırmak için sola kaydırın.", "@swipeLeftToMarkADocumentAsSeen": {}, "yesterday": "<PERSON><PERSON><PERSON>", "@yesterday": {}, "anyAssigned": "<PERSON><PERSON><PERSON> bir atanan", "@anyAssigned": {}, "noItemsFound": "Hiç bir öğe bulunamadı!", "@noItemsFound": {}, "caseIrrelevant": "İlişkisiz durum.", "@caseIrrelevant": {}, "matchingAlgorithm": "Eşleştirme Algoritması", "@matchingAlgorithm": {}, "match": "Match", "@match": {}, "name": "İsim", "@name": {}, "notAssigned": "Atanmadı", "@notAssigned": {}, "addNewCorrespondent": "Yeni ek yazar ekle", "@addNewCorrespondent": {}, "noCorrespondentsSetUp": "Herhangi bir ek yazar a<PERSON>ışsınız gibi görünüyor.", "@noCorrespondentsSetUp": {}, "correspondents": "Correspondents", "@correspondents": {}, "addNewDocumentType": "<PERSON><PERSON> türü ekle", "@addNewDocumentType": {}, "noDocumentTypesSetUp": "Ayarlanmış herhangi bir döküman türünüz yok gibi görünüyor.", "@noDocumentTypesSetUp": {}, "documentTypes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@documentTypes": {}, "addNewStoragePath": "<PERSON><PERSON> depolama yolu ekle", "@addNewStoragePath": {}, "noStoragePathsSetUp": "Ayarlanmış herhangi bir depolama yolunuz yok gibi görünüyor.", "@noStoragePathsSetUp": {}, "storagePaths": "<PERSON><PERSON><PERSON>", "@storagePaths": {}, "addNewTag": "<PERSON><PERSON> et<PERSON>t e<PERSON>", "@addNewTag": {}, "noTagsSetUp": "Ayarlanmış herhangi bir etiketiniz yok gibi görünüyor.", "@noTagsSetUp": {}, "linkedDocuments": "Bağlantılı Belgeler", "@linkedDocuments": {}, "advancedSettings": "Gelişmiş <PERSON>", "@advancedSettings": {}, "passphrase": "<PERSON><PERSON><PERSON>", "@passphrase": {}, "configureMutualTLSAuthentication": "Karşılıklı TLS Kimlik Doğrulamasını Yapılandırma", "@configureMutualTLSAuthentication": {}, "invalidCertificateFormat": "Geçersiz sertifika biçimi, yalnızca .pfx'e izin verilir", "@invalidCertificateFormat": {}, "clientcertificate": "İstemci Sertifikası", "@clientcertificate": {}, "selectFile": "<PERSON><PERSON><PERSON> seç...", "@selectFile": {}, "continueLabel": "<PERSON><PERSON>", "@continueLabel": {}, "incorrectOrMissingCertificatePassphrase": "Yanlış veya eksik sertifika parolası.", "@incorrectOrMissingCertificatePassphrase": {}, "connect": "Bağlan", "@connect": {}, "password": "Pa<PERSON><PERSON>", "@password": {}, "passwordMustNotBeEmpty": "Şifre boş bırakılamaz.", "@passwordMustNotBeEmpty": {}, "connectionTimedOut": "Bağlantı zaman aşımına uğradı.", "@connectionTimedOut": {}, "loginPageReachabilityMissingClientCertificateText": "Bir istemci sertifikası bekleniyordu ancak gönderilmedi. Lütfen bir sertifika sağlayın.", "@loginPageReachabilityMissingClientCertificateText": {}, "couldNotEstablishConnectionToTheServer": "Sunucuyla bağlantı kurulamadı.", "@couldNotEstablishConnectionToTheServer": {}, "connectionSuccessfulylEstablished": "Bağlantı başarıyla kuruldu.", "@connectionSuccessfulylEstablished": {}, "hostCouldNotBeResolved": "Ana bilgisayar çözülemedi. Lütfen sunucu adresini ve internet bağlantınızı kontrol edin.", "@hostCouldNotBeResolved": {}, "serverAddress": "<PERSON><PERSON><PERSON>", "@serverAddress": {}, "invalidAddress": "Geçersiz adres.", "@invalidAddress": {}, "serverAddressMustIncludeAScheme": "<PERSON><PERSON><PERSON> adresi bir şema içermelidir.", "@serverAddressMustIncludeAScheme": {}, "serverAddressMustNotBeEmpty": "<PERSON><PERSON><PERSON> ad<PERSON> bo<PERSON> bırakılamaz.", "@serverAddressMustNotBeEmpty": {}, "signIn": "Oturum Aç", "@signIn": {}, "loginPageSignInTitle": "Oturum Aç", "@loginPageSignInTitle": {}, "signInToServer": "{serverAddress} adresinde oturum aç", "@signInToServer": {"placeholders": {"serverAddress": {}}}, "connectToPaperless": "Paperless'a Bağlan", "@connectToPaperless": {}, "username": "Kullanıcı adı", "@username": {}, "usernameMustNotBeEmpty": "Kullanıcı adı boş bırakılamaz.", "@usernameMustNotBeEmpty": {}, "documentContainsAllOfTheseWords": "Document contains all of these words", "@documentContainsAllOfTheseWords": {}, "all": "All", "@all": {}, "documentContainsAnyOfTheseWords": "Document contains any of these words", "@documentContainsAnyOfTheseWords": {}, "any": "Any", "@any": {}, "learnMatchingAutomatically": "Learn matching automatically", "@learnMatchingAutomatically": {}, "auto": "Auto", "@auto": {}, "documentContainsThisString": "Document contains this string", "@documentContainsThisString": {}, "exact": "Exact", "@exact": {}, "documentContainsAWordSimilarToThisWord": "Document contains a word similar to this word", "@documentContainsAWordSimilarToThisWord": {}, "fuzzy": "Fuzzy", "@fuzzy": {}, "documentMatchesThisRegularExpression": "Document matches this regular expression", "@documentMatchesThisRegularExpression": {}, "regularExpression": "Regular Expression", "@regularExpression": {}, "anInternetConnectionCouldNotBeEstablished": "İnternet bağlantısı kurulamadı.", "@anInternetConnectionCouldNotBeEstablished": {}, "done": "<PERSON><PERSON>", "@done": {}, "next": "İleri", "@next": {}, "couldNotAccessReceivedFile": "Alınan dosyaya erişilemedi. Lütfen paylaşmadan önce uygulamayı açmayı deneyin.", "@couldNotAccessReceivedFile": {}, "newView": "<PERSON><PERSON>", "@newView": {}, "createsASavedViewBasedOnTheCurrentFilterCriteria": "Geçerli filtre ölçütlerine göre yeni bir görünüm oluşturur.", "@createsASavedViewBasedOnTheCurrentFilterCriteria": {}, "createViewsToQuicklyFilterYourDocuments": "Belgelerinizi hızla filtrelemek için görünümler oluşturun.", "@createViewsToQuicklyFilterYourDocuments": {}, "nFiltersSet": "{count, plural, zero{{count} filters set} one{{count} filter set} other{{count} filters set}}", "@nFiltersSet": {"placeholders": {"count": {}}}, "showInSidebar": "<PERSON><PERSON>ö<PERSON>", "@showInSidebar": {}, "showOnDashboard": "<PERSON><PERSON><PERSON>", "@showOnDashboard": {}, "views": "Kayıtlı Görünümler", "@views": {}, "clearAll": "Clear all", "@clearAll": {}, "scan": "Tara", "@scan": {}, "previewScan": "Preview", "@previewScan": {}, "scrollToTop": "Scroll to top", "@scrollToTop": {}, "paperlessServerVersion": "Paperless sunucu versi<PERSON>u", "@paperlessServerVersion": {}, "darkTheme": "<PERSON><PERSON>", "@darkTheme": {}, "lightTheme": "Aydınlık <PERSON>", "@lightTheme": {}, "systemTheme": "Sistem temasını kullan", "@systemTheme": {}, "appearance": "G<PERSON>rü<PERSON><PERSON><PERSON>", "@appearance": {}, "languageAndVisualAppearance": "Dil ve görsel gö<PERSON><PERSON>", "@languageAndVisualAppearance": {}, "applicationSettings": "Uygulama", "@applicationSettings": {}, "colorSchemeHint": "Choose between a classic color scheme inspired by a traditional Paperless green or use the dynamic color scheme based on your system theme.", "@colorSchemeHint": {}, "colorSchemeNotSupportedWarning": "Dynamic theming is only supported for devices running Android 12 and above. Selecting the 'Dynamic' option might not have any effect depending on your OS implementation.", "@colorSchemeNotSupportedWarning": {}, "colors": "Colors", "@colors": {}, "language": "Dil", "@language": {}, "security": "Güvenlik", "@security": {}, "mangeFilesAndStorageSpace": "Dosyaları ve depolama alanını yönetin", "@mangeFilesAndStorageSpace": {}, "storage": "<PERSON><PERSON><PERSON>", "@storage": {}, "dark": "<PERSON><PERSON>", "@dark": {}, "light": "Aydınlık", "@light": {}, "system": "Sistem", "@system": {}, "ascending": "Ascending", "@ascending": {}, "descending": "Descending", "@descending": {}, "storagePathDay": "g<PERSON>n", "@storagePathDay": {}, "storagePathMonth": "ay", "@storagePathMonth": {}, "storagePathYear": "yıl", "@storagePathYear": {}, "color": "Ren<PERSON>r", "@color": {}, "filterTags": "Etiketleri filtrele...", "@filterTags": {}, "inboxTag": "<PERSON><PERSON><PERSON>", "@inboxTag": {}, "uploadInferValuesHint": "Bu alanlar i<PERSON><PERSON>, paperless örneğiniz otomatik olarak bir değer türetmeyecektir. Bu değerlerin sunucunuz tarafından otomatik olarak doldurulmasını istiyorsanız, al<PERSON><PERSON><PERSON> boş bırakın.", "@uploadInferValuesHint": {}, "useTheConfiguredBiometricFactorToAuthenticate": "Belgelerinizin şifresini çözmek ve oturum açmak için biyometrik faktör kullanın.", "@useTheConfiguredBiometricFactorToAuthenticate": {}, "verifyYourIdentity": "Kimliğinizi doğrulayın", "@verifyYourIdentity": {}, "verifyIdentity": "Kimliği Doğrula", "@verifyIdentity": {}, "detailed": "Detailed", "@detailed": {}, "grid": "Grid", "@grid": {}, "list": "List", "@list": {}, "remove": "Remove", "removeQueryFromSearchHistory": "Remove query from search history?", "dynamicColorScheme": "Dynamic", "@dynamicColorScheme": {}, "classicColorScheme": "Classic", "@classicColorScheme": {}, "notificationDownloadComplete": "Download complete", "@notificationDownloadComplete": {"description": "Notification title when a download has been completed."}, "notificationDownloadingDocument": "Downloading document", "@notificationDownloadingDocument": {"description": "Notification title shown when a document download is pending"}, "archiveSerialNumberUpdated": "Archive Serial Number updated.", "@archiveSerialNumberUpdated": {"description": "Message shown when the ASN has been updated."}, "donateCoffee": "Buy me a coffee", "@donateCoffee": {"description": "Label displayed in the app drawer"}, "thisFieldIsRequired": "This field is required!", "@thisFieldIsRequired": {"description": "Message shown below the form field when a required field has not been filled out."}, "confirm": "Confirm", "confirmAction": "Confirm action", "@confirmAction": {"description": "Typically used as a title to confirm a previously selected action"}, "areYouSureYouWantToContinue": "Are you sure you want to continue?", "bulkEditTagsAddMessage": "{count, plural, one{This operation will add the tags {tags} to the selected document.} other{This operation will add the tags {tags} to {count} selected documents.}}", "@bulkEditTagsAddMessage": {"description": "Message of the confirmation dialog when bulk adding tags."}, "bulkEditTagsRemoveMessage": "{count, plural, one{This operation will remove the tags {tags} from the selected document.} other{This operation will remove the tags {tags} from {count} selected documents.}}", "@bulkEditTagsRemoveMessage": {"description": "Message of the confirmation dialog when bulk removing tags."}, "bulkEditTagsModifyMessage": "{count, plural, one{This operation will add the tags {addTags} and remove the tags {removeTags} from the selected document.} other{This operation will add the tags {addTags} and remove the tags {removeTags} from {count} selected documents.}}", "@bulkEditTagsModifyMessage": {"description": "Message of the confirmation dialog when both adding and removing tags."}, "bulkEditCorrespondentAssignMessage": "{count, plural, one{This operation will assign the correspondent {correspondent} to the selected document.} other{This operation will assign the correspondent {correspondent} to {count} selected documents.}}", "bulkEditDocumentTypeAssignMessage": "{count, plural, one{This operation will assign the document type {docType} to the selected document.} other{This operation will assign the documentType {docType} to {count} selected documents.}}", "bulkEditStoragePathAssignMessage": "{count, plural, one{This operation will assign the storage path {path} to the selected document.} other{This operation will assign the storage path {path} to {count} selected documents.}}", "bulkEditCorrespondentRemoveMessage": "{count, plural, one{This operation will remove the correspondent from the selected document.} other{This operation will remove the correspondent from {count} selected documents.}}", "bulkEditDocumentTypeRemoveMessage": "{count, plural, one{This operation will remove the document type from the selected document.} other{This operation will remove the document type from {count} selected documents.}}", "bulkEditStoragePathRemoveMessage": "{count, plural, one{This operation will remove the storage path from the selected document.} other{This operation will remove the storage path from {count} selected documents.}}", "anyTag": "Any", "@anyTag": {"description": "Label shown when any tag should be filtered"}, "allTags": "All", "@allTags": {"description": "Label shown when a document has to be assigned to all selected tags"}, "switchingAccountsPleaseWait": "Switching accounts. Please wait...", "@switchingAccountsPleaseWait": {"description": "Message shown while switching accounts is in progress."}, "testConnection": "Test connection", "@testConnection": {"description": "Button label shown on login page. Allows user to test whether the server is reachable or not."}, "accounts": "Accounts", "@accounts": {"description": "Title of the account management dialog"}, "addAccount": "Add account", "@addAccount": {"description": "Label of add account action"}, "switchAccount": "Switch", "@switchAccount": {"description": "Label for switch account action"}, "logout": "Logout", "@logout": {"description": "Generic Logout label"}, "switchAccountTitle": "Switch account", "@switchAccountTitle": {"description": "Title of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "switchToNewAccount": "Do you want to switch to the new account? You can switch back at any time.", "@switchToNewAccount": {"description": "Content of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "sourceCode": "Source Code", "findTheSourceCodeOn": "Find the source code on", "@findTheSourceCodeOn": {"description": "Text before link to Paperless Mobile GitHub"}, "rememberDecision": "Remember my decision", "defaultDownloadFileType": "Default Download File Type", "@defaultDownloadFileType": {"description": "Label indicating the default filetype to download (one of archived, original and always ask)"}, "defaultShareFileType": "Default Share File Type", "@defaultShareFileType": {"description": "Label indicating the default filetype to share (one of archived, original and always ask)"}, "alwaysAsk": "Always ask", "@alwaysAsk": {"description": "Option to choose when the app should always ask the user which filetype to use"}, "disableMatching": "Do not tag documents automatically", "@disableMatching": {"description": "One of the options for automatic tagging of documents"}, "none": "None", "@none": {"description": "One of available enum values of matching algorithm for tags"}, "logInToExistingAccount": "Log in to existing account", "@logInToExistingAccount": {"description": "Title shown on login page if at least one user is already known to the app."}, "print": "Print", "@print": {"description": "Tooltip for print button"}, "managePermissions": "Manage permissions", "@managePermissions": {"description": "Button which leads user to manage permissions page"}, "errorRetrievingServerVersion": "An error occurred trying to resolve the server version.", "@errorRetrievingServerVersion": {"description": "Message shown at the bottom of the settings page when the remote server version could not be resolved."}, "resolvingServerVersion": "Resolving server version...", "@resolvingServerVersion": {"description": "Message shown while the app is loading the remote server version."}, "goToLogin": "Go to login", "@goToLogin": {"description": "Label of the button shown on the login page to skip logging in to existing accounts and navigate user to login page"}, "export": "Export", "@export": {"description": "Label for button that exports scanned images to pdf (before upload)"}, "invalidFilenameCharacter": "Invalid character(s) found in filename: {characters}", "@invalidFilenameCharacter": {"description": "For validating filename in export dialogue"}, "exportScansToPdf": "Export scans to PDF", "@exportScansToPdf": {"description": "title of the alert dialog when exporting scans to pdf"}, "allScansWillBeMerged": "All scans will be merged into a single PDF file.", "behavior": "Behavior", "@behavior": {"description": "Title of the settings concerning app beahvior"}, "theme": "Theme", "@theme": {"description": "Title of the theme mode setting"}, "clearCache": "Clear cache", "@clearCache": {"description": "Title of the clear cache setting"}, "freeBytes": "Free {byteString}", "@freeBytes": {"description": "Text shown for clear storage settings"}, "calculatingDots": "Calculating...", "@calculatingDots": {"description": "Text shown when the byte size is still being calculated"}, "freedDiskSpace": "Successfully freed {bytes} of disk space.", "@freedDiskSpace": {"description": "Message shown after clearing storage"}, "uploadScansAsPdf": "Upload scans as PDF", "@uploadScansAsPdf": {"description": "Title of the setting which toggles whether scans are always uploaded as pdf"}, "convertSinglePageScanToPdf": "Always convert single page scans to PDF before uploading", "@convertSinglePageScanToPdf": {"description": "description of the upload scans as pdf setting"}, "loginRequiredPermissionsHint": "Using Paperless Mobile requires a minimum set of user permissions since paperless-ngx 1.14.0 and higher. Therefore, please make sure that the user to be logged in has the permission to view other users (User → View) and the settings (UISettings → View). If you do not have these permissions, please contact an administrator of your paperless-ngx server.", "@loginRequiredPermissionsHint": {"description": "Hint shown on the login page informing the user of the required permissions to use the app."}, "missingPermissions": "You do not have the necessary permissions to perform this action.", "@missingPermissions": {"description": "Message shown in a snackbar when a user without the reequired permissions performs an action."}, "editView": "Edit View", "@editView": {"description": "Title of the edit saved view page"}, "donate": "Donate", "@donate": {"description": "Label of the in-app donate button"}, "donationDialogContent": "Thank you for considering to support this app! Due to both Google's and Apple's Payment Policies, no links leading to donations may be displayed in-app. Not even linking to the project's repository page appears to be allowed in this context. Therefore, maybe have a look at the 'Donations' section in the project's README. Your support is much appreciated and keeps the development of this app alive. Thanks!", "@donationDialogContent": {"description": "Text displayed in the donation dialog"}, "noDocumentsFound": "No documents found.", "@noDocumentsFound": {"description": "Message shown when no documents were found."}, "couldNotDeleteCorrespondent": "Could not delete correspondent, please try again.", "@couldNotDeleteCorrespondent": {"description": "Message shown in snackbar when a correspondent could not be deleted."}, "couldNotDeleteDocumentType": "Could not delete document type, please try again.", "@couldNotDeleteDocumentType": {"description": "Message shown when a document type could not be deleted"}, "couldNotDeleteTag": "Could not delete tag, please try again.", "@couldNotDeleteTag": {"description": "Message shown when a tag could not be deleted"}, "couldNotDeleteStoragePath": "Could not delete storage path, please try again.", "@couldNotDeleteStoragePath": {"description": "Message shown when a storage path could not be deleted"}, "couldNotUpdateCorrespondent": "Could not update correspondent, please try again.", "@couldNotUpdateCorrespondent": {"description": "Message shown when a correspondent could not be updated"}, "couldNotUpdateDocumentType": "Could not update document type, please try again.", "@couldNotUpdateDocumentType": {"description": "Message shown when a document type could not be updated"}, "couldNotUpdateTag": "Could not update tag, please try again.", "@couldNotUpdateTag": {"description": "Message shown when a tag could not be updated"}, "couldNotLoadServerInformation": "Could not load server information.", "@couldNotLoadServerInformation": {"description": "Message shown when the server information could not be loaded"}, "couldNotLoadStatistics": "Could not load server statistics.", "@couldNotLoadStatistics": {"description": "Message shown when the server statistics could not be loaded"}, "couldNotLoadUISettings": "Could not load UI settings.", "@couldNotLoadUISettings": {"description": "Message shown when the UI settings could not be loaded"}, "couldNotLoadTasks": "Could not load tasks.", "@couldNotLoadTasks": {"description": "Message shown when the tasks (e.g. document consumed) could not be loaded"}, "userNotFound": "User could not be found.", "@userNotFound": {"description": "Message shown when the specified user (e.g. by id) could not be found"}, "couldNotUpdateSavedView": "Could not update saved view, please try again.", "@couldNotUpdateSavedView": {"description": "Message shown when a saved view could not be updated"}, "couldNotUpdateStoragePath": "Could not update storage path, please try again.", "savedViewSuccessfullyUpdated": "Saved view successfully updated.", "@savedViewSuccessfullyUpdated": {"description": "Message shown when a saved view was successfully updated."}, "discardChanges": "Discard changes?", "@discardChanges": {"description": "Title of the alert dialog shown when a user tries to close a view with unsaved changes."}, "savedViewChangedDialogContent": "The filter conditions of the active view have changed. By resetting the filter, these changes will be lost. Do you still wish to continue?", "@savedViewChangedDialogContent": {"description": "Content of the alert dialog shown when all of the following applies:\r\n* User has saved view selected\r\n* User has performed changes to the current document filter\r\n* User now tries to reset this filter without having saved the changes to the view."}, "createFromCurrentFilter": "Create from current filter", "@createFromCurrentFilter": {"description": "Tooltip of the \"New saved view\" button"}, "home": "Home", "@home": {"description": "Label of the \"Home\" route"}, "welcomeUser": "Welcome, {name}!", "@welcomeUser": {"description": "Top message shown on the home page"}, "statistics": "Statistics", "documentsInInbox": "Documents in inbox", "totalDocuments": "Total documents", "totalCharacters": "Total characters", "showAll": "Show all", "@showAll": {"description": "Button label shown on a saved view preview to open this view in the documents page"}, "userAlreadyExists": "This user already exists.", "@userAlreadyExists": {"description": "Error message shown when the user tries to add an already existing account."}, "youDidNotSaveAnyViewsYet": "You did not save any views yet, create one and it will be shown here.", "@youDidNotSaveAnyViewsYet": {"description": "Message shown when there are no saved views yet."}, "tryAgain": "Try again", "discardFile": "Discard file?", "discard": "Discard", "backToLogin": "Back to login", "skipEditingReceivedFiles": "Skip editing received files", "uploadWithoutPromptingUploadForm": "Always upload without prompting the upload form when sharing files with the app.", "authenticatingDots": "Authenticating...", "@authenticatingDots": {"description": "Message shown when the app is authenticating the user"}, "persistingUserInformation": "Persisting user information...", "fetchingUserInformation": "Fetching user information...", "@fetchingUserInformation": {"description": "Message shown when the app loads user data from the server"}, "restoringSession": "Restoring session...", "@restoringSession": {"description": "Message shown when the user opens the app and the previous user is tried to be authenticated and logged in"}, "documentsAssigned": "{count, plural, zero{No documents} one{1 document} other{{count} documents}}", "@documentsAssigned": {"description": "Text shown with a correspondent, document type etc. to indicate the number of documents this filter will maximally yield."}, "discardChangesWarning": "You have unsaved changes. By continuing, all changes will be lost. Do you want to discard these changes?", "@discardChangesWarning": {"description": "Warning message shown when the user tries to close a route without saving the changes."}, "changelog": "Changelog", "noLogsFoundOn": "No logs found on {date}.", "logfileBottomReached": "You have reached the bottom of this logfile.", "appLogs": "App logs {date}", "saveLogsToFile": "Save logs to file", "copyToClipboard": "Copy to clipboard", "couldNotLoadLogfileFrom": "Could not load logfile from {date}.", "loadingLogsFrom": "Loading logs from {date}...", "clearLogs": "Clear logs from {date}", "showPdf": "Show PDF", "@showPdf": {"description": "Tooltip shown on the \"show pdf\" button on the document edit page"}, "hidePdf": "Hide PDF", "@hidePdf": {"description": "Tooltip shown on the \"show pdf\" icon button on the document edit page"}, "misc": "Miscellaneous", "loggingOut": "Logging out...", "testingConnection": "Testing connection...", "@testingConnection": {"description": "Text shown while the app tries to establish a connection to the specified host."}, "version": "Version {versionCode}", "notes": "{count, plural, zero{Notes} one{Note} other{Notes}}", "addNote": "Add note", "newerVersionAvailable": "Newer version available:", "dateOutOfRange": "Date must be between {firstDate} and {lastDate}.", "@dateOutOfRange": {"description": "Error message shown when the user tries to select a date outside of the allowed range.", "placeholders": {"firstDate": {"type": "DateTime", "format": "yMd"}, "lastDate": {"type": "DateTime", "format": "yMd"}}}, "permissions": "Permissions", "newNote": "New note", "notesMarkdownSyntaxSupportHint": "Paperless Mobile can render notes using basic markdown syntax. Try it out!"}
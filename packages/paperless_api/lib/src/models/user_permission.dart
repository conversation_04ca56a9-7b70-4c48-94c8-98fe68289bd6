import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
part 'user_permission.g.dart';

@JsonSerializable()
class UserPermission extends Equatable {
  int? id;
  String? username;
  String? email;
  String? password;
  String? firstName;
  String? lastName;
  DateTime? dateJoined;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_staff')
  bool? isStaff;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  bool? isActive;
  bool? isSuperuser;
  List<dynamic>? groups;
  @Json<PERSON>ey(name: 'user_permissions')
  List<dynamic>? userPermissions;
  @<PERSON>son<PERSON><PERSON>(name: 'inherited_permissions')
  List<String>? inheritedPermissions;
  bool? isMfaEnabled;
  bool isSelected;

  UserPermission(
      {this.id,
      this.username,
      this.email,
      this.password,
      this.firstName,
      this.lastName,
      this.dateJoined,
      this.isStaff,
      this.isActive,
      this.isSuperuser,
      this.groups,
      this.userPermissions,
      this.inheritedPermissions,
      this.isMfaEnabled,
      this.isSelected = false});

  factory UserPermission.fromJson(Map<String, dynamic> json) =>
      _$UserPermissionFromJson(json);

  Map<String, dynamic> toJson() => _$UserPermissionToJson(this);

  /// ✅ Copy method để tạo bản mới khi cập nhật
  UserPermission copyWith({
    int? id,
    String? username,
    String? email,
    String? password,
    String? firstName,
    String? lastName,
    DateTime? dateJoined,
    bool? isStaff,
    bool? isActive,
    bool? isSuperuser,
    List<dynamic>? groups,
    List<dynamic>? userPermissions,
    List<String>? inheritedPermissions,
    bool? isMfaEnabled,
    bool? isSelected,
  }) {
    return UserPermission(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      password: password ?? this.password,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateJoined: dateJoined ?? this.dateJoined,
      isStaff: isStaff ?? this.isStaff,
      isActive: isActive ?? this.isActive,
      isSuperuser: isSuperuser ?? this.isSuperuser,
      groups: groups ?? this.groups,
      userPermissions: userPermissions ?? this.userPermissions,
      inheritedPermissions: inheritedPermissions ?? this.inheritedPermissions,
      isMfaEnabled: isMfaEnabled ?? this.isMfaEnabled,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        password,
        firstName,
        lastName,
        dateJoined,
        isStaff,
        isActive,
        isSuperuser,
        groups,
        userPermissions,
        inheritedPermissions,
        isMfaEnabled,
        isSelected,
      ];
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tags_query.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AnyAssignedTagsQueryAdapter extends TypeAdapter<AnyAssignedTagsQuery> {
  @override
  final int typeId = 103;

  @override
  AnyAssignedTagsQuery read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AnyAssignedTagsQuery(
      tagIds: (fields[0] as List).cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, AnyAssignedTagsQuery obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.tagIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnyAssignedTagsQueryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class IdsTagsQueryAdapter extends TypeAdapter<IdsTagsQuery> {
  @override
  final int typeId = 106;

  @override
  IdsTagsQuery read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return IdsTagsQuery(
      include: (fields[0] as List).cast<int>(),
      exclude: (fields[1] as List).cast<int>(),
    );
  }

  @override
  void write(BinaryWriter writer, IdsTagsQuery obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.include)
      ..writeByte(1)
      ..write(obj.exclude);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is IdsTagsQueryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

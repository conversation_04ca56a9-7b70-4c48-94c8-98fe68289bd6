import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

class ObscuredInputTextFormField extends StatefulWidget {
  final String? initialValue;
  final String label;
  final void Function(String?) onChanged;
  final FormFieldValidator<String>? validator;
  final bool enabled;
  final TextEditingController? passController;

  final ValueChanged<String?>? onFieldSubmitted;

  const ObscuredInputTextFormField(
      {super.key,
      required this.onChanged,
      required this.label,
      this.validator,
      this.initialValue,
      this.enabled = true,
      this.onFieldSubmitted,
      this.passController});

  @override
  State<ObscuredInputTextFormField> createState() =>
      _ObscuredInputTextFormFieldState();
}

class _ObscuredInputTextFormFieldState
    extends State<ObscuredInputTextFormField> {
  bool _showPassword = false;
  // late final FocusNode _passwordFocusNode;
  // bool _isInternalFocusNode = false;

  @override
  void initState() {
    super.initState();
    // _isInternalFocusNode = widget.focusNode == null;
    // _passwordFocusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: widget.passController,
      enabled: widget.enabled,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      onFieldSubmitted: widget.onFieldSubmitted,
      // validator: widget.validator,
      initialValue: widget.initialValue,
      // focusNode: _passwordFocusNode,
      obscureText: !_showPassword,
      autocorrect: false,
      onChanged: widget.onChanged,
      autofillHints: const [AutofillHints.password],
      decoration: InputDecoration(
        border: InputBorder.none,
        hintStyle: const TextStyle(fontSize: 14, color: AppColor.grey_BABABA),
        hintText: widget.label,
        suffixIcon: IconButton(
          icon: Icon(_showPassword ? Icons.visibility : Icons.visibility_off),
          onPressed: () => setState(() {
            _showPassword = !_showPassword;
          }),
        ),
      ),
    );
  }
}

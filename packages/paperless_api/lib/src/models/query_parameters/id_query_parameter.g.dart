// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'id_query_parameter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SetIdQueryParameterAdapter extends TypeAdapter<SetIdQueryParameter> {
  @override
  final int typeId = 119;

  @override
  SetIdQueryParameter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SetIdQueryParameter(
      id: fields[0] as int,
    );
  }

  @override
  void write(BinaryWriter writer, SetIdQueryParameter obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.id);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SetIdQueryParameterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

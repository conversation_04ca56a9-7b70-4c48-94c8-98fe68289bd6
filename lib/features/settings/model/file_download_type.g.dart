// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'file_download_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FileDownloadTypeAdapter extends TypeAdapter<FileDownloadType> {
  @override
  final int typeId = 10;

  @override
  FileDownloadType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 1:
        return FileDownloadType.original;
      case 2:
        return FileDownloadType.archived;
      case 3:
        return FileDownloadType.alwaysAsk;
      default:
        return FileDownloadType.original;
    }
  }

  @override
  void write(BinaryWriter writer, FileDownloadType obj) {
    switch (obj) {
      case FileDownloadType.original:
        writer.writeByte(1);
        break;
      case FileDownloadType.archived:
        writer.writeByte(2);
        break;
      case FileDownloadType.alwaysAsk:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FileDownloadTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

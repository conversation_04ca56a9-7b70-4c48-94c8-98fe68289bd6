import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/features/settings/view/widgets/global_settings_builder.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class DisableAnimationsSetting extends StatelessWidget {
  const DisableAnimationsSetting({super.key});

  @override
  Widget build(BuildContext context) {
    return GlobalSettingsBuilder(builder: (context, settings) {
      return ListTile(
        title: const Text('Disable animations'),
        subtitle: const Text(
          'Disables page transitions and most animations. '
          'Temporary workaround until system accessibility settings can be used.',
        ),
        trailing: CupertinoSwitch(
          value: settings.disableAnimations,
          activeColor: AppColor.primary,
          // trackColor: AppColor.white,
          onChanged: (val) {
            settings.disableAnimations = val;
            settings.save();
          },
        ),
        onTap: () {
          final newVal = !settings.disableAnimations;
          settings.disableAnimations = newVal;
          settings.save();
        },
      );
    });
  }
}

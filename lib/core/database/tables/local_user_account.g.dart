// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_user_account.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalUserAccountAdapter extends TypeAdapter<LocalUserAccount> {
  @override
  final int typeId = 7;

  @override
  LocalUserAccount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalUserAccount(
      id: fields[3] as String,
      serverUrl: fields[0] as String,
      settings: fields[4] as LocalUserSettings,
      paperlessUser: fields[7] as UserModel,
      apiVersion: fields[8] == null ? 2 : fields[8] as int,
    );
  }

  @override
  void write(BinaryWriter writer, LocalUserAccount obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.serverUrl)
      ..writeByte(3)
      ..write(obj.id)
      ..writeByte(4)
      ..write(obj.settings)
      ..writeByte(7)
      ..write(obj.paperlessUser)
      ..writeByte(8)
      ..write(obj.apiVersion);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalUserAccountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

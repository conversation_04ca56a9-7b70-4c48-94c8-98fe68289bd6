// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'color_scheme_option.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ColorSchemeOptionAdapter extends TypeAdapter<ColorSchemeOption> {
  @override
  final int typeId = 3;

  @override
  ColorSchemeOption read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ColorSchemeOption.classic;
      case 1:
        return ColorSchemeOption.dynamic;
      default:
        return ColorSchemeOption.classic;
    }
  }

  @override
  void write(BinaryWriter writer, ColorSchemeOption obj) {
    switch (obj) {
      case ColorSchemeOption.classic:
        writer.writeByte(0);
        break;
      case ColorSchemeOption.dynamic:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ColorSchemeOptionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

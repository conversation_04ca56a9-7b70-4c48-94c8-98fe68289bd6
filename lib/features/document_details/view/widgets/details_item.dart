import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';

class DetailsItem extends StatelessWidget {
  final String label;
  final Widget content;
  const DetailsItem({
    super.key,
    required this.label,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.textStyle12.copyWith(
              color: AppColor.grey_909090, fontWeight: FontWeight.w500),
        ),
        content,
      ],
    );
  }

  DetailsItem.text(
    String text, {
    super.key,
    required this.label,
    required BuildContext context,
  }) : content = Text(
          text,
          style: Theme.of(context).textTheme.bodyLarge,
        );
}

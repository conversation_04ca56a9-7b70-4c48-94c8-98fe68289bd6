import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/accessibility/accessibility_utils.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/widgets/shimmer_placeholder.dart';
import 'package:paperless_mobile/features/documents/view/widgets/saved_views/saved_view_chip.dart';
import 'package:paperless_mobile/features/saved_view/cubit/saved_view_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/connectivity_aware_action_wrapper.dart';
import 'package:paperless_mobile/routing/routes/saved_views_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';

class SavedViewsWidget extends StatefulWidget {
  final void Function(SavedView view) onViewSelected;
  final void Function(SavedView view) onUpdateView;
  final void Function(SavedView view) onDeleteView;
  final void Function(bool isExpanded)? onExpansionChanged;
  final void Function(void Function(bool) updateState)?
      onRegisterUpdateCallback;

  final DocumentFilter filter;
  final ExpansionTileController? controller;

  const SavedViewsWidget({
    super.key,
    required this.onViewSelected,
    required this.filter,
    required this.onUpdateView,
    required this.onDeleteView,
    this.controller,
    this.onExpansionChanged,
    this.onRegisterUpdateCallback,
  });

  @override
  State<SavedViewsWidget> createState() => _SavedViewsWidgetState();
}

class _SavedViewsWidgetState extends State<SavedViewsWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200).accessible(),
    );
    _animation = _animationController.drive(Tween(begin: 0, end: 0.5));

    // Đăng ký callback để parent có thể cập nhật trạng thái
    widget.onRegisterUpdateCallback?.call(updateExpansionState);
  }

  void updateExpansionState(bool isExpanded) {
    if (mounted) {
      setState(() {
        _isExpanded = isExpanded;
      });
      widget.onExpansionChanged?.call(isExpanded);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SavedViewCubit, SavedViewState>(
      builder: (context, state) {
        final selectedView = state.mapOrNull(
          loaded: (value) {
            if (widget.filter.selectedView != null) {
              return value.savedViews[widget.filter.selectedView!];
            }
          },
        );
        final selectedViewHasChanged = selectedView != null &&
            selectedView.toDocumentFilter() != widget.filter;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: _isExpanded ? null : 0,
          child: _isExpanded
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    state
                        .maybeMap(
                          loaded: (value) {
                            if (value.savedViews.isEmpty) {
                              return Text(
                                S.of(context)!.youDidNotSaveAnyViewsYet,
                                style: Theme.of(context).textTheme.bodySmall,
                              ).paddedOnly(left: 16);
                            }

                            return SizedBox(
                              height: kMinInteractiveDimension,
                              child: NotificationListener<ScrollNotification>(
                                onNotification: (notification) => true,
                                child: CustomScrollView(
                                  scrollDirection: Axis.horizontal,
                                  slivers: [
                                    const SliverToBoxAdapter(
                                      child: SizedBox(width: 12),
                                    ),
                                    SliverList.separated(
                                      itemBuilder: (context, index) {
                                        final view = value.savedViews.values
                                            .elementAt(index);
                                        final isSelected =
                                            (widget.filter.selectedView ??
                                                    -1) ==
                                                view.id;
                                        return ConnectivityAwareActionWrapper(
                                          child: SavedViewChip(
                                            view: view,
                                            onViewSelected:
                                                widget.onViewSelected,
                                            selected: isSelected,
                                            hasChanged: isSelected &&
                                                view.toDocumentFilter() !=
                                                    widget.filter,
                                            onUpdateView: widget.onUpdateView,
                                            onDeleteView: widget.onDeleteView,
                                          ),
                                        );
                                      },
                                      separatorBuilder: (context, index) =>
                                          const SizedBox(width: 8),
                                      itemCount: value.savedViews.length,
                                    ),
                                    const SliverToBoxAdapter(
                                      child: SizedBox(width: 12),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          error: (_) => Text(
                            S.of(context)!.couldNotLoadSavedViews,
                          ).paddedOnly(left: 16),
                          orElse: _buildLoadingState,
                        )
                        .paddedOnly(top: 16),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Tooltip(
                        message: S.of(context)!.createFromCurrentFilter,
                        child: ConnectivityAwareActionWrapper(
                          child: TextButton.icon(
                            onPressed: () {
                              CreateSavedViewRoute($extra: widget.filter)
                                  .push(context);
                            },
                            icon: const Icon(
                              Icons.add,
                              color: AppColor.primary,
                            ),
                            label: Text(S.of(context)!.newView,
                                style:
                                    const TextStyle(color: AppColor.primary)),
                          ),
                        ),
                      ).padded(4),
                    ),
                  ],
                )
              : const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      height: kMinInteractiveDimension,
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) => true,
        child: ShimmerPlaceholder(
          child: CustomScrollView(
            scrollDirection: Axis.horizontal,
            slivers: [
              const SliverToBoxAdapter(
                child: SizedBox(width: 12),
              ),
              SliverList.separated(
                itemBuilder: (context, index) {
                  return Container(
                    width: 130,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                  );
                },
                separatorBuilder: (context, index) => const SizedBox(width: 8),
              ),
              const SliverToBoxAdapter(
                child: SizedBox(width: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

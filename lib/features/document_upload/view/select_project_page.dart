import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';

class SelectProjectPage extends StatelessWidget {
  const SelectProjectPage({super.key, required this.documentUploadCubit});
  final DocumentUploadCubit documentUploadCubit;

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title: const Row(children: [
          Text(
            'Select project',
            style: AppTextStyles.textStyleAppBar,
          )
        ]),
      ),
      body: Padding(
          padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
          child: BlocBuilder<DocumentUploadCubit, DocumentUploadState>(
            builder: (context, state) {
              return ListView.separated(
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        documentUploadCubit.selectedProject(index);
                      },
                      child: Container(
                        height: 46,
                        width: size.width,
                        padding: const EdgeInsets.symmetric(horizontal: 14),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: state.projects[index].isSelected == true
                                ? AppColor.blue_024CAA.withOpacity(0.1)
                                : AppColor.white),
                        child: Row(
                          children: [
                            Text(
                              state.projects[index].name,
                              style: AppTextStyles.textStyle14
                                  .copyWith(fontWeight: FontWeight.w500),
                            ),
                            const Spacer(),
                            if (state.projects[index].isSelected == true)
                              SvgPicture.asset('assets/svgs/tick.svg')
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => const Gap(16),
                  itemCount: state.projects.length);
            },
          )),
    );
  }
}

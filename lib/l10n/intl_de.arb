{"developedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von {name}.", "@developedBy": {"placeholders": {"name": {}}}, "addAnotherAccount": "Einen Account hinzufügen", "@addAnotherAccount": {}, "account": "Account", "@account": {}, "addCorrespondent": "<PERSON><PERSON><PERSON>", "@addCorrespondent": {"description": "Title when adding a new correspondent"}, "addDocumentType": "Neuer Dokumenttyp", "@addDocumentType": {"description": "Title when adding a new document type"}, "addStoragePath": "Neuer Speicherpfad", "@addStoragePath": {"description": "Title when adding a new storage path"}, "addTag": "Neuer Tag", "@addTag": {"description": "Title when adding a new tag"}, "aboutThisApp": "Über diese App", "@aboutThisApp": {"description": "Label for about this app tile displayed in the drawer"}, "loggedInAs": "<PERSON><PERSON><PERSON><PERSON> als {name}", "@loggedInAs": {"placeholders": {"name": {}}}, "disconnect": "Verbindung trennen", "@disconnect": {"description": "Logout button label"}, "reportABug": "Einen Fehler melden", "@reportABug": {}, "settings": "Einstellungen", "@settings": {}, "authenticateOnAppStart": "Authentifizierung beim Start der Anwendung", "@authenticateOnAppStart": {"description": "Description of the biometric authentication settings tile"}, "biometricAuthentication": "Biometrische Authentifizierung", "@biometricAuthentication": {}, "authenticateToToggleBiometricAuthentication": "{mode, select, enable{Authentifizieren, um die biometrische Authentifizierung zu aktivieren} disable{Authentifizieren, um die biometrische Authentifizierung zu deaktivieren}  other{}}", "@authenticateToToggleBiometricAuthentication": {"placeholders": {"mode": {}}}, "documents": "Dokumente", "@documents": {}, "inbox": "<PERSON><PERSON><PERSON><PERSON>", "@inbox": {}, "labels": "Labels", "@labels": {}, "scanner": "Scanner", "@scanner": {}, "startTyping": "<PERSON><PERSON>ne zu tippen...", "@startTyping": {}, "doYouReallyWantToDeleteThisView": "Möchtest Du diese Ansicht wirklich löschen?", "@doYouReallyWantToDeleteThisView": {}, "deleteView": "<PERSON><PERSON><PERSON> {name} l<PERSON><PERSON>?", "@deleteView": {}, "addedAt": "Hinzugefügt am", "@addedAt": {}, "archiveSerialNumber": "Archiv-Seriennummer", "@archiveSerialNumber": {}, "asn": "ASN", "@asn": {}, "correspondent": "Korrespondent", "@correspondent": {}, "createdAt": "Ausgestellt am", "@createdAt": {}, "documentSuccessfullyDeleted": "Das Dokument wurde erfolgreich gelöscht.", "@documentSuccessfullyDeleted": {}, "assignAsn": "ASN zu<PERSON>sen", "@assignAsn": {}, "deleteDocumentTooltip": "Löschen", "@deleteDocumentTooltip": {"description": "Tooltip shown for the delete button on details page"}, "downloadDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@downloadDocumentTooltip": {"description": "Tooltip shown for the download button on details page"}, "editDocumentTooltip": "<PERSON><PERSON><PERSON>", "@editDocumentTooltip": {"description": "Tooltip shown for the edit button on details page"}, "loadFullContent": "Lade gesamten Inhalt", "@loadFullContent": {}, "noAppToDisplayPDFFilesFound": "Es wurde keine App zum Anzeigen von PDF Dateien gefunden!", "@noAppToDisplayPDFFilesFound": {}, "openInSystemViewer": "In System-<PERSON><PERSON>", "@openInSystemViewer": {}, "couldNotOpenFilePermissionDenied": "Datei konnte nicht geöffnet werden: <PERSON><PERSON><PERSON> verweigert.", "@couldNotOpenFilePermissionDenied": {}, "previewTooltip": "Vorschau", "@previewTooltip": {"description": "Tooltip shown for the preview button on details page"}, "shareTooltip": "Teilen", "@shareTooltip": {"description": "Tooltip shown for the share button on details page"}, "similarDocuments": "Similar Documents", "@similarDocuments": {"description": "Label shown in the tabbar on details page"}, "content": "Inhalt", "@content": {"description": "Label shown in the tabbar on details page"}, "metaData": "<PERSON><PERSON><PERSON>", "@metaData": {"description": "Label shown in the tabbar on details page"}, "overview": "Übersicht", "@overview": {"description": "Label shown in the tabbar on details page"}, "documentType": "Dokumenttyp", "@documentType": {}, "archivedPdf": "Archiviert (pdf)", "@archivedPdf": {"description": "Option to chose when downloading a document"}, "chooseFiletype": "<PERSON><PERSON><PERSON><PERSON>", "@chooseFiletype": {}, "original": "Original", "@original": {"description": "Option to chose when downloading a document"}, "documentSuccessfullyDownloaded": "Dokument erfolgreich heruntergeladen.", "@documentSuccessfullyDownloaded": {}, "suggestions": "Vorschläge: ", "@suggestions": {}, "editDocument": "Dokument Bearbeiten", "@editDocument": {}, "advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@advanced": {}, "apply": "<PERSON><PERSON><PERSON>", "@apply": {}, "extended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@extended": {}, "titleAndContent": "Titel & Inhalt", "@titleAndContent": {}, "title": "Titel", "@title": {}, "reset": "Z<PERSON>ücksetzen", "@reset": {}, "filterDocuments": "Dokumente filtern", "@filterDocuments": {"description": "Title of the document filter"}, "originalMD5Checksum": "MD5-Prüfsumme Original", "@originalMD5Checksum": {}, "mediaFilename": "Media-Dateiname", "@mediaFilename": {}, "originalFileSize": "Dateigröße Original", "@originalFileSize": {}, "originalMIMEType": "MIME-Typ Original", "@originalMIMEType": {}, "modifiedAt": "Geändert am", "@modifiedAt": {}, "preview": "Vorschau", "@preview": {"description": "Title of the document preview page"}, "scanADocument": "<PERSON><PERSON> ein Do<PERSON>", "@scanADocument": {}, "noDocumentsScannedYet": "<PERSON>s wurden noch keine Dokumente gescannt.", "@noDocumentsScannedYet": {}, "or": "oder", "@or": {"description": "Used on the scanner page between both main actions when no scans have been captured."}, "deleteAllScans": "Alle scans löschen", "@deleteAllScans": {}, "uploadADocumentFromThisDevice": "Lade ein Dokument von diesem Gerät hoch", "@uploadADocumentFromThisDevice": {"description": "Button label on scanner page"}, "noMatchesFound": "<PERSON><PERSON>.", "@noMatchesFound": {"description": "Displayed when no documents were found in the document search."}, "removeFromSearchHistory": "Aus dem Suchverlauf entfernen?", "@removeFromSearchHistory": {}, "results": "Ergebnisse", "@results": {"description": "Label displayed above search results in document search."}, "searchDocuments": "Durchsuche Dokumente", "@searchDocuments": {}, "resetFilter": "<PERSON><PERSON>", "@resetFilter": {}, "lastMonth": "Letzter Monat", "@lastMonth": {}, "last7Days": "Letzte 7 Tage", "@last7Days": {}, "last3Months": "Letzten 3 Monate", "@last3Months": {}, "lastYear": "Letztes Jahr", "@lastYear": {}, "search": "<PERSON><PERSON>", "@search": {}, "documentsSuccessfullyDeleted": "Das massenhafte Löschen der Dokumente war erfolgreich.", "@documentsSuccessfullyDeleted": {}, "thereSeemsToBeNothingHere": "<PERSON>s scheint nichts hier zu sein...", "@thereSeemsToBeNothingHere": {}, "oops": "Ups.", "@oops": {}, "newDocumentAvailable": "Neues Dokument verfügbar!", "@newDocumentAvailable": {}, "orderBy": "Sortiere nach", "@orderBy": {}, "thisActionIsIrreversibleDoYouWishToProceedAnyway": "Diese Aktion ist unwiderruflich. Möchtest Du trotzdem fortfahren?", "@thisActionIsIrreversibleDoYouWishToProceedAnyway": {}, "confirmDeletion": "Löschen bestätigen", "@confirmDeletion": {}, "areYouSureYouWantToDeleteTheFollowingDocuments": "{count, plural, one{<PERSON><PERSON> <PERSON>, dass Du folgendes Dokument löschen möchtest?} other{Bist <PERSON>, dass Du folgende Dokumente löschen möchtest?}}", "@areYouSureYouWantToDeleteTheFollowingDocuments": {"placeholders": {"count": {}}}, "countSelected": "{count} ausgewählt", "@countSelected": {"description": "Displayed in the appbar when at least one document is selected.", "placeholders": {"count": {}}}, "storagePath": "Speicherpfad", "@storagePath": {}, "prepareDocument": "Dokument vorbereiten", "@prepareDocument": {}, "tags": "Tags", "@tags": {}, "documentSuccessfullyUpdated": "Dokument erfolgreich aktualisiert.", "@documentSuccessfullyUpdated": {}, "fileName": "Dateiname", "@fileName": {}, "synchronizeTitleAndFilename": "Synchronisiere Titel und Dateiname", "@synchronizeTitleAndFilename": {}, "reload": "Neu laden", "@reload": {}, "documentSuccessfullyUploadedProcessing": "Das Dokument wurde erfolgreich hochgeladen. Verarbeite...", "@documentSuccessfullyUploadedProcessing": {}, "deleteLabelWarningText": "Dieser Kennzeichner wird von Dokumenten referenziert. Durch das Löschen dieses Kennzeichners werden alle Referenzen entfernt. Fortfahren?", "@deleteLabelWarningText": {}, "couldNotAcknowledgeTasks": "Dateiaufgabe konnte nicht verworfen werden.", "@couldNotAcknowledgeTasks": {}, "authenticationFailedPleaseTryAgain": "Authentifizierung fehlgeschlagen, bitte versuche es erneut.", "@authenticationFailedPleaseTryAgain": {}, "anErrorOccurredWhileTryingToAutocompleteYourQuery": "Beim automatischen Vervollständigen ist ein Fehler aufgetreten.", "@anErrorOccurredWhileTryingToAutocompleteYourQuery": {}, "biometricAuthenticationFailed": "Biometrische Authentifizierung fehlgeschlagen.", "@biometricAuthenticationFailed": {}, "biometricAuthenticationNotSupported": "Biometrische Authentifizierung wird von diesem Gerät nicht unterstützt.", "@biometricAuthenticationNotSupported": {}, "couldNotBulkEditDocuments": "Es ist ein Fehler beim massenhaften bearbeiten der Dokumente aufgetreten.", "@couldNotBulkEditDocuments": {}, "couldNotCreateCorrespondent": "Korrespondent konnte nicht erstellt werden, bitte versuche es erneut.", "@couldNotCreateCorrespondent": {}, "couldNotLoadCorrespondents": "Korrespondenten konnten nicht geladen werden.", "@couldNotLoadCorrespondents": {}, "couldNotCreateSavedView": "Gespeicherte Ansicht konnte nicht erstellt werden, bitte versuche es erneut.", "@couldNotCreateSavedView": {}, "couldNotDeleteSavedView": "Gespeicherte Ansicht konnte nicht geklöscht werden, bitte versuche es erneut.", "@couldNotDeleteSavedView": {}, "youAreCurrentlyOffline": "Du bist offline. <PERSON><PERSON> stelle sicher, dass du mit dem Internet verbunden bist.", "@youAreCurrentlyOffline": {}, "couldNotAssignArchiveSerialNumber": "Archiv-Seriennummer konnte nicht zugewiesen werden.", "@couldNotAssignArchiveSerialNumber": {}, "couldNotDeleteDocument": "Dokument konnte nicht gelöscht werden, bitte versuche es erneut.", "@couldNotDeleteDocument": {}, "couldNotLoadDocuments": "Dokumente konnten nicht geladen werden, bitte versuche es erneut.", "@couldNotLoadDocuments": {}, "couldNotLoadDocumentPreview": "Vorschau konnte nicht geladen werden.", "@couldNotLoadDocumentPreview": {}, "couldNotCreateDocument": "Dokumenttyp konnte nicht erstellt werden, bitte versuche es erneut.", "@couldNotCreateDocument": {}, "couldNotLoadDocumentTypes": "Dokumenttypen konnten nicht geladen werden, bitte versuche es erneut.", "@couldNotLoadDocumentTypes": {}, "couldNotUpdateDocument": "Dokument konnte nicht aktualisiert werden, bitte versuche es erneut.", "@couldNotUpdateDocument": {}, "couldNotUploadDocument": "Dokument konnte nicht hoch<PERSON>aden werden, bitte versuche es erneut.", "@couldNotUploadDocument": {}, "invalidCertificateOrMissingPassphrase": "Ungültiges Zertifikat oder fehlende Passphrase, bitte versuche es erneut.", "@invalidCertificateOrMissingPassphrase": {}, "couldNotLoadSavedViews": "Ansichten konnten nicht geladen werden.", "@couldNotLoadSavedViews": {}, "aClientCertificateWasExpectedButNotSent": "Ein Client Zerfitikat wurde erwartet, aber nicht gesendet. Bitte konfiguriere ein gültiges Zertifikat.", "@aClientCertificateWasExpectedButNotSent": {}, "userIsNotAuthenticated": "User is not authenticated.", "@userIsNotAuthenticated": {}, "requestTimedOut": "Bei der Anfrage an den Server kam es zu einer Zeitüberschreitung.", "@requestTimedOut": {}, "anErrorOccurredRemovingTheScans": "Beim Löschen der Aufnahmen ist ein Fehler aufgetreten.", "@anErrorOccurredRemovingTheScans": {}, "couldNotReachYourPaperlessServer": "Es konnte keine Verbindung zu Deinem Paperless Server hergestellt werden, ist die Instanz in Betrieb?", "@couldNotReachYourPaperlessServer": {}, "couldNotLoadSimilarDocuments": "Ähnliche Dokumente konnten nicht geladen werden.", "@couldNotLoadSimilarDocuments": {}, "couldNotCreateStoragePath": "Speicherpfad konnte nicht erstellt werden, bitte versuche es erneut.", "@couldNotCreateStoragePath": {}, "couldNotLoadStoragePaths": "Speicherpfade konnten nicht geladen werden.", "@couldNotLoadStoragePaths": {}, "couldNotLoadSuggestions": "Vorschläge konnten nicht geladen werden.", "@couldNotLoadSuggestions": {}, "couldNotCreateTag": "Tag konnte nicht erstellt werden, bitte versuche es erneut.", "@couldNotCreateTag": {}, "couldNotLoadTags": "Tags konnten nicht geladen werden.", "@couldNotLoadTags": {}, "anUnknownErrorOccurred": "Ein unbekannter Fehler ist aufgetreten.", "@anUnknownErrorOccurred": {}, "fileFormatNotSupported": "Das Dateiformat wird nicht unterstützt.", "@fileFormatNotSupported": {}, "report": "MELDEN", "@report": {}, "absolute": "Absolut", "@absolute": {}, "hintYouCanAlsoSpecifyRelativeValues": "Hinweis: <PERSON>eben konkreten Daten kannst du den Zeitraum auch über eine relative Zeitspanne einschränken.", "@hintYouCanAlsoSpecifyRelativeValues": {"description": "Displayed in the extended date range picker"}, "amount": "<PERSON><PERSON><PERSON>", "@amount": {}, "relative": "Relativ", "@relative": {}, "last": "Letzte", "@last": {}, "timeUnit": "Zeiteinheit", "@timeUnit": {}, "selectDateRange": "Wähle Zeitraum", "@selectDateRange": {}, "after": "Nach", "@after": {}, "before": "Vor", "@before": {}, "days": "{count, plural, zero{} one{Tag} other{Tage}}", "@days": {"placeholders": {"count": {}}}, "lastNDays": "{count, plural, zero{} one{<PERSON><PERSON><PERSON>} other{Letzte {count} <PERSON><PERSON>}}", "@lastNDays": {"placeholders": {"count": {}}}, "lastNMonths": "{count, plural, zero{} one{<PERSON><PERSON><PERSON>} other{Letzte {count} <PERSON><PERSON>}}", "@lastNMonths": {"placeholders": {"count": {}}}, "lastNWeeks": "{count, plural, zero{} one{Letzte Woche} other{Letzte {count} <PERSON><PERSON><PERSON>}}", "@lastNWeeks": {"placeholders": {"count": {}}}, "lastNYears": "{count, plural, zero{} one{Letztes Jahr} other{Letzte {count} Jahre}}", "@lastNYears": {"placeholders": {"count": {}}}, "months": "{count, plural, zero{} one{<PERSON><PERSON>} other{Monate}}", "@months": {"placeholders": {"count": {}}}, "weeks": "{count, plural, zero{} one{<PERSON>och<PERSON>} other{<PERSON><PERSON><PERSON>}}", "@weeks": {"placeholders": {"count": {}}}, "years": "{count, plural, zero{} one{<PERSON>ahr} other{Jahre}}", "@years": {"placeholders": {"count": {}}}, "gotIt": "Verstanden!", "@gotIt": {}, "cancel": "Abbrechen", "@cancel": {}, "close": "Schließen", "@close": {}, "create": "<PERSON><PERSON><PERSON><PERSON>", "@create": {}, "delete": "Löschen", "@delete": {}, "edit": "<PERSON><PERSON><PERSON>", "@edit": {}, "ok": "Ok", "@ok": {}, "save": "Speichern", "@save": {}, "select": "Auswählen", "@select": {}, "saveChanges": "Änderungen speichern", "@saveChanges": {}, "upload": "Hochladen", "@upload": {}, "youreOffline": "Du bist offline.", "@youreOffline": {}, "deleteDocument": "Dokument löschen", "@deleteDocument": {"description": "Used as an action label on each inbox item"}, "removeDocumentFromInbox": "Dokument aus Posteingang entfernt.", "@removeDocumentFromInbox": {}, "areYouSureYouWantToMarkAllDocumentsAsSeen": "<PERSON><PERSON> <PERSON>, dass Du alle Dokumente als gesehen markieren möchtest? <PERSON><PERSON><PERSON> wird eine Massenbearbeitung durchgeführt, bei der alle Posteingangs-Tags von den Dokumenten entfernt werden. Diese Aktion kann nicht rückgängig gemacht werden! Möchtest Du trotzdem fortfahren?", "@areYouSureYouWantToMarkAllDocumentsAsSeen": {}, "markAllAsSeen": "Alle als gesehen markieren?", "@markAllAsSeen": {}, "allSeen": "<PERSON>e gesehen", "@allSeen": {}, "markAsSeen": "<PERSON><PERSON> g<PERSON>hen markieren", "@markAsSeen": {}, "refresh": "Neu laden", "@refresh": {}, "youDoNotHaveUnseenDocuments": "Du hast keine ungesehenen Dokumente.", "@youDoNotHaveUnseenDocuments": {}, "quickAction": "Schnell Aktion", "@quickAction": {}, "suggestionSuccessfullyApplied": "Vorschlag wurde erfolgreich angewendet.", "@suggestionSuccessfullyApplied": {}, "today": "<PERSON><PERSON>", "@today": {}, "undo": "Undo", "@undo": {}, "nUnseen": "{count} ungesehen", "@nUnseen": {"placeholders": {"count": {}}}, "swipeLeftToMarkADocumentAsSeen": "Tipp: Wische nach links um ein Dokument als gesehen zu markieren und alle Posteingangs-Tags von diesem Dokument zu entfernen.", "@swipeLeftToMarkADocumentAsSeen": {}, "yesterday": "Gestern", "@yesterday": {}, "anyAssigned": "Beliebig zugewiesen", "@anyAssigned": {}, "noItemsFound": "<PERSON><PERSON> gefunden!", "@noItemsFound": {}, "caseIrrelevant": "Groß-/Kleinschreibung irrelevant", "@caseIrrelevant": {}, "matchingAlgorithm": "Zuweisungsalgorithmus", "@matchingAlgorithm": {}, "match": "Zuweisungsmuster", "@match": {}, "name": "Name", "@name": {}, "notAssigned": "<PERSON>cht zugewiesen", "@notAssigned": {}, "addNewCorrespondent": "<PERSON><PERSON><PERSON> neuen Korrespondenten", "@addNewCorrespondent": {}, "noCorrespondentsSetUp": "<PERSON>s wurden noch keine Korrespondenten angelegt.", "@noCorrespondentsSetUp": {}, "correspondents": "Korrespondenten", "@correspondents": {}, "addNewDocumentType": "<PERSON><PERSON><PERSON>uen Dokumenttypen", "@addNewDocumentType": {}, "noDocumentTypesSetUp": "<PERSON>s wurden noch keine Dokumenttypen angelegt.", "@noDocumentTypesSetUp": {}, "documentTypes": "Dokumenttypen", "@documentTypes": {}, "addNewStoragePath": "<PERSON><PERSON><PERSON> neuen Speicherpfad", "@addNewStoragePath": {}, "noStoragePathsSetUp": "<PERSON>s wurden noch keine Speicherpfade angelegt.", "@noStoragePathsSetUp": {}, "storagePaths": "Speicherpfade", "@storagePaths": {}, "addNewTag": "<PERSON><PERSON><PERSON>", "@addNewTag": {}, "noTagsSetUp": "<PERSON>s wurden noch keine Tags angelegt.", "@noTagsSetUp": {}, "linkedDocuments": "Referenzierte Dokumente", "@linkedDocuments": {}, "advancedSettings": "Erweiterte Einstellungen", "@advancedSettings": {}, "passphrase": "Passphrase", "@passphrase": {}, "configureMutualTLSAuthentication": "Konfiguriere Mutual TLS Authentifizierung", "@configureMutualTLSAuthentication": {}, "invalidCertificateFormat": "Ungültiges Zertifikatsformat, nur .pfx ist erlaubt.", "@invalidCertificateFormat": {}, "clientcertificate": "<PERSON><PERSON>", "@clientcertificate": {}, "selectFile": "Datei auswählen...", "@selectFile": {}, "continueLabel": "Fortfahren", "@continueLabel": {}, "incorrectOrMissingCertificatePassphrase": "Falsche oder fehlende Zertifikatspassphrase.", "@incorrectOrMissingCertificatePassphrase": {}, "connect": "Verbinden", "@connect": {}, "password": "Passwort", "@password": {}, "passwordMustNotBeEmpty": "Passwort darf nicht leer sein.", "@passwordMustNotBeEmpty": {}, "connectionTimedOut": "Zeitüberschreitung der Verbindung.", "@connectionTimedOut": {}, "loginPageReachabilityMissingClientCertificateText": "Ein Client-Zertifikat wurde erwartet aber nicht gesendet. Bitte stelle ein Zertifikat zur Verfügung.", "@loginPageReachabilityMissingClientCertificateText": {}, "couldNotEstablishConnectionToTheServer": "<PERSON>s konnte keine Verbindung zum Server hergestellt werden.", "@couldNotEstablishConnectionToTheServer": {}, "connectionSuccessfulylEstablished": "Verbindung erfolgreich hergestellt.", "@connectionSuccessfulylEstablished": {}, "hostCouldNotBeResolved": "Der Host konnte nicht aufgelöst werden. Bitte überprüfe die Server-Adresse und deine Internetverbindung.", "@hostCouldNotBeResolved": {}, "serverAddress": "Server-<PERSON><PERSON><PERSON>", "@serverAddress": {}, "invalidAddress": "Ungültige Adresse.", "@invalidAddress": {}, "serverAddressMustIncludeAScheme": "Server-<PERSON><PERSON><PERSON> muss ein <PERSON> enthalten.", "@serverAddressMustIncludeAScheme": {}, "serverAddressMustNotBeEmpty": "Server-Addresse darf nicht leer sein.", "@serverAddressMustNotBeEmpty": {}, "signIn": "Anmelden", "@signIn": {}, "loginPageSignInTitle": "Anmelden", "@loginPageSignInTitle": {}, "signInToServer": "Bei {<PERSON><PERSON><PERSON><PERSON>} anmelden", "@signInToServer": {"placeholders": {"serverAddress": {}}}, "connectToPaperless": "<PERSON>t Paperless verbinden", "@connectToPaperless": {}, "username": "<PERSON><PERSON><PERSON><PERSON>", "@username": {}, "usernameMustNotBeEmpty": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein.", "@usernameMustNotBeEmpty": {}, "documentContainsAllOfTheseWords": "Dokument enthält alle folgenden Wörter", "@documentContainsAllOfTheseWords": {}, "all": "Alle", "@all": {}, "documentContainsAnyOfTheseWords": "Dokument enthält eins der folgenden Wörter", "@documentContainsAnyOfTheseWords": {}, "any": "<PERSON><PERSON><PERSON><PERSON>", "@any": {}, "learnMatchingAutomatically": "Zuweisung automatisch erlernen", "@learnMatchingAutomatically": {}, "auto": "Auto", "@auto": {}, "documentContainsThisString": "Dokument enthält die folgende Zeichenkette", "@documentContainsThisString": {}, "exact": "Exakt", "@exact": {}, "documentContainsAWordSimilarToThisWord": "Dokument enthält ein zum folgenden Wort ähnliches Wort", "@documentContainsAWordSimilarToThisWord": {}, "fuzzy": "<PERSON><PERSON><PERSON>", "@fuzzy": {}, "documentMatchesThisRegularExpression": "Dokument passt zum folgenden Ausdruck", "@documentMatchesThisRegularExpression": {}, "regularExpression": "Regulärer Ausdruck", "@regularExpression": {}, "anInternetConnectionCouldNotBeEstablished": "<PERSON>s konte keine Verbindung zum Internet hergestellt werden.", "@anInternetConnectionCouldNotBeEstablished": {}, "done": "<PERSON><PERSON><PERSON>", "@done": {}, "next": "<PERSON><PERSON>", "@next": {}, "couldNotAccessReceivedFile": "Der Zugriff auf die empfangene Datei wurde verweigert. Bitte öffne die App vor dem teilen.", "@couldNotAccessReceivedFile": {}, "newView": "Neue Ansicht", "@newView": {}, "createsASavedViewBasedOnTheCurrentFilterCriteria": "Erstellt eine neue Ansicht basierend auf den aktuellen Filterkriterien.", "@createsASavedViewBasedOnTheCurrentFilterCriteria": {}, "createViewsToQuicklyFilterYourDocuments": "Lege Ansichten an, um Dokumente schneller zu finden.", "@createViewsToQuicklyFilterYourDocuments": {}, "nFiltersSet": "{count, plural, zero{{count} Filter gesetzt} one{{count} Filter gesetzt} other{{count} Filter gesetzt}}", "@nFiltersSet": {"placeholders": {"count": {}}}, "showInSidebar": "In Seitenleiste zeigen", "@showInSidebar": {}, "showOnDashboard": "Auf Startseite zeigen", "@showOnDashboard": {}, "views": "<PERSON><PERSON><PERSON><PERSON>", "@views": {}, "clearAll": "Alle löschen", "@clearAll": {}, "scan": "<PERSON><PERSON><PERSON><PERSON>", "@scan": {}, "previewScan": "Vorschau", "@previewScan": {}, "scrollToTop": "<PERSON><PERSON>", "@scrollToTop": {}, "paperlessServerVersion": "Paperless Server-Version", "@paperlessServerVersion": {}, "darkTheme": "Dunkler Modus", "@darkTheme": {}, "lightTheme": "<PERSON><PERSON>", "@lightTheme": {}, "systemTheme": "Benutze Sytemeinstellung", "@systemTheme": {}, "appearance": "<PERSON><PERSON><PERSON>", "@appearance": {}, "languageAndVisualAppearance": "Sprache und Aussehen", "@languageAndVisualAppearance": {}, "applicationSettings": "<PERSON><PERSON><PERSON><PERSON>", "@applicationSettings": {}, "colorSchemeHint": "W<PERSON>hle zwischen einem klassischen Farbschema, das vom traditionellen Paperless-Grün inspiriert ist, oder einem dynamische Farbschema basierend auf den Systemfarben.", "@colorSchemeHint": {}, "colorSchemeNotSupportedWarning": "Dynamische Farbgebung wird nur von Geräten mit Android 12 und höher unterstützt. Das Auswählen des dynamischen Farbschemas hat für Geräte unter Android 12 womöglich keinen Effekt.", "@colorSchemeNotSupportedWarning": {}, "colors": "<PERSON><PERSON>", "@colors": {}, "language": "<PERSON><PERSON><PERSON>", "@language": {}, "security": "Sicherheit", "@security": {}, "mangeFilesAndStorageSpace": "Dateien und Speicherplatz verwalten", "@mangeFilesAndStorageSpace": {}, "storage": "<PERSON><PERSON><PERSON><PERSON>", "@storage": {}, "dark": "<PERSON><PERSON><PERSON>", "@dark": {}, "light": "Hell", "@light": {}, "system": "System", "@system": {}, "ascending": "Aufsteigend", "@ascending": {}, "descending": "Absteigend", "@descending": {}, "storagePathDay": "Tag", "@storagePathDay": {}, "storagePathMonth": "<PERSON><PERSON>", "@storagePathMonth": {}, "storagePathYear": "<PERSON><PERSON><PERSON>", "@storagePathYear": {}, "color": "Farbe", "@color": {}, "filterTags": "<PERSON><PERSON>ne zu tippen...", "@filterTags": {}, "inboxTag": "Posteingangs-Tag", "@inboxTag": {}, "uploadInferValuesHint": "Wenn Werte für diese Felder angegeben werden, wird Paperless nicht automatisch einen Wert zuweisen. Wenn diese Felder automatisch von Paperless erkannt werden sollen, sollten die Felder leer bleiben.", "@uploadInferValuesHint": {}, "useTheConfiguredBiometricFactorToAuthenticate": "Benutze den konfigurierten Biometrischen Faktor um dich zu identifizieren und auf deine Dokumente zuzugreifen.", "@useTheConfiguredBiometricFactorToAuthenticate": {}, "verifyYourIdentity": "Verifiziere deine Identität", "@verifyYourIdentity": {}, "verifyIdentity": "Identität verifizieren", "@verifyIdentity": {}, "detailed": "Detailliert", "@detailed": {}, "grid": "<PERSON><PERSON>", "@grid": {}, "list": "Liste", "@list": {}, "remove": "Entfernen", "removeQueryFromSearchHistory": "Aus Suchverlauf ent<PERSON>?", "dynamicColorScheme": "Dynamisch", "@dynamicColorScheme": {}, "classicColorScheme": "Klassisch", "@classicColorScheme": {}, "notificationDownloadComplete": "Download abgeschlossen", "@notificationDownloadComplete": {"description": "Notification title when a download has been completed."}, "notificationDownloadingDocument": "Do<PERSON><PERSON> wird heruntergeladen", "@notificationDownloadingDocument": {"description": "Notification title shown when a document download is pending"}, "archiveSerialNumberUpdated": "Archiv-Seriennummer aktualisiert.", "@archiveSerialNumberUpdated": {"description": "Message shown when the ASN has been updated."}, "donateCoffee": "Spendiere mir einen <PERSON>e", "@donateCoffee": {"description": "Label displayed in the app drawer"}, "thisFieldIsRequired": "<PERSON><PERSON> ist erford<PERSON>lich!", "@thisFieldIsRequired": {"description": "Message shown below the form field when a required field has not been filled out."}, "confirm": "Bestätigen", "confirmAction": "Aktion bestätigen", "@confirmAction": {"description": "Typically used as a title to confirm a previously selected action"}, "areYouSureYouWantToContinue": "Bist <PERSON>, dass Du fortfahren möchtest?", "bulkEditTagsAddMessage": "{count, plural, one{Diese Operation wird die Tags {tags} dem ausgewählten Dokument hinzufügen.} other{Diese Operation wird die Tags {tags} den {count} ausgewählten Dokumenten hinzufügen.}}", "@bulkEditTagsAddMessage": {"description": "Message of the confirmation dialog when bulk adding tags."}, "bulkEditTagsRemoveMessage": "{count, plural, one{Diese Operation wird die Tags {tags} vom ausgewählten Dokument entfernen.} other{Diese Operation wird die Tags {tags} von den {count} ausgewählten Dokumenten entfernen.}}", "@bulkEditTagsRemoveMessage": {"description": "Message of the confirmation dialog when bulk removing tags."}, "bulkEditTagsModifyMessage": "{count, plural, one{Diese Operation wird die Tags {addTags} hinzufügen und die Tags {removeTags} von dem ausgewählten Dokument entfernen.} other{Diese Operation wird die Tags {addTags} hinzufügen und die Tags {removeTags} von {count} ausgewählten Dokumenten entfernen.}}", "@bulkEditTagsModifyMessage": {"description": "Message of the confirmation dialog when both adding and removing tags."}, "bulkEditCorrespondentAssignMessage": "{count, plural, one{Diese Operation wird den Korrespondent {correspondent} dem ausgewählten Dokument zuweisen.} other{Diese Operation wird den Korrespondent {correspondent} den {count} ausgewählten Dokumenten zuweisen.}}", "bulkEditDocumentTypeAssignMessage": "{count, plural, one{Diese Operation wird den Dokumenttyp {docType} dem ausgewählten Dokument zuweisen.} other{Diese Operation wird den Dokumenttyp {docType} den {count} ausgewählten Dokumenten zuweisen.}}", "bulkEditStoragePathAssignMessage": "{count, plural, one{Diese Operation wird den Speicherpfad {path} dem ausgewählten Dokument zuweisen.} other{Diese Operation wird den Speicherpfad {path} den {count} ausgewählten Dokumenten zuweisen.}}", "bulkEditCorrespondentRemoveMessage": "{count, plural, one{Diese Operation wird den Korrespondent vom ausgewählten Dokument entfernen.} other{Diese Operation wird den Korrespondent von {count} ausgewählten Dokumenten entfernen.}}", "bulkEditDocumentTypeRemoveMessage": "{count, plural, one{Diese Operation wird den Dokumenttyp vom ausgewählten Dokument entfernen.} other{Diese Operation wird den Dokumenttyp von {count} ausgewählten Dokumenten entfernen.}}", "bulkEditStoragePathRemoveMessage": "{count, plural, one{Diese Operation wird den Speicherpfad vom ausgewählten Dokument entfernen.} other{Diese Operation wird den Speicherpfad von {count} ausgewählten Dokumenten entfernen.}}", "anyTag": "Irgendeines", "@anyTag": {"description": "Label shown when any tag should be filtered"}, "allTags": "Alle", "@allTags": {"description": "Label shown when a document has to be assigned to all selected tags"}, "switchingAccountsPleaseWait": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bitte warten...", "@switchingAccountsPleaseWait": {"description": "Message shown while switching accounts is in progress."}, "testConnection": "Verbindung testen", "@testConnection": {"description": "Button label shown on login page. Allows user to test whether the server is reachable or not."}, "accounts": "Accounts", "@accounts": {"description": "Title of the account management dialog"}, "addAccount": "Account hi<PERSON><PERSON><PERSON><PERSON>", "@addAccount": {"description": "Label of add account action"}, "switchAccount": "Wechseln", "@switchAccount": {"description": "Label for switch account action"}, "logout": "Ausloggen", "@logout": {"description": "Generic Logout label"}, "switchAccountTitle": "Account we<PERSON><PERSON>n", "@switchAccountTitle": {"description": "Title of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "switchToNewAccount": "Möchtest du zum neuen Account wechseln? Du kannst jederzeit zurückwechseln.", "@switchToNewAccount": {"description": "Content of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "sourceCode": "Source Code", "findTheSourceCodeOn": "<PERSON>e den Code auf", "@findTheSourceCodeOn": {"description": "Text before link to Paperless Mobile GitHub"}, "rememberDecision": "Meine Entscheidung merken", "defaultDownloadFileType": "Standard Dateityp beim <PERSON>", "@defaultDownloadFileType": {"description": "Label indicating the default filetype to download (one of archived, original and always ask)"}, "defaultShareFileType": "Standard Dateityp beim <PERSON>", "@defaultShareFileType": {"description": "Label indicating the default filetype to share (one of archived, original and always ask)"}, "alwaysAsk": "Immer fragen", "@alwaysAsk": {"description": "Option to choose when the app should always ask the user which filetype to use"}, "disableMatching": "Deaktiviere automatische Zuweisung", "@disableMatching": {"description": "One of the options for automatic tagging of documents"}, "none": "<PERSON><PERSON>", "@none": {"description": "One of available enum values of matching algorithm for tags"}, "logInToExistingAccount": "<PERSON><PERSON> bestehendem Account anmelden", "@logInToExistingAccount": {"description": "Title shown on login page if at least one user is already known to the app."}, "print": "<PERSON><PERSON><PERSON>", "@print": {"description": "Tooltip for print button"}, "managePermissions": "Berechtigungen verwalten", "@managePermissions": {"description": "Button which leads user to manage permissions page"}, "errorRetrievingServerVersion": "<PERSON><PERSON> Server-Version ist ein Fehler aufgetreten.", "@errorRetrievingServerVersion": {"description": "Message shown at the bottom of the settings page when the remote server version could not be resolved."}, "resolvingServerVersion": "Lade Server-Version...", "@resolvingServerVersion": {"description": "Message shown while the app is loading the remote server version."}, "goToLogin": "Gehe zur Anmeldung", "@goToLogin": {"description": "Label of the button shown on the login page to skip logging in to existing accounts and navigate user to login page"}, "export": "Exportieren", "@export": {"description": "Label for button that exports scanned images to pdf (before upload)"}, "invalidFilenameCharacter": "Ungültige(s) Zeichen im Dateinamen gefunden: {characters}", "@invalidFilenameCharacter": {"description": "For validating filename in export dialogue"}, "exportScansToPdf": "Scans als PDF exportieren", "@exportScansToPdf": {"description": "title of the alert dialog when exporting scans to pdf"}, "allScansWillBeMerged": "Alle Scans werden in eine einzige PDF-Datei zusammengeführt.", "behavior": "Verhalten", "@behavior": {"description": "Title of the settings concerning app beahvior"}, "theme": "Erscheinungsbild", "@theme": {"description": "Title of the theme mode setting"}, "clearCache": "<PERSON><PERSON> le<PERSON>n", "@clearCache": {"description": "Title of the clear cache setting"}, "freeBytes": "{byteString} freigeben", "@freeBytes": {"description": "Text shown for clear storage settings"}, "calculatingDots": "Berechne...", "@calculatingDots": {"description": "Text shown when the byte size is still being calculated"}, "freedDiskSpace": "{bytes} erfolgreich freigegeben.", "@freedDiskSpace": {"description": "Message shown after clearing storage"}, "uploadScansAsPdf": "Scans als PDF hochladen", "@uploadScansAsPdf": {"description": "Title of the setting which toggles whether scans are always uploaded as pdf"}, "convertSinglePageScanToPdf": "Einseitige Scans vor dem Hochladen immer in PDF umwandeln", "@convertSinglePageScanToPdf": {"description": "description of the upload scans as pdf setting"}, "loginRequiredPermissionsHint": "Die Verwendung von Paperless Mobile erfordert seit paperless-ngx 1.14.0 und höher ein Mindestmaß an Benutzerberechtigungen. <PERSON>elle deshalb bitte sicher, dass der anzumeldende Benutzer die Berechtigung hat, andere Benutzer (User → View) und die Einstellungen (UISettings → View) einzusehen. Falls du nicht über diese Berechtigungen verfügst, wende dich bitte an einen Administrator deines paperless-ngx Servers.", "@loginRequiredPermissionsHint": {"description": "Hint shown on the login page informing the user of the required permissions to use the app."}, "missingPermissions": "Sie besitzen nicht die benötigten Berechtigungen, um diese Aktion durchzuführen.", "@missingPermissions": {"description": "Message shown in a snackbar when a user without the reequired permissions performs an action."}, "editView": "Ansicht bearbeiten", "@editView": {"description": "Title of the edit saved view page"}, "donate": "<PERSON><PERSON><PERSON>", "@donate": {"description": "Label of the in-app donate button"}, "donationDialogContent": "<PERSON><PERSON><PERSON>, dass Du diese App unterstützen möchtest! Aufgrund der Zahlungsrichtlinien von Google und Apple dürfen keine Links, die zu Spendenseiten führen, in der App angezeigt werden. Nicht einmal die Verlinkung zur Repository-Seite des Projekts scheint in diesem Zusammenhang erlaubt zu sein. Werfe von daher vielleicht einen Blick auf den Abschnitt 'Donations' in der README des Projekts. Deine Unterstützung ist sehr willkommen und hält die Entwicklung dieser App am Leben. Vielen Dank!", "@donationDialogContent": {"description": "Text displayed in the donation dialog"}, "noDocumentsFound": "Keine Dokumente gefunden.", "@noDocumentsFound": {"description": "Message shown when no documents were found."}, "couldNotDeleteCorrespondent": "Korrespondent konnte nicht gelöscht werden, bitte versuche es erneut.", "@couldNotDeleteCorrespondent": {"description": "Message shown in snackbar when a correspondent could not be deleted."}, "couldNotDeleteDocumentType": "Dokumenttyp konnten nicht gelöscht werden, bitte versuche es erneut.", "@couldNotDeleteDocumentType": {"description": "Message shown when a document type could not be deleted"}, "couldNotDeleteTag": "Tag konnte nicht gelöscht werden, bitte versuche es erneut.", "@couldNotDeleteTag": {"description": "Message shown when a tag could not be deleted"}, "couldNotDeleteStoragePath": "Speicherpfad konnte nicht gelöscht werden, bitte versuchen Sie es erneut.", "@couldNotDeleteStoragePath": {"description": "Message shown when a storage path could not be deleted"}, "couldNotUpdateCorrespondent": "Korrespondent konnte nicht aktualisiert werden, bitte versuche es erneut.", "@couldNotUpdateCorrespondent": {"description": "Message shown when a correspondent could not be updated"}, "couldNotUpdateDocumentType": "Dokumenttyp konnte nicht aktualisiert werden, bitte versuche es erneut.", "@couldNotUpdateDocumentType": {"description": "Message shown when a document type could not be updated"}, "couldNotUpdateTag": "Tag konnte nicht aktualisiert werden, bitte versuche es erneut.", "@couldNotUpdateTag": {"description": "Message shown when a tag could not be updated"}, "couldNotLoadServerInformation": "Serverinformationen konnten nicht geladen werden.", "@couldNotLoadServerInformation": {"description": "Message shown when the server information could not be loaded"}, "couldNotLoadStatistics": "Serverstatistiken konnten nicht geladen werden.", "@couldNotLoadStatistics": {"description": "Message shown when the server statistics could not be loaded"}, "couldNotLoadUISettings": "UI Einstellungen konnten nicht geladen werden.", "@couldNotLoadUISettings": {"description": "Message shown when the UI settings could not be loaded"}, "couldNotLoadTasks": "Dateiaufgaben konnten nicht geladen werden.", "@couldNotLoadTasks": {"description": "Message shown when the tasks (e.g. document consumed) could not be loaded"}, "userNotFound": "Der Nutzer konnte nicht gefunden werden.", "@userNotFound": {"description": "Message shown when the specified user (e.g. by id) could not be found"}, "couldNotUpdateSavedView": "Ansicht konnte nicht aktualisiert werden, bitte versuche es erneut.", "@couldNotUpdateSavedView": {"description": "Message shown when a saved view could not be updated"}, "couldNotUpdateStoragePath": "Speicherpfad konnte nicht aktualisiert werden, bitte versuchen Sie es erneut.", "savedViewSuccessfullyUpdated": "Ansicht erfolgreich aktualisiert.", "@savedViewSuccessfullyUpdated": {"description": "Message shown when a saved view was successfully updated."}, "discardChanges": "Änderungen verwerfen?", "@discardChanges": {"description": "Title of the alert dialog shown when a user tries to close a view with unsaved changes."}, "savedViewChangedDialogContent": "Die Filterbedingungen der aktiven Ansicht haben sich geändert. Durch Zurücksetzen des aktuellen Filters gehen diese Änderungen verloren. Möchtest du trotzdem fortfahren?", "@savedViewChangedDialogContent": {"description": "Content of the alert dialog shown when all of the following applies:\r\n* User has saved view selected\r\n* User has performed changes to the current document filter\r\n* User now tries to reset this filter without having saved the changes to the view."}, "createFromCurrentFilter": "Vom aktuellen Filter erstellen", "@createFromCurrentFilter": {"description": "Tooltip of the \"New saved view\" button"}, "home": "Startseite", "@home": {"description": "Label of the \"Home\" route"}, "welcomeUser": "<PERSON><PERSON><PERSON><PERSON>, {name}!", "@welcomeUser": {"description": "Top message shown on the home page"}, "statistics": "Statistiken", "documentsInInbox": "Dokumente im Posteingang", "totalDocuments": "Dokumente insgesamt", "totalCharacters": "Zeichen insgesamt", "showAll": "Alle anzeigen", "@showAll": {"description": "Button label shown on a saved view preview to open this view in the documents page"}, "userAlreadyExists": "Dieser Nutzer existiert bereits.", "@userAlreadyExists": {"description": "Error message shown when the user tries to add an already existing account."}, "youDidNotSaveAnyViewsYet": "Du hast noch keine Ansichten gespeichert. <PERSON><PERSON><PERSON> eine neue Ansicht, und sie wird hier angezeigt.", "@youDidNotSaveAnyViewsYet": {"description": "Message shown when there are no saved views yet."}, "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "discardFile": "Datei verwerfen?", "discard": "Verwerfen", "backToLogin": "Zurück zur Anmeldung", "skipEditingReceivedFiles": "Bearbeitun<PERSON> von empfangenen Dateien überspringen", "uploadWithoutPromptingUploadForm": "<PERSON><PERSON> der App geteilte Dateien immer direkt hochladen, ohne das Upload-Formular anzuzeigen.", "authenticatingDots": "Authentifizieren...", "@authenticatingDots": {"description": "Message shown when the app is authenticating the user"}, "persistingUserInformation": "Nutzerinformationen werden gespeichert...", "fetchingUserInformation": "Benutzerinformationen werden abgerufen...", "@fetchingUserInformation": {"description": "Message shown when the app loads user data from the server"}, "restoringSession": "Sitzung wird wiederhergestellt...", "@restoringSession": {"description": "Message shown when the user opens the app and the previous user is tried to be authenticated and logged in"}, "documentsAssigned": "{count, plural, zero{Ke<PERSON>} one{1 <PERSON>kument} other{{count} Dokumente}}", "@documentsAssigned": {"description": "Text shown with a correspondent, document type etc. to indicate the number of documents this filter will maximally yield."}, "discardChangesWarning": "Du hast ungespeicherte Änderungen. Diese gehen verloren, falls du fortfährst. Möchtest du die Änderungen verwerfen?", "@discardChangesWarning": {"description": "Warning message shown when the user tries to close a route without saving the changes."}, "changelog": "Changelog", "noLogsFoundOn": "<PERSON>ine <PERSON> am {date} gefunden.", "logfileBottomReached": "Du hast das Ende dieser Logdatei erreicht.", "appLogs": "App Logs {date}", "saveLogsToFile": "Logs in Datei speichern", "copyToClipboard": "In Zwischenablage kopieren", "couldNotLoadLogfileFrom": "Logs vom {date} konnten nicht geladen werden.", "loadingLogsFrom": "Lade Logs vom {date}...", "clearLogs": "Logs vom {date} leeren", "showPdf": "PDF anzeigen", "@showPdf": {"description": "Tooltip shown on the \"show pdf\" button on the document edit page"}, "hidePdf": "PDF ausblenden", "@hidePdf": {"description": "Tooltip shown on the \"show pdf\" icon button on the document edit page"}, "misc": "Sonstige", "loggingOut": "Abmelden...", "testingConnection": "Teste Verbindung...", "@testingConnection": {"description": "Text shown while the app tries to establish a connection to the specified host."}, "version": "Version {versionCode}", "notes": "{count, plural, zero{Notizen} one{Notiz} other{Notizen}}", "addNote": "<PERSON><PERSON>", "newerVersionAvailable": "Neuere Version verfügbar:", "dateOutOfRange": "Das Datum muss zwischen {firstDate} und {lastDate} liegen.", "@dateOutOfRange": {"description": "Error message shown when the user tries to select a date outside of the allowed range.", "placeholders": {"firstDate": {"type": "DateTime", "format": "yMd"}, "lastDate": {"type": "DateTime", "format": "yMd"}}}, "permissions": "Berechtigungen", "newNote": "Neue Notiz", "notesMarkdownSyntaxSupportHint": "Paperless Mobile unterstützt Markdown-Syntax zur Darstellung und Formatierung von Notizen. Probiere es aus!"}
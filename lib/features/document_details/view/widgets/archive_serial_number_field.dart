import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/bloc/connectivity_cubit.dart';
import 'package:paperless_mobile/core/bloc/loading_status.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_details/cubit/document_details_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';

class ArchiveSerialNumberField extends StatefulWidget {
  final DocumentModel document;
  const ArchiveSerialNumberField({
    super.key,
    required this.document,
  });

  @override
  State<ArchiveSerialNumberField> createState() =>
      _ArchiveSerialNumberFieldState();
}

class _ArchiveSerialNumberFieldState extends State<ArchiveSerialNumberField> {
  late final TextEditingController _asnEditingController;
  late bool _showClearButton;
  bool _canUpdate = false;
  Map<String, dynamic> _errors = {};

  @override
  void initState() {
    super.initState();
    _asnEditingController = TextEditingController(
      text: widget.document.archiveSerialNumber?.toString(),
    )..addListener(_clearButtonListener);
    _showClearButton = widget.document.archiveSerialNumber != null;
    _asnEditingController.addListener(() {});
  }

  void _clearButtonListener() {
    setState(() {
      _showClearButton = _asnEditingController.text.isNotEmpty;
      _canUpdate = int.tryParse(_asnEditingController.text) !=
          widget.document.archiveSerialNumber;
    });
  }

  @override
  Widget build(BuildContext context) {
    final userCanEditDocument =
        context.watch<LocalUserAccount>().paperlessUser.canEditDocuments;
    return BlocListener<DocumentDetailsCubit, DocumentDetailsState>(
      listenWhen: (previous, current) =>
          previous.status == LoadingStatus.loaded &&
          current.status == LoadingStatus.loaded &&
          previous.document!.archiveSerialNumber !=
              current.document!.archiveSerialNumber,
      listener: (context, state) {
        _asnEditingController.text =
            state.document!.archiveSerialNumber?.toString() ?? '';
        setState(() {
          _canUpdate = false;
        });
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Archive Serial Number (ASN)',
            style:
                AppTextStyles.textStyle14.copyWith(fontWeight: FontWeight.w600),
          ),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  enabled: userCanEditDocument,
                  controller: _asnEditingController,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    setState(() => _errors = {});
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  onFieldSubmitted: (_) => _onSubmitted(),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10)),
                    focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(color: AppColor.primary)),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_showClearButton)
                          IconButton(
                            icon: const Icon(Icons.clear),
                            color: Theme.of(context).colorScheme.primary,
                            onPressed: userCanEditDocument
                                ? _asnEditingController.clear
                                : null,
                          ),
                        IconButton(
                          icon: const Icon(Icons.plus_one_rounded),
                          color: Theme.of(context).colorScheme.primary,
                          onPressed: context.watchInternetConnection &&
                                  !_showClearButton
                              ? _onAutoAssign
                              : null,
                        ).paddedOnly(right: 8),
                      ],
                    ),
                    errorText: _errors['archive_serial_number'],
                    errorMaxLines: 1,
                    // labelText: S.of(context)!.archiveSerialNumber,
                  ),
                ),
              ),
              const Gap(16),
              GestureDetector(
                onTap: context.watchInternetConnection && _canUpdate
                    ? _onSubmitted
                    : null,
                child: Container(
                    alignment: Alignment.center,
                    height: 56,
                    width: 81,
                    decoration: BoxDecoration(
                        color: _asnEditingController.text.isNotEmpty
                            ? AppColor.primary
                            : AppColor.grey_DADADA,
                        borderRadius: BorderRadius.circular(10)),
                    child: Text(
                      S.of(context)!.save,
                      style: AppTextStyles.textStyle14.copyWith(
                          fontWeight: FontWeight.w600,
                          color: _asnEditingController.text.isNotEmpty
                              ? AppColor.white
                              : AppColor.grey_A1A1A1),
                    )),
              )
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _onSubmitted() async {
    final value = _asnEditingController.text;
    final asn = int.tryParse(value);

    await context
        .read<DocumentDetailsCubit>()
        .assignAsn(widget.document, asn: asn)
        .then((value) => _onAsnUpdated())
        .onError<PaperlessApiException>(
          (error, stackTrace) => showErrorMessage(context, error, stackTrace),
        )
        .onError<PaperlessFormValidationException>(
      (error, stackTrace) {
        setState(() => _errors = error.validationMessages);
      },
    );
    FocusScope.of(context).unfocus();
  }

  Future<void> _onAutoAssign() async {
    await context
        .read<DocumentDetailsCubit>()
        .assignAsn(
          widget.document,
          autoAssign: true,
        )
        .then((value) => _onAsnUpdated())
        .onError<PaperlessApiException>(
          (error, stackTrace) => showErrorMessage(context, error, stackTrace),
        )
        .catchError((error) => showGenericError(context, error));
  }

  void _onAsnUpdated() {
    setState(() => _errors = {});
    FocusScope.of(context).unfocus();
    showSnackBar(context, S.of(context)!.archiveSerialNumberUpdated);
  }
}

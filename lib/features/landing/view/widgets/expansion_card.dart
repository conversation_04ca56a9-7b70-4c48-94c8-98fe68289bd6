import 'package:flutter/material.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

class ExpansionCard extends StatefulWidget {
  final Widget content;
  final bool initiallyExpanded;
  final String title;

  const ExpansionCard(
      {super.key,
      required this.content,
      this.initiallyExpanded = false,
      required this.title});

  @override
  State<ExpansionCard> createState() => _ExpansionCardState();
}

class _ExpansionCardState extends State<ExpansionCard> {
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded; // Set initial state
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          expansionTileTheme: ExpansionTileThemeData(
            shape: Theme.of(context).cardTheme.shape,
            collapsedShape: Theme.of(context).cardTheme.shape,
          ),
          listTileTheme: ListTileThemeData(
            shape: Theme.of(context).cardTheme.shape,
          ),
        ),
        child: ExpansionTile(
          onExpansionChanged: (value) {
            setState(() {
              _isExpanded = value; // Update expansion state
            });
          },
          backgroundColor: Colors.white,
          initiallyExpanded: widget.initiallyExpanded,
          collapsedBackgroundColor: AppColor.white,
          title: Text(
            widget.title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: _isExpanded
                  ? AppColor
                      .black_333333 // khi mở rộng, màu mũi tên là màu xanh
                  : AppColor.primary,
            ),
          ),
          iconColor: AppColor.black_333333, // màu khi mở rộng
          collapsedIconColor: AppColor.primary,
          children: [widget.content],
        ),
      ),
    );
  }
}

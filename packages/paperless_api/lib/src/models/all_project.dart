import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';
part 'all_project.g.dart';

@JsonSerializable()
@CopyWith()
class AllProject {
  int count;
  dynamic next;
  dynamic previous;
  List<int> all;
  List<Project> results;

  AllProject({
    required this.count,
    required this.next,
    required this.previous,
    required this.all,
    required this.results,
  });

  factory AllProject.fromJson(Map<String, dynamic> json) =>
      _$AllProjectFromJson(json);

  Map<String, dynamic> toJson() => _$AllProjectToJson(this);
}

@JsonSerializable()
@CopyWith()
class Project {
  int id;
  String slug;
  String name;
  String match;
  @Json<PERSON><PERSON>(name: 'matching_algorithm')
  int matchingAlgorithm;
  @JsonKey(name: 'is_insensitive')
  bool isInsensitive;
  @JsonKey(name: 'document_count')
  int documentCount;
  dynamic owner;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_can_change')
  bool userCanChange;
  bool isSelected;

  Project({
    required this.id,
    required this.slug,
    required this.name,
    required this.match,
    required this.matchingAlgorithm,
    required this.isInsensitive,
    required this.documentCount,
    required this.owner,
    required this.userCanChange,
    this.isSelected = false,
  });

  factory Project.fromJson(Map<String, dynamic> json) =>
      _$ProjectFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectToJson(this);
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0XFF024CAA),
              Color(0XFF013577),
              Color(0XFF011E44),
            ],
          ),
        ),
        child: Center(
            child: Container(
                height: 120,
                width: 120,
                decoration: BoxDecoration(
                    color: AppColor.white,
                    borderRadius: BorderRadius.circular(10)),
                child: Image.asset('assets/logos/logo_and_name.png'))),
      ),
    );
  }
}

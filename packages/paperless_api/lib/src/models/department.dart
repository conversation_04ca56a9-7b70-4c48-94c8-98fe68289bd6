// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'department.g.dart';

@JsonSerializable()
class Department extends Equatable {
  int id;
  String name;
  List<String> permissions;
  bool isSelected;
  Department({
    required this.id,
    required this.name,
    required this.permissions,
    this.isSelected = false,
  });

  factory Department.fromJson(Map<String, dynamic> json) =>
      _$DepartmentFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentToJson(this);

  @override
  // TODO: implement props
  List<Object?> get props => [id, name, permissions, isSelected];

  Department copyWith({
    int? id,
    String? name,
    List<String>? permissions,
    bool? isSelected,
  }) {
    return Department(
      id: id ?? this.id,
      name: name ?? this.name,
      permissions: permissions ?? this.permissions,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

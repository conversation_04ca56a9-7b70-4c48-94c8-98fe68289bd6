{"developedBy": "Desenvolupat per {name}.", "@developedBy": {"placeholders": {"name": {}}}, "addAnotherAccount": "Afegir altre compte", "@addAnotherAccount": {}, "account": "<PERSON><PERSON><PERSON>", "@account": {}, "addCorrespondent": "Nou corresponsal", "@addCorrespondent": {"description": "Title when adding a new correspondent"}, "addDocumentType": "Nou tipus de document", "@addDocumentType": {"description": "Title when adding a new document type"}, "addStoragePath": "Nova ruta emmagatzematge", "@addStoragePath": {"description": "Title when adding a new storage path"}, "addTag": "Nova Etiqueta", "@addTag": {"description": "Title when adding a new tag"}, "aboutThisApp": "Sobre l'app", "@aboutThisApp": {"description": "Label for about this app tile displayed in the drawer"}, "loggedInAs": "Logat com a {name}", "@loggedInAs": {"placeholders": {"name": {}}}, "disconnect": "Desconnectar", "@disconnect": {"description": "Logout button label"}, "reportABug": "Informa error", "@reportABug": {}, "settings": "Opcions", "@settings": {}, "authenticateOnAppStart": "Autenticar al iniciar l'app", "@authenticateOnAppStart": {"description": "Description of the biometric authentication settings tile"}, "biometricAuthentication": "Autenticació Biomètrica", "@biometricAuthentication": {}, "authenticateToToggleBiometricAuthentication": "{mode, select, enable{Autentifica per habilitar l'autentificació biomètrica} disable{Autentifica per deshabilitar l'autentificació biomètrica} other{}}", "@authenticateToToggleBiometricAuthentication": {"placeholders": {"mode": {}}}, "documents": "Documents", "@documents": {}, "inbox": "Inbox", "@inbox": {}, "labels": "Etiquetes", "@labels": {}, "scanner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@scanner": {}, "startTyping": "Comença a escriure...", "@startTyping": {}, "doYouReallyWantToDeleteThisView": "Vols esborrar aquesta vista?", "@doYouReallyWantToDeleteThisView": {}, "deleteView": "Esborra Vista {name}?", "@deleteView": {}, "addedAt": "Afegit", "@addedAt": {}, "archiveSerialNumber": "ASN", "@archiveSerialNumber": {}, "asn": "ASN", "@asn": {}, "correspondent": "Corresponsal", "@correspondent": {}, "createdAt": "<PERSON>reat el", "@createdAt": {}, "documentSuccessfullyDeleted": "Document esborrat correctament.", "@documentSuccessfullyDeleted": {}, "assignAsn": "Assigna ASN", "@assignAsn": {}, "deleteDocumentTooltip": "Esborra", "@deleteDocumentTooltip": {"description": "Tooltip shown for the delete button on details page"}, "downloadDocumentTooltip": "<PERSON><PERSON><PERSON><PERSON>", "@downloadDocumentTooltip": {"description": "Tooltip shown for the download button on details page"}, "editDocumentTooltip": "<PERSON><PERSON>", "@editDocumentTooltip": {"description": "Tooltip shown for the edit button on details page"}, "loadFullContent": "Carregar contingut", "@loadFullContent": {}, "noAppToDisplayPDFFilesFound": "Sense app per mostrar arxius PDF!", "@noAppToDisplayPDFFilesFound": {}, "openInSystemViewer": "Obrir amb visualitzador del sistema", "@openInSystemViewer": {}, "couldNotOpenFilePermissionDenied": "No es pot obrir arxiu: Accés denegat.", "@couldNotOpenFilePermissionDenied": {}, "previewTooltip": "Previsualitza", "@previewTooltip": {"description": "Tooltip shown for the preview button on details page"}, "shareTooltip": "Compartir", "@shareTooltip": {"description": "Tooltip shown for the share button on details page"}, "similarDocuments": "Documents Similars", "@similarDocuments": {"description": "Label shown in the tabbar on details page"}, "content": "Contingut", "@content": {"description": "Label shown in the tabbar on details page"}, "metaData": "<PERSON><PERSON>", "@metaData": {"description": "Label shown in the tabbar on details page"}, "overview": "Visió general", "@overview": {"description": "Label shown in the tabbar on details page"}, "documentType": "Tipus Document", "@documentType": {}, "archivedPdf": "Arxivat (pdf)", "@archivedPdf": {"description": "Option to chose when downloading a document"}, "chooseFiletype": "Escull tipus arxiu", "@chooseFiletype": {}, "original": "Original", "@original": {"description": "Option to chose when downloading a document"}, "documentSuccessfullyDownloaded": "Document descarregat correctament.", "@documentSuccessfullyDownloaded": {}, "suggestions": "Suggerències: ", "@suggestions": {}, "editDocument": "Edita Document", "@editDocument": {}, "advanced": "<PERSON><PERSON><PERSON><PERSON>", "@advanced": {}, "apply": "Aplica", "@apply": {}, "extended": "<PERSON><PERSON><PERSON><PERSON>", "@extended": {}, "titleAndContent": "Títol i contingut", "@titleAndContent": {}, "title": "Títol", "@title": {}, "reset": "<PERSON><PERSON><PERSON><PERSON>", "@reset": {}, "filterDocuments": "Filtra Documents", "@filterDocuments": {"description": "Title of the document filter"}, "originalMD5Checksum": "Original MD5-Checksum", "@originalMD5Checksum": {}, "mediaFilename": "Nom Arxiu", "@mediaFilename": {}, "originalFileSize": "Mida arxiu original", "@originalFileSize": {}, "originalMIMEType": "Original MIME-Type", "@originalMIMEType": {}, "modifiedAt": "Modificat", "@modifiedAt": {}, "preview": "Previsualitza", "@preview": {"description": "Title of the document preview page"}, "scanADocument": "Escaneja document", "@scanADocument": {}, "noDocumentsScannedYet": "Sense documents escanejats.", "@noDocumentsScannedYet": {}, "or": "o", "@or": {"description": "Used on the scanner page between both main actions when no scans have been captured."}, "deleteAllScans": "Esborra tots els escanejos", "@deleteAllScans": {}, "uploadADocumentFromThisDevice": "Puja document des d'aquest dispositiu", "@uploadADocumentFromThisDevice": {"description": "Button label on scanner page"}, "noMatchesFound": "Cap coincidència trobada.", "@noMatchesFound": {"description": "Displayed when no documents were found in the document search."}, "removeFromSearchHistory": "Elimina de l'historial de cerca?", "@removeFromSearchHistory": {}, "results": "Resultats", "@results": {"description": "Label displayed above search results in document search."}, "searchDocuments": "Cerca documents", "@searchDocuments": {}, "resetFilter": "<PERSON>ab<PERSON><PERSON>", "@resetFilter": {}, "lastMonth": "Mes passat", "@lastMonth": {}, "last7Days": "Últims 7 dies", "@last7Days": {}, "last3Months": "Últims 3 mesos", "@last3Months": {}, "lastYear": "Any passat", "@lastYear": {}, "search": "Cerca", "@search": {}, "documentsSuccessfullyDeleted": "Documents esborrats correctament.", "@documentsSuccessfullyDeleted": {}, "thereSeemsToBeNothingHere": "Sembla que no hi ha res...", "@thereSeemsToBeNothingHere": {}, "oops": "<PERSON><PERSON>.", "@oops": {}, "newDocumentAvailable": "Nou document discponible!", "@newDocumentAvailable": {}, "orderBy": "Ordena per", "@orderBy": {}, "thisActionIsIrreversibleDoYouWishToProceedAnyway": "Acció irreversible. Continuar?", "@thisActionIsIrreversibleDoYouWishToProceedAnyway": {}, "confirmDeletion": "<PERSON><PERSON><PERSON>", "@confirmDeletion": {}, "areYouSureYouWantToDeleteTheFollowingDocuments": "{count, plural, one{Segur que vols esborrar el següent document?} other{Segur que vols esborrar els següents documents?}}", "@areYouSureYouWantToDeleteTheFollowingDocuments": {"placeholders": {"count": {}}}, "countSelected": "{count} seleccionats", "@countSelected": {"description": "Displayed in the appbar when at least one document is selected.", "placeholders": {"count": {}}}, "storagePath": "<PERSON><PERSON> em<PERSON>", "@storagePath": {}, "prepareDocument": "Prepara document", "@prepareDocument": {}, "tags": "Etiquetes", "@tags": {}, "documentSuccessfullyUpdated": "Document actualitzat correctament.", "@documentSuccessfullyUpdated": {}, "fileName": "Nom d'arxiu", "@fileName": {}, "synchronizeTitleAndFilename": "Sincronitza títol i nom d'arxiu", "@synchronizeTitleAndFilename": {}, "reload": "Recarrega", "@reload": {}, "documentSuccessfullyUploadedProcessing": "Document carregat correctament, processant...", "@documentSuccessfullyUploadedProcessing": {}, "deleteLabelWarningText": "Aquesta etiqueta conté referències a altres documents. Si l'elimines totes les altres referències s'esborraran. Continuar?", "@deleteLabelWarningText": {}, "couldNotAcknowledgeTasks": "No es poden reconèixer les tasques.", "@couldNotAcknowledgeTasks": {}, "authenticationFailedPleaseTryAgain": "Autentificació fallida, torna a provar.", "@authenticationFailedPleaseTryAgain": {}, "anErrorOccurredWhileTryingToAutocompleteYourQuery": "Error al intentar autocompletar la cerca.", "@anErrorOccurredWhileTryingToAutocompleteYourQuery": {}, "biometricAuthenticationFailed": "Autenticació biomètrica ha fallat.", "@biometricAuthenticationFailed": {}, "biometricAuthenticationNotSupported": "L’autenticació biomètrica no és compatible en aquest dispositiu.", "@biometricAuthenticationNotSupported": {}, "couldNotBulkEditDocuments": "No es poden editar els documents de manera massiva.", "@couldNotBulkEditDocuments": {}, "couldNotCreateCorrespondent": "No es pot crear corresponsal, prova de nou.", "@couldNotCreateCorrespondent": {}, "couldNotLoadCorrespondents": "No es poden carregar corresponsals.", "@couldNotLoadCorrespondents": {}, "couldNotCreateSavedView": "No es pot crear vista desada, prova de nou.", "@couldNotCreateSavedView": {}, "couldNotDeleteSavedView": "No es pot esborrar vista guardada, prova de nou", "@couldNotDeleteSavedView": {}, "youAreCurrentlyOffline": "Estàs fora de línia. Assegura't d'estar connectat a internet.", "@youAreCurrentlyOffline": {}, "couldNotAssignArchiveSerialNumber": "No es pot assignar ASN.", "@couldNotAssignArchiveSerialNumber": {}, "couldNotDeleteDocument": "No es pot esborrar document, prova de nou.", "@couldNotDeleteDocument": {}, "couldNotLoadDocuments": "No es poden carregar documents, prova de nou.", "@couldNotLoadDocuments": {}, "couldNotLoadDocumentPreview": "No es pot carregar la vista prèvia.", "@couldNotLoadDocumentPreview": {}, "couldNotCreateDocument": "No es pot crear document, prova de nou.", "@couldNotCreateDocument": {}, "couldNotLoadDocumentTypes": "No es poden carregar tipus de documents, prova de nou.", "@couldNotLoadDocumentTypes": {}, "couldNotUpdateDocument": "No es pot actualitzar el document, prova de nou.", "@couldNotUpdateDocument": {}, "couldNotUploadDocument": "No es pot carregar document, prova de nou.", "@couldNotUploadDocument": {}, "invalidCertificateOrMissingPassphrase": "Certificat invàlid o sense paraula de pas", "@invalidCertificateOrMissingPassphrase": {}, "couldNotLoadSavedViews": "No es pot carregar vistes guardades.", "@couldNotLoadSavedViews": {}, "aClientCertificateWasExpectedButNotSent": "Certificat de client esperat però no trobat. Proporciona un certificat de client vàlid.", "@aClientCertificateWasExpectedButNotSent": {}, "userIsNotAuthenticated": "Usuari no autenticat.", "@userIsNotAuthenticated": {}, "requestTimedOut": "Esgotat el temps d'espera de connexió amb el servidor.", "@requestTimedOut": {}, "anErrorOccurredRemovingTheScans": "Error eliminant escanejos.", "@anErrorOccurredRemovingTheScans": {}, "couldNotReachYourPaperlessServer": "No es pot connectar al saervidor, està funcionant?", "@couldNotReachYourPaperlessServer": {}, "couldNotLoadSimilarDocuments": "No es poden carregar documents similars.", "@couldNotLoadSimilarDocuments": {}, "couldNotCreateStoragePath": "No es pot crear ruta emmagatzematge, prova de nou.", "@couldNotCreateStoragePath": {}, "couldNotLoadStoragePaths": "No es pot carregar ruta d'emmagatzematge.", "@couldNotLoadStoragePaths": {}, "couldNotLoadSuggestions": "No es poden carregar suggerències.", "@couldNotLoadSuggestions": {}, "couldNotCreateTag": "No es pot crear etiqueta, prova de nou.", "@couldNotCreateTag": {}, "couldNotLoadTags": "No es poden carregar etiquetes.", "@couldNotLoadTags": {}, "anUnknownErrorOccurred": "Error descon<PERSON>.", "@anUnknownErrorOccurred": {}, "fileFormatNotSupported": "Aquest format no és compatible.", "@fileFormatNotSupported": {}, "report": "REPORT", "@report": {}, "absolute": "Absolut", "@absolute": {}, "hintYouCanAlsoSpecifyRelativeValues": "Suggeriment: a part de les dates concretes, també es pot especificar un interval de temps relatiu a la data actual.", "@hintYouCanAlsoSpecifyRelativeValues": {"description": "Displayed in the extended date range picker"}, "amount": "Quantitat", "@amount": {}, "relative": "<PERSON><PERSON><PERSON>", "@relative": {}, "last": "Últim", "@last": {}, "timeUnit": "Unitat de temps", "@timeUnit": {}, "selectDateRange": "Rang de dates", "@selectDateRange": {}, "after": "<PERSON><PERSON><PERSON><PERSON>", "@after": {}, "before": "A<PERSON><PERSON>", "@before": {}, "days": "{count, plural, one{dia} other{dies}}", "@days": {"placeholders": {"count": {}}}, "lastNDays": "{count, plural, one{<PERSON>ir} other{Últims {count} dies}}", "@lastNDays": {"placeholders": {"count": {}}}, "lastNMonths": "{count, plural, one{Últim mes} other{Últims {count} mesos}}", "@lastNMonths": {"placeholders": {"count": {}}}, "lastNWeeks": "{count, plural, one{Últ<PERSON> setmana} other{Últimes {count} setmanes}}", "@lastNWeeks": {"placeholders": {"count": {}}}, "lastNYears": "{count, plural, one{<PERSON>ltim any} other{Últims {count} anys}}", "@lastNYears": {"placeholders": {"count": {}}}, "months": "{count, plural, one{mes} other{mesos}}", "@months": {"placeholders": {"count": {}}}, "weeks": "{count, plural, one{setmana} other{weeks}}", "@weeks": {"placeholders": {"count": {}}}, "years": "{count, plural, one{any} other{anys}}", "@years": {"placeholders": {"count": {}}}, "gotIt": "Ho tinc!", "@gotIt": {}, "cancel": "Cancel·la", "@cancel": {}, "close": "Tanca", "@close": {}, "create": "<PERSON><PERSON>", "@create": {}, "delete": "Esborra", "@delete": {}, "edit": "<PERSON><PERSON>", "@edit": {}, "ok": "Ok", "@ok": {}, "save": "<PERSON><PERSON>", "@save": {}, "select": "Selecciona", "@select": {}, "saveChanges": "<PERSON><PERSON> can<PERSON>", "@saveChanges": {}, "upload": "Carrega", "@upload": {}, "youreOffline": "Sense connexió.", "@youreOffline": {}, "deleteDocument": "Esborra document", "@deleteDocument": {"description": "Used as an action label on each inbox item"}, "removeDocumentFromInbox": "Document esborrat de la safata d'entrada.", "@removeDocumentFromInbox": {}, "areYouSureYouWantToMarkAllDocumentsAsSeen": "Segur que vols marcar tots els documents com a vistos? Això realitzarà una operació massiva eliminant totes les etiquetes de la safata d'entrada dels documents. Aquesta acció no és reversible! Continuar?", "@areYouSureYouWantToMarkAllDocumentsAsSeen": {}, "markAllAsSeen": "Marca tot com a llegit?", "@markAllAsSeen": {}, "allSeen": "<PERSON><PERSON> vist", "@allSeen": {}, "markAsSeen": "Marca com a llegit", "@markAsSeen": {}, "refresh": "Refresca", "@refresh": {}, "youDoNotHaveUnseenDocuments": "Sense document no vistos.", "@youDoNotHaveUnseenDocuments": {}, "quickAction": "<PERSON><PERSON><PERSON>", "@quickAction": {}, "suggestionSuccessfullyApplied": "Suggerència aplicada correctament.", "@suggestionSuccessfullyApplied": {}, "today": "<PERSON><PERSON><PERSON>", "@today": {}, "undo": "<PERSON><PERSON>", "@undo": {}, "nUnseen": "{count} no vist", "@nUnseen": {"placeholders": {"count": {}}}, "swipeLeftToMarkADocumentAsSeen": "Suggeriment: llisca el dit cap a l'esquerra per marcar un document com a vist i eliminar totes les etiquetes del document.", "@swipeLeftToMarkADocumentAsSeen": {}, "yesterday": "<PERSON><PERSON>", "@yesterday": {}, "anyAssigned": "Qualsevol assignat", "@anyAssigned": {}, "noItemsFound": "Elements no trobats!", "@noItemsFound": {}, "caseIrrelevant": "No distingeix majúscules-minúscules", "@caseIrrelevant": {}, "matchingAlgorithm": "Algoritme coincident", "@matchingAlgorithm": {}, "match": "Coincidència", "@match": {}, "name": "Nom", "@name": {}, "notAssigned": "No assignat", "@notAssigned": {}, "addNewCorrespondent": "Crear nou corresponsal", "@addNewCorrespondent": {}, "noCorrespondentsSetUp": "No has afegit cap corresponsal.", "@noCorrespondentsSetUp": {}, "correspondents": "Corresponsals", "@correspondents": {}, "addNewDocumentType": "Crea nou tipus de document", "@addNewDocumentType": {}, "noDocumentTypesSetUp": "No has afegit cap tipus de document.", "@noDocumentTypesSetUp": {}, "documentTypes": "Tipus Documents", "@documentTypes": {}, "addNewStoragePath": "Nova ruta emmagatzematge", "@addNewStoragePath": {}, "noStoragePathsSetUp": "No has afegit cap ruta d'emmagatzematge.", "@noStoragePathsSetUp": {}, "storagePaths": "Rutes emmagatzematge", "@storagePaths": {}, "addNewTag": "Afegir nova etiqueta", "@addNewTag": {}, "noTagsSetUp": "No has afegit cap etiqueta.", "@noTagsSetUp": {}, "linkedDocuments": "Documents enllaçats", "@linkedDocuments": {}, "advancedSettings": "Opcions avançades", "@advancedSettings": {}, "passphrase": "Contrasenya", "@passphrase": {}, "configureMutualTLSAuthentication": "Configura Autentificació TLS mútua", "@configureMutualTLSAuthentication": {}, "invalidCertificateFormat": "Format de certificat invàlid, només s'accepta .pfx", "@invalidCertificateFormat": {}, "clientcertificate": "Certificat de client", "@clientcertificate": {}, "selectFile": "Selecciona Fitxer...", "@selectFile": {}, "continueLabel": "Continua", "@continueLabel": {}, "incorrectOrMissingCertificatePassphrase": "Incorrecte o falta de contrasenya de certificat.", "@incorrectOrMissingCertificatePassphrase": {}, "connect": "Connecta", "@connect": {}, "password": "Contrasenya", "@password": {}, "passwordMustNotBeEmpty": "Contrasenya no pot ser buida.", "@passwordMustNotBeEmpty": {}, "connectionTimedOut": "Temps de connexió exhaurit.", "@connectionTimedOut": {}, "loginPageReachabilityMissingClientCertificateText": "Certificat de client esperat però no trobat. Proporciona un certificat.", "@loginPageReachabilityMissingClientCertificateText": {}, "couldNotEstablishConnectionToTheServer": "No es pot establir una connexió amb el servidor.", "@couldNotEstablishConnectionToTheServer": {}, "connectionSuccessfulylEstablished": "Connexió establerta correctament.", "@connectionSuccessfulylEstablished": {}, "hostCouldNotBeResolved": "No es pot connectar al host. Comprova l'adreça del servidor i la connexió a internet. ", "@hostCouldNotBeResolved": {}, "serverAddress": "<PERSON><PERSON><PERSON>", "@serverAddress": {}, "invalidAddress": "<PERSON><PERSON><PERSON>.", "@invalidAddress": {}, "serverAddressMustIncludeAScheme": "Adreça del servidor ha d'incloure esquema.", "@serverAddressMustIncludeAScheme": {}, "serverAddressMustNotBeEmpty": "Adreça del servidor no pot estar buida.", "@serverAddressMustNotBeEmpty": {}, "signIn": "Entrar", "@signIn": {}, "loginPageSignInTitle": "Entrar", "@loginPageSignInTitle": {}, "signInToServer": "Entrar a {serverAddress}", "@signInToServer": {"placeholders": {"serverAddress": {}}}, "connectToPaperless": "Connecta a Paperless", "@connectToPaperless": {}, "username": "Nom usuari", "@username": {}, "usernameMustNotBeEmpty": "Nom d'usuari no pot estar buit.", "@usernameMustNotBeEmpty": {}, "documentContainsAllOfTheseWords": "Document conté totes aquestes paraules", "@documentContainsAllOfTheseWords": {}, "all": "<PERSON><PERSON>", "@all": {}, "documentContainsAnyOfTheseWords": "Document conté qualsevol d'aquestes paraules", "@documentContainsAnyOfTheseWords": {}, "any": "Qualsevol", "@any": {}, "learnMatchingAutomatically": "Aprenentatge automàtic", "@learnMatchingAutomatically": {}, "auto": "Auto", "@auto": {}, "documentContainsThisString": "Document conté la cadena", "@documentContainsThisString": {}, "exact": "Exacte", "@exact": {}, "documentContainsAWordSimilarToThisWord": "Document conté una paraula semblant a aquesta", "@documentContainsAWordSimilarToThisWord": {}, "fuzzy": "Di<PERSON>ús", "@fuzzy": {}, "documentMatchesThisRegularExpression": "Document coincideix amb aquesta expresió regular", "@documentMatchesThisRegularExpression": {}, "regularExpression": "Expressió Regular", "@regularExpression": {}, "anInternetConnectionCouldNotBeEstablished": "No s'ha pogut establir una connexió a internet.", "@anInternetConnectionCouldNotBeEstablished": {}, "done": "Fet", "@done": {}, "next": "<PERSON><PERSON><PERSON><PERSON>", "@next": {}, "couldNotAccessReceivedFile": "No es pot accedir al fitxer rebut. Prova d'obrir l'app abans de compartir.", "@couldNotAccessReceivedFile": {}, "newView": "Nova Vista", "@newView": {}, "createsASavedViewBasedOnTheCurrentFilterCriteria": "Crea una nova vista basada en els filtres actuals.", "@createsASavedViewBasedOnTheCurrentFilterCriteria": {}, "createViewsToQuicklyFilterYourDocuments": "Crea vistes per filtrar documents de manera ràpida.", "@createViewsToQuicklyFilterYourDocuments": {}, "nFiltersSet": "{count, plural, one{{count} filtre aplicat} other{{count} filtres aplicats}}", "@nFiltersSet": {"placeholders": {"count": {}}}, "showInSidebar": "Mostra a la barra lateral", "@showInSidebar": {}, "showOnDashboard": "Mostra al panell", "@showOnDashboard": {}, "views": "<PERSON><PERSON><PERSON>", "@views": {}, "clearAll": "<PERSON><PERSON><PERSON> tot", "@clearAll": {}, "scan": "Escaneja", "@scan": {}, "previewScan": "Previsualitza", "@previewScan": {}, "scrollToTop": "Torna a dalt", "@scrollToTop": {}, "paperlessServerVersion": "<PERSON>ers<PERSON><PERSON> servidor <PERSON>less", "@paperlessServerVersion": {}, "darkTheme": "Tema fosc", "@darkTheme": {}, "lightTheme": "Tema clar", "@lightTheme": {}, "systemTheme": "Utilitza tema del sistema", "@systemTheme": {}, "appearance": "Aparença", "@appearance": {}, "languageAndVisualAppearance": "Llengua i aspecte visual", "@languageAndVisualAppearance": {}, "applicationSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@applicationSettings": {}, "colorSchemeHint": "Tria entre un esquema de colors clàssic inspirat en un verd tradicional de Paperless o utilitzeu l'esquema de colors dinàmic basat en el tema del vostre sistema.", "@colorSchemeHint": {}, "colorSchemeNotSupportedWarning": "Temàtica dinàmica només admès per a dispositius amb Android 12 i versions posteriors. La selecció de l'opció \"Dinàmica\" pot no tenir cap efecte en funció de la implementació del vostre sistema operatiu.", "@colorSchemeNotSupportedWarning": {}, "colors": "Colors", "@colors": {}, "language": "Idioma", "@language": {}, "security": "Se<PERSON><PERSON><PERSON>", "@security": {}, "mangeFilesAndStorageSpace": "Gestioneu fitxers i espais d'emmagatzematge", "@mangeFilesAndStorageSpace": {}, "storage": "Emmagatzematge", "@storage": {}, "dark": "Fosc", "@dark": {}, "light": "<PERSON><PERSON>", "@light": {}, "system": "Sistema", "@system": {}, "ascending": "Ascendent", "@ascending": {}, "descending": "Descendent", "@descending": {}, "storagePathDay": "dia", "@storagePathDay": {}, "storagePathMonth": "mes", "@storagePathMonth": {}, "storagePathYear": "any", "@storagePathYear": {}, "color": "Color", "@color": {}, "filterTags": "Filtra etiquetes...", "@filterTags": {}, "inboxTag": "Etiqueta d'entrada", "@inboxTag": {}, "uploadInferValuesHint": "Si especifiques valors per a aquests camps, la instància de Paperless no obtindrà un valor automàticament. Deixa els camps en blanc si vols que el vostre servidor els ompli automàticament.", "@uploadInferValuesHint": {}, "useTheConfiguredBiometricFactorToAuthenticate": "Utilitza el factor biomètric per autenticar i desbloquejar els documents.", "@useTheConfiguredBiometricFactorToAuthenticate": {}, "verifyYourIdentity": "Verifica la identitat", "@verifyYourIdentity": {}, "verifyIdentity": "Verifica identitat", "@verifyIdentity": {}, "detailed": "<PERSON><PERSON>", "@detailed": {}, "grid": "<PERSON><PERSON><PERSON>", "@grid": {}, "list": "Llista", "@list": {}, "remove": "Esborra", "removeQueryFromSearchHistory": "Elimina consulta de l'historial de cerca?", "dynamicColorScheme": "<PERSON><PERSON><PERSON>", "@dynamicColorScheme": {}, "classicColorScheme": "Clàssic", "@classicColorScheme": {}, "notificationDownloadComplete": "<PERSON><PERSON><PERSON><PERSON><PERSON> com<PERSON>", "@notificationDownloadComplete": {"description": "Notification title when a download has been completed."}, "notificationDownloadingDocument": "Descarregant document", "@notificationDownloadingDocument": {"description": "Notification title shown when a document download is pending"}, "archiveSerialNumberUpdated": "ASN actualitzat.", "@archiveSerialNumberUpdated": {"description": "Message shown when the ASN has been updated."}, "donateCoffee": "Compra'm un café", "@donateCoffee": {"description": "Label displayed in the app drawer"}, "thisFieldIsRequired": "Aquest camp és obligatori!", "@thisFieldIsRequired": {"description": "Message shown below the form field when a required field has not been filled out."}, "confirm": "Confirma", "confirmAction": "<PERSON><PERSON><PERSON><PERSON>", "@confirmAction": {"description": "Typically used as a title to confirm a previously selected action"}, "areYouSureYouWantToContinue": "Segur que vols continuar?", "bulkEditTagsAddMessage": "{count, plural, one{Això afegira l'etiqueta {tags} al document seleccionat.} other{Això afegira les etiquetes {tags} a {count} documents seleccionats.}}", "@bulkEditTagsAddMessage": {"description": "Message of the confirmation dialog when bulk adding tags."}, "bulkEditTagsRemoveMessage": "{count, plural, one{Això eliminarà l'etiqueta {tags} del document seleccionat.} other{Això eliminarà les etiquetes {tags} dels {count} documents seleccionats.}}", "@bulkEditTagsRemoveMessage": {"description": "Message of the confirmation dialog when bulk removing tags."}, "bulkEditTagsModifyMessage": "{count, plural, one{<PERSON><PERSON>ò afegirà les etiquetes {addTags} i esborrarà les etiquetes {removeTags} del document seleccionat.} other{Això afegirà les etiquetes {addTags} i esborrarà les etiquetes {removeTags} dels {count} documents seleccionats.}}", "@bulkEditTagsModifyMessage": {"description": "Message of the confirmation dialog when both adding and removing tags."}, "bulkEditCorrespondentAssignMessage": "{count, plural, one{<PERSON><PERSON>ò assignarà el corresponsal {correspondent} al document seleccionat.} other{Això assignarà el corresponsal {correspondent} als {count} documents seleccionats.}}", "bulkEditDocumentTypeAssignMessage": "{count, plural, one{Això assignarà el tipus de document {docType} al document seleccionat.} other{Això assignarà el tipus de document {docType} als {count} documents seleccionats.}}", "bulkEditStoragePathAssignMessage": "{count, plural, one{Això assignarà la ruta d'emmagatzematge {path} al document seleccionat.} other{Això assignarà la ruta d'emmagatzematge {path} als {count} documents seleccionats.}}", "bulkEditCorrespondentRemoveMessage": "{count, plural, one{Això esborrarà el corresponsal del document seleccionat.} other{Això esborrarà el corresponsal dels {count} documents seleccionats.}}", "bulkEditDocumentTypeRemoveMessage": "{count, plural, one{Això esborrarà el tipus de document del document seleccionat.} other{Això esborrarà el tipus de document dels {count} documents seleccionats.}}", "bulkEditStoragePathRemoveMessage": "{count, plural, one{Això esborrarà la ruta d'emmagatzematge del document selccionat.} other{Això esborrarà la ruta d'emmagatzematge dels {count} documents seleccionats.}}", "anyTag": "Qualsevol", "@anyTag": {"description": "Label shown when any tag should be filtered"}, "allTags": "<PERSON><PERSON>", "@allTags": {"description": "Label shown when a document has to be assigned to all selected tags"}, "switchingAccountsPleaseWait": "Canviant de compte. Espera...", "@switchingAccountsPleaseWait": {"description": "Message shown while switching accounts is in progress."}, "testConnection": "Prova connexió", "@testConnection": {"description": "Button label shown on login page. Allows user to test whether the server is reachable or not."}, "accounts": "<PERSON><PERSON><PERSON>", "@accounts": {"description": "Title of the account management dialog"}, "addAccount": "A<PERSON>gir compte", "@addAccount": {"description": "Label of add account action"}, "switchAccount": "Alterna", "@switchAccount": {"description": "Label for switch account action"}, "logout": "<PERSON><PERSON>", "@logout": {"description": "Generic Logout label"}, "switchAccountTitle": "<PERSON><PERSON> de compte", "@switchAccountTitle": {"description": "Title of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "switchToNewAccount": "Vols canviar al nou compte? Pots canviar en qualsevol moment.", "@switchToNewAccount": {"description": "Content of the dialog shown after adding an account, asking the user whether to switch to the newly added account or not."}, "sourceCode": "Codi Font", "findTheSourceCodeOn": "Troba el codi font a", "@findTheSourceCodeOn": {"description": "Text before link to Paperless Mobile GitHub"}, "rememberDecision": "Recorda la selecció", "defaultDownloadFileType": "<PERSON><PERSON><PERSON> de Fitxer a descarregar per defecte", "@defaultDownloadFileType": {"description": "Label indicating the default filetype to download (one of archived, original and always ask)"}, "defaultShareFileType": "Tipus compartició de fitxer per defecte", "@defaultShareFileType": {"description": "Label indicating the default filetype to share (one of archived, original and always ask)"}, "alwaysAsk": "Pregunta sempre", "@alwaysAsk": {"description": "Option to choose when the app should always ask the user which filetype to use"}, "disableMatching": "No etiquetar documents automaticament", "@disableMatching": {"description": "One of the options for automatic tagging of documents"}, "none": "Cap", "@none": {"description": "One of available enum values of matching algorithm for tags"}, "logInToExistingAccount": "Entrar en compte existent", "@logInToExistingAccount": {"description": "Title shown on login page if at least one user is already known to the app."}, "print": "Imprimir", "@print": {"description": "Tooltip for print button"}, "managePermissions": "Gestionar permisos", "@managePermissions": {"description": "Button which leads user to manage permissions page"}, "errorRetrievingServerVersion": "Error en determinar la versió del servidor.", "@errorRetrievingServerVersion": {"description": "Message shown at the bottom of the settings page when the remote server version could not be resolved."}, "resolvingServerVersion": "Determinant versió del servidor...", "@resolvingServerVersion": {"description": "Message shown while the app is loading the remote server version."}, "goToLogin": "<PERSON><PERSON> al login", "@goToLogin": {"description": "Label of the button shown on the login page to skip logging in to existing accounts and navigate user to login page"}, "export": "Exporta", "@export": {"description": "Label for button that exports scanned images to pdf (before upload)"}, "invalidFilenameCharacter": "Caràcter(s) invàlids trobats a nom d'arxiu: {characters}", "@invalidFilenameCharacter": {"description": "For validating filename in export dialogue"}, "exportScansToPdf": "Exporta escanejos a PDF", "@exportScansToPdf": {"description": "title of the alert dialog when exporting scans to pdf"}, "allScansWillBeMerged": "Els escanejos s'uniran en un únic document PDF.", "behavior": "Comportament", "@behavior": {"description": "Title of the settings concerning app beahvior"}, "theme": "<PERSON><PERSON>", "@theme": {"description": "Title of the theme mode setting"}, "clearCache": "Esborra cache", "@clearCache": {"description": "Title of the clear cache setting"}, "freeBytes": "Lliure {byteString}", "@freeBytes": {"description": "Text shown for clear storage settings"}, "calculatingDots": "Calculant...", "@calculatingDots": {"description": "Text shown when the byte size is still being calculated"}, "freedDiskSpace": "Alliberats {bytes} d'espai de disc.", "@freedDiskSpace": {"description": "Message shown after clearing storage"}, "uploadScansAsPdf": "Puja escanejos com a PDF", "@uploadScansAsPdf": {"description": "Title of the setting which toggles whether scans are always uploaded as pdf"}, "convertSinglePageScanToPdf": "Coverteix únics escanejos a PDF abans de pujar", "@convertSinglePageScanToPdf": {"description": "description of the upload scans as pdf setting"}, "loginRequiredPermissionsHint": "L'ús de Paperless Mobile requereix un conjunt mínim de permisos d'usuari des de paperless-ngx 1.14.0 i posterior. Per tant, assegureu-vos que l'usuari que voleu iniciar sessió té el permís per veure altres usuaris (Usuari → Visualització) i la configuració (UISettings → Visualització). Si no teniu aquests permisos, poseu-vos en contacte amb un administrador del vostre servidor paperless-ngx.", "@loginRequiredPermissionsHint": {"description": "Hint shown on the login page informing the user of the required permissions to use the app."}, "missingPermissions": "No tens els permisos necessaris per a completar l'acció.", "@missingPermissions": {"description": "Message shown in a snackbar when a user without the reequired permissions performs an action."}, "editView": "Editar <PERSON>", "@editView": {"description": "Title of the edit saved view page"}, "donate": "Donar", "@donate": {"description": "Label of the in-app donate button"}, "donationDialogContent": "Gràcies per considerar donar suport a aquesta aplicació! A causa de les polítiques de pagament de Google i d'Apple, no es pot mostrar cap enllaç a les donacions a l'aplicació. Ni tan sols l'enllaç a la pàgina del repositori del projecte sembla que estigui permès en aquest context. Per tant, potser feu una ullada a la secció 'Donacions' del README del projecte. El vostre suport és molt apreciat i manté viu el desenvolupament d'aquesta aplicació. Gràcies!", "@donationDialogContent": {"description": "Text displayed in the donation dialog"}, "noDocumentsFound": "Sense Documents trobats.", "@noDocumentsFound": {"description": "Message shown when no documents were found."}, "couldNotDeleteCorrespondent": "No es pot esborrar corresponsal, torna a provar.", "@couldNotDeleteCorrespondent": {"description": "Message shown in snackbar when a correspondent could not be deleted."}, "couldNotDeleteDocumentType": "No es pot esborrar document, prova de nou.", "@couldNotDeleteDocumentType": {"description": "Message shown when a document type could not be deleted"}, "couldNotDeleteTag": "No es pot esborrar etiqueta, prova de nou.", "@couldNotDeleteTag": {"description": "Message shown when a tag could not be deleted"}, "couldNotDeleteStoragePath": "No es pot esborrar ruta emmagatzematge, prova de nou.", "@couldNotDeleteStoragePath": {"description": "Message shown when a storage path could not be deleted"}, "couldNotUpdateCorrespondent": "No es pot actualitzar corresponsal, prova de nou.", "@couldNotUpdateCorrespondent": {"description": "Message shown when a correspondent could not be updated"}, "couldNotUpdateDocumentType": "No es pot actualitzar tipus de document, prova de nou.", "@couldNotUpdateDocumentType": {"description": "Message shown when a document type could not be updated"}, "couldNotUpdateTag": "No es pot actualitzar etiqueta, prova de nou.", "@couldNotUpdateTag": {"description": "Message shown when a tag could not be updated"}, "couldNotLoadServerInformation": "No es pot carregar informació del servidor.", "@couldNotLoadServerInformation": {"description": "Message shown when the server information could not be loaded"}, "couldNotLoadStatistics": "No es poden carregar estadístiques del servidor.", "@couldNotLoadStatistics": {"description": "Message shown when the server statistics could not be loaded"}, "couldNotLoadUISettings": "No es pot carregar l'interfície.", "@couldNotLoadUISettings": {"description": "Message shown when the UI settings could not be loaded"}, "couldNotLoadTasks": "No es poden carregar les tasques.", "@couldNotLoadTasks": {"description": "Message shown when the tasks (e.g. document consumed) could not be loaded"}, "userNotFound": "<PERSON><PERSON>ri no trobat.", "@userNotFound": {"description": "Message shown when the specified user (e.g. by id) could not be found"}, "couldNotUpdateSavedView": "No es poden acualitzar les vistes desades, prova de nou.", "@couldNotUpdateSavedView": {"description": "Message shown when a saved view could not be updated"}, "couldNotUpdateStoragePath": "No es pot actualitzar la ruta emmagatzematge, prova de nou.", "savedViewSuccessfullyUpdated": "Vista desada actualitzada correctament.", "@savedViewSuccessfullyUpdated": {"description": "Message shown when a saved view was successfully updated."}, "discardChanges": "<PERSON><PERSON><PERSON> canvis?", "@discardChanges": {"description": "Title of the alert dialog shown when a user tries to close a view with unsaved changes."}, "savedViewChangedDialogContent": "Les condicions del filtre de la vista activa han canviat. En restablir el filtre, aquests canvis es perdran. Encara vols continuar?", "@savedViewChangedDialogContent": {"description": "Content of the alert dialog shown when all of the following applies:\r\n* User has saved view selected\r\n* User has performed changes to the current document filter\r\n* User now tries to reset this filter without having saved the changes to the view."}, "createFromCurrentFilter": "<PERSON><PERSON><PERSON> desde filtre actual", "@createFromCurrentFilter": {"description": "Tooltip of the \"New saved view\" button"}, "home": "Inici", "@home": {"description": "Label of the \"Home\" route"}, "welcomeUser": "Benvingut {name}!", "@welcomeUser": {"description": "Top message shown on the home page"}, "statistics": "Estadí­stiques", "documentsInInbox": "Document safata", "totalDocuments": "Total documents", "totalCharacters": "Caràcters Totals", "showAll": "<PERSON>ra tot", "@showAll": {"description": "Button label shown on a saved view preview to open this view in the documents page"}, "userAlreadyExists": "Us<PERSON>ri ja esisteix.", "@userAlreadyExists": {"description": "Error message shown when the user tries to add an already existing account."}, "youDidNotSaveAnyViewsYet": "Encara no has desat cap visualització, crea'n una i es mostrarà aquí.", "@youDidNotSaveAnyViewsYet": {"description": "Message shown when there are no saved views yet."}, "tryAgain": "<PERSON>na a provar", "discardFile": "Desestimar arxiu?", "discard": "<PERSON><PERSON><PERSON>", "backToLogin": "Torna a inici de sessió", "skipEditingReceivedFiles": "Salta l'edició de fitxers rebuts", "uploadWithoutPromptingUploadForm": "Pengeu sempre sense demanar el formulari de càrrega quan compartiu fitxers amb l'aplicació.", "authenticatingDots": "Autenticant…", "@authenticatingDots": {"description": "Message shown when the app is authenticating the user"}, "persistingUserInformation": "Informació de l'usuari persistent...", "fetchingUserInformation": "Obtenint dades d'usuari...", "@fetchingUserInformation": {"description": "Message shown when the app loads user data from the server"}, "restoringSession": "Restaurant sessió...", "@restoringSession": {"description": "Message shown when the user opens the app and the previous user is tried to be authenticated and logged in"}, "documentsAssigned": "{count, plural, zero{No documents} one{1 document} other{{count} documents}}", "@documentsAssigned": {"description": "Text shown with a correspondent, document type etc. to indicate the number of documents this filter will maximally yield."}, "discardChangesWarning": "Tens canvis sense desar. <PERSON> continues es perdran. Vols descartar els canvis?", "@discardChangesWarning": {"description": "Warning message shown when the user tries to close a route without saving the changes."}, "changelog": "<PERSON><PERSON> de canvis", "noLogsFoundOn": "Sense logs trovats per {date}.", "logfileBottomReached": "Final d'aquest arxiu de registres.", "appLogs": "Logs d'aplicació {date}", "saveLogsToFile": "Desar registres a arxiu", "copyToClipboard": "Copia al porta-retalls", "couldNotLoadLogfileFrom": "No es pot carregar log desde {date}.", "loadingLogsFrom": "Carregant registres des de {date}...", "clearLogs": "Netejar registres des de {date}", "showPdf": "Mostra PDF", "@showPdf": {"description": "Tooltip shown on the \"show pdf\" button on the document edit page"}, "hidePdf": "Oculta PDF", "@hidePdf": {"description": "Tooltip shown on the \"show pdf\" icon button on the document edit page"}, "misc": "<PERSON>s<PERSON>·lanni", "loggingOut": "Sortint...", "testingConnection": "Provant connexió...", "@testingConnection": {"description": "Text shown while the app tries to establish a connection to the specified host."}, "version": "Versió {versionCode}", "notes": "{count, plural, zero{Notes} one{Nota} other{Notes}}", "addNote": "<PERSON><PERSON><PERSON><PERSON>", "newerVersionAvailable": "Newer version available:", "dateOutOfRange": "Date must be between {firstDate} and {lastDate}.", "@dateOutOfRange": {"description": "Error message shown when the user tries to select a date outside of the allowed range.", "placeholders": {"firstDate": {"type": "DateTime", "format": "yMd"}, "lastDate": {"type": "DateTime", "format": "yMd"}}}, "permissions": "Permissions", "newNote": "New note", "notesMarkdownSyntaxSupportHint": "Paperless Mobile can render notes using basic markdown syntax. Try it out!"}
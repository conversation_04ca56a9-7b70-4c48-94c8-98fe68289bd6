// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_query.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TextQueryAdapter extends TypeAdapter<TextQuery> {
  @override
  final int typeId = 114;

  @override
  TextQuery read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TextQuery(
      queryType: fields[0] as QueryType,
      queryText: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, TextQuery obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.queryType)
      ..writeByte(1)
      ..write(obj.queryText);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TextQueryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_details_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DocumentDetailsState {
  LoadingStatus get status => throw _privateConstructorUsedError;
  DocumentModel? get document => throw _privateConstructorUsedError;
  DocumentMetaData? get metaData => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DocumentDetailsStateCopyWith<DocumentDetailsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentDetailsStateCopyWith<$Res> {
  factory $DocumentDetailsStateCopyWith(DocumentDetailsState value,
          $Res Function(DocumentDetailsState) then) =
      _$DocumentDetailsStateCopyWithImpl<$Res, DocumentDetailsState>;
  @useResult
  $Res call(
      {LoadingStatus status,
      DocumentModel? document,
      DocumentMetaData? metaData});
}

/// @nodoc
class _$DocumentDetailsStateCopyWithImpl<$Res,
        $Val extends DocumentDetailsState>
    implements $DocumentDetailsStateCopyWith<$Res> {
  _$DocumentDetailsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? document = freezed,
    Object? metaData = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as LoadingStatus,
      document: freezed == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as DocumentModel?,
      metaData: freezed == metaData
          ? _value.metaData
          : metaData // ignore: cast_nullable_to_non_nullable
              as DocumentMetaData?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DocumentDetailsStateImplCopyWith<$Res>
    implements $DocumentDetailsStateCopyWith<$Res> {
  factory _$$DocumentDetailsStateImplCopyWith(_$DocumentDetailsStateImpl value,
          $Res Function(_$DocumentDetailsStateImpl) then) =
      __$$DocumentDetailsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingStatus status,
      DocumentModel? document,
      DocumentMetaData? metaData});
}

/// @nodoc
class __$$DocumentDetailsStateImplCopyWithImpl<$Res>
    extends _$DocumentDetailsStateCopyWithImpl<$Res, _$DocumentDetailsStateImpl>
    implements _$$DocumentDetailsStateImplCopyWith<$Res> {
  __$$DocumentDetailsStateImplCopyWithImpl(_$DocumentDetailsStateImpl _value,
      $Res Function(_$DocumentDetailsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? document = freezed,
    Object? metaData = freezed,
  }) {
    return _then(_$DocumentDetailsStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as LoadingStatus,
      document: freezed == document
          ? _value.document
          : document // ignore: cast_nullable_to_non_nullable
              as DocumentModel?,
      metaData: freezed == metaData
          ? _value.metaData
          : metaData // ignore: cast_nullable_to_non_nullable
              as DocumentMetaData?,
    ));
  }
}

/// @nodoc

class _$DocumentDetailsStateImpl implements _DocumentDetailsState {
  const _$DocumentDetailsStateImpl(
      {this.status = LoadingStatus.initial, this.document, this.metaData});

  @override
  @JsonKey()
  final LoadingStatus status;
  @override
  final DocumentModel? document;
  @override
  final DocumentMetaData? metaData;

  @override
  String toString() {
    return 'DocumentDetailsState(status: $status, document: $document, metaData: $metaData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentDetailsStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.document, document) ||
                other.document == document) &&
            (identical(other.metaData, metaData) ||
                other.metaData == metaData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status, document, metaData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentDetailsStateImplCopyWith<_$DocumentDetailsStateImpl>
      get copyWith =>
          __$$DocumentDetailsStateImplCopyWithImpl<_$DocumentDetailsStateImpl>(
              this, _$identity);
}

abstract class _DocumentDetailsState implements DocumentDetailsState {
  const factory _DocumentDetailsState(
      {final LoadingStatus status,
      final DocumentModel? document,
      final DocumentMetaData? metaData}) = _$DocumentDetailsStateImpl;

  @override
  LoadingStatus get status;
  @override
  DocumentModel? get document;
  @override
  DocumentMetaData? get metaData;
  @override
  @JsonKey(ignore: true)
  _$$DocumentDetailsStateImplCopyWith<_$DocumentDetailsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

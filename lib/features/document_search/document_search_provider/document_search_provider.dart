import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import 'package:paperless_mobile/core/database/hive/hive_config.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/database/tables/local_user_app_state.dart';
import 'package:paperless_mobile/features/document_search/cubit/document_search_cubit.dart';
import 'package:paperless_mobile/features/document_search/view/document_search_page.dart';

class DocumentSearchProvider extends StatelessWidget {
  const DocumentSearchProvider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DocumentSearchCubit(
        context.read(),
        context.read(),
        Hive.box<LocalUserAppState>(HiveBoxes.localUserAppState)
            .get(context.read<LocalUserAccount>().id)!,
        context.read(),
      ),
      child: const DocumentSearchPage(),
    );
  }
}

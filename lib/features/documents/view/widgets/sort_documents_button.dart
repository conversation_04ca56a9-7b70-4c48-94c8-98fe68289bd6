import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/translation/sort_field_localization_mapper.dart';
import 'package:paperless_mobile/features/documents/cubit/documents_cubit.dart';
import 'package:paperless_mobile/features/documents/view/widgets/search/sort_field_selection_bottom_sheet.dart';
import 'package:paperless_mobile/features/labels/cubit/label_cubit.dart';
import 'package:paperless_mobile/helpers/connectivity_aware_action_wrapper.dart';

class SortDocumentsButton extends StatefulWidget {
  final bool enabled;
  const SortDocumentsButton({
    super.key,
    this.enabled = true,
  });

  @override
  State<SortDocumentsButton> createState() => _SortDocumentsButtonState();
}

class _SortDocumentsButtonState extends State<SortDocumentsButton> {
  bool _isBottomSheetOpen = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DocumentsCubit, DocumentsState>(
      builder: (context, state) {
        if (state.filter.sortField == null) {
          return const SizedBox.shrink();
        }

        final animatedIcon = AnimatedRotation(
          turns: _isBottomSheetOpen
              ? 0.5
              : 0, // 0.5 = 180 degrees (up), 0 = 0 degrees (down)
          duration: const Duration(milliseconds: 200),
          child: Icon(
            state.filter.sortOrder == SortOrder.ascending
                ? Icons.keyboard_arrow_up
                : Icons.keyboard_arrow_down,
            color: AppColor.primary,
          ),
        );

        final label = Text(
          translateSortField(context, state.filter.sortField),
          style: const TextStyle(color: AppColor.primary),
        );

        return ConnectivityAwareActionWrapper(
          offlineBuilder: (context, child) {
            return TextButton(
              onPressed: null,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  label,
                  animatedIcon,
                ],
              ),
            );
          },
          child: TextButton(
            onPressed: widget.enabled
                ? () {
                    // Trigger animation when button is pressed
                    setState(() {
                      _isBottomSheetOpen = true;
                    });

                    showModalBottomSheet(
                      backgroundColor: AppColor.white,
                      elevation: 2,
                      context: context,
                      isScrollControlled: true,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      builder: (_) => BlocProvider<DocumentsCubit>.value(
                        value: context.read<DocumentsCubit>(),
                        child: MultiBlocProvider(
                          providers: [
                            BlocProvider(
                              create: (context) => LabelCubit(context.read()),
                            ),
                          ],
                          child: Material(
                            color: Colors.white,
                            child: SortFieldSelectionBottomSheet(
                              initialSortField: state.filter.sortField,
                              initialSortOrder: state.filter.sortOrder,
                              onSubmit: (field, order) {
                                return context
                                    .read<DocumentsCubit>()
                                    .updateCurrentFilter(
                                      (filter) => filter.copyWith(
                                        sortField: field,
                                        sortOrder: order,
                                      ),
                                    );
                              },
                            ),
                          ),
                        ),
                      ),
                    ).then((_) {
                      // Reset animation when bottom sheet is closed
                      if (mounted) {
                        setState(() {
                          _isBottomSheetOpen = false;
                        });
                      }
                    });
                  }
                : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                label,
                animatedIcon,
              ],
            ),
          ),
        );
      },
    );
  }
}

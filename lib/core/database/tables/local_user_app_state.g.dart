// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_user_app_state.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocalUserAppStateAdapter extends TypeAdapter<LocalUserAppState> {
  @override
  final int typeId = 8;

  @override
  LocalUserAppState read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocalUserAppState(
      userId: fields[0] as String,
      currentDocumentFilter: fields[1] as DocumentFilter,
      documentSearchHistory: (fields[2] as List).cast<String>(),
      documentsPageViewType: fields[3] as ViewType,
      documentSearchViewType: fields[5] as ViewType,
      savedViewsViewType: fields[4] as ViewType,
    );
  }

  @override
  void write(BinaryWriter writer, LocalUserAppState obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.currentDocumentFilter)
      ..writeByte(2)
      ..write(obj.documentSearchHistory)
      ..writeByte(3)
      ..write(obj.documentsPageViewType)
      ..writeByte(4)
      ..write(obj.savedViewsViewType)
      ..writeByte(5)
      ..write(obj.documentSearchViewType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocalUserAppStateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'document_scanner_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DocumentScannerState {
  LoadingStatus get status => throw _privateConstructorUsedError;
  List<File> get scans => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DocumentScannerStateCopyWith<DocumentScannerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DocumentScannerStateCopyWith<$Res> {
  factory $DocumentScannerStateCopyWith(DocumentScannerState value,
          $Res Function(DocumentScannerState) then) =
      _$DocumentScannerStateCopyWithImpl<$Res, DocumentScannerState>;
  @useResult
  $Res call({LoadingStatus status, List<File> scans});
}

/// @nodoc
class _$DocumentScannerStateCopyWithImpl<$Res,
        $Val extends DocumentScannerState>
    implements $DocumentScannerStateCopyWith<$Res> {
  _$DocumentScannerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? scans = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as LoadingStatus,
      scans: null == scans
          ? _value.scans
          : scans // ignore: cast_nullable_to_non_nullable
              as List<File>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DocumentScannerStateImplCopyWith<$Res>
    implements $DocumentScannerStateCopyWith<$Res> {
  factory _$$DocumentScannerStateImplCopyWith(_$DocumentScannerStateImpl value,
          $Res Function(_$DocumentScannerStateImpl) then) =
      __$$DocumentScannerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({LoadingStatus status, List<File> scans});
}

/// @nodoc
class __$$DocumentScannerStateImplCopyWithImpl<$Res>
    extends _$DocumentScannerStateCopyWithImpl<$Res, _$DocumentScannerStateImpl>
    implements _$$DocumentScannerStateImplCopyWith<$Res> {
  __$$DocumentScannerStateImplCopyWithImpl(_$DocumentScannerStateImpl _value,
      $Res Function(_$DocumentScannerStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? scans = null,
  }) {
    return _then(_$DocumentScannerStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as LoadingStatus,
      scans: null == scans
          ? _value._scans
          : scans // ignore: cast_nullable_to_non_nullable
              as List<File>,
    ));
  }
}

/// @nodoc

class _$DocumentScannerStateImpl
    with DiagnosticableTreeMixin
    implements _DocumentScannerState {
  const _$DocumentScannerStateImpl(
      {this.status = LoadingStatus.initial, final List<File> scans = const []})
      : _scans = scans;

  @override
  @JsonKey()
  final LoadingStatus status;
  final List<File> _scans;
  @override
  @JsonKey()
  List<File> get scans {
    if (_scans is EqualUnmodifiableListView) return _scans;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_scans);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DocumentScannerState(status: $status, scans: $scans)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DocumentScannerState'))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('scans', scans));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DocumentScannerStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._scans, _scans));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, status, const DeepCollectionEquality().hash(_scans));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DocumentScannerStateImplCopyWith<_$DocumentScannerStateImpl>
      get copyWith =>
          __$$DocumentScannerStateImplCopyWithImpl<_$DocumentScannerStateImpl>(
              this, _$identity);
}

abstract class _DocumentScannerState implements DocumentScannerState {
  const factory _DocumentScannerState(
      {final LoadingStatus status,
      final List<File> scans}) = _$DocumentScannerStateImpl;

  @override
  LoadingStatus get status;
  @override
  List<File> get scans;
  @override
  @JsonKey(ignore: true)
  _$$DocumentScannerStateImplCopyWith<_$DocumentScannerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

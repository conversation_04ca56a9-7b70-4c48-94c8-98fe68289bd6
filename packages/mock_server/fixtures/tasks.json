[{"id": 141, "type": "file", "result": "sample 2.pdf: Not consuming sample 2.pdf: It is a duplicate. : Traceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ngx.nosync-udqDZzaE/lib/python3.8/site-packages/django_q/cluster.py\", line 432, in worker\n    res = f(*task[\"args\"], **task[\"kwargs\"])\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/tasks.py\", line 316, in consume_file\n    document = Consumer().try_consume_file(\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/consumer.py\", line 218, in try_consume_file\n    self.pre_check_duplicate()\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/consumer.py\", line 113, in pre_check_duplicate\n    self._fail(\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/consumer.py\", line 84, in _fail\n    raise ConsumerError(f\"{self.filename}: {log_message or message}\")\ndocuments.consumer.ConsumerError: sample 2.pdf: Not consuming sample 2.pdf: It is a duplicate.\n", "status": "FAILURE", "task_id": "d8ddbe298a42427d82553206ddf0bc94", "task_file_name": "sample 2.pdf", "date_created": "2022-05-26T23:17:38.333474-07:00", "date_done": null, "acknowledged": false, "related_document": null}, {"id": 132, "type": "file", "result": " : Traceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/subprocess.py\", line 131, in get_version\n    env=env,\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/subprocess.py\", line 68, in run\n    proc = subprocess_run(args, env=env, **kwargs)\n  File \"/Users/<USER>/opt/anaconda3/envs/paperless-ng/lib/python3.6/subprocess.py\", line 423, in run\n    with Pope<PERSON>(*popenargs, **kwargs) as process:\n  File \"/Users/<USER>/opt/anaconda3/envs/paperless-ng/lib/python3.6/subprocess.py\", line 729, in __init__\n    restore_signals, start_new_session)\n  File \"/Users/<USER>/opt/anaconda3/envs/paperless-ng/lib/python3.6/subprocess.py\", line 1364, in _execute_child\n    raise child_exception_type(errno_num, err_msg, err_filename)\nFileNotFoundError: [Errno 2] No such file or directory: 'unpaper': 'unpaper'\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/subprocess.py\", line 287, in check_external_program\n    found_version = version_checker()\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/_exec/unpaper.py\", line 34, in version\n    return get_version('unpaper')\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/subprocess.py\", line 137, in get_version\n    ) from e\nocrmypdf.exceptions.MissingDependencyError: Could not find program 'unpaper' on the PATH\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/paperless_tesseract/parsers.py\", line 176, in parse\n    ocrmypdf.ocr(**ocr_args)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/api.py\", line 315, in ocr\n    check_options(options, plugin_manager)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/_validation.py\", line 260, in check_options\n    _check_options(options, plugin_manager, ocr_engine_languages)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/_validation.py\", line 250, in _check_options\n    check_options_preprocessing(options)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/_validation.py\", line 128, in check_options_preprocessing\n    required_for=['--clean, --clean-final'],\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/ocrmypdf/subprocess.py\", line 293, in check_external_program\n    raise MissingDependencyError()\nocrmypdf.exceptions.MissingDependencyError\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/consumer.py\", line 179, in try_consume_file\n    document_parser.parse(self.path, mime_type, self.filename)\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/paperless_tesseract/parsers.py\", line 197, in parse\n    raise ParseError(e)\ndocuments.parsers.ParseError\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/django_q/cluster.py\", line 436, in worker\n    res = f(*task[\"args\"], **task[\"kwargs\"])\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/tasks.py\", line 73, in consume_file\n    override_tag_ids=override_tag_ids)\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/consumer.py\", line 196, in try_consume_file\n    raise ConsumerError(e)\ndocuments.consumer.ConsumerError\n", "status": "FAILURE", "task_id": "4c554075552c4cc985abd76e6f274c90", "task_file_name": "pdf-sample 10.24.48 PM.pdf", "date_created": "2022-05-26T14:26:07.846365-07:00", "date_done": null, "acknowledged": null}, {"id": 115, "type": "file", "result": "2021-01-24 2021-01-20 sample_wide_orange.pdf: Document is a duplicate : Traceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/django_q/cluster.py\", line 436, in worker\n    res = f(*task[\"args\"], **task[\"kwargs\"])\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/tasks.py\", line 75, in consume_file\n    task_id=task_id\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/consumer.py\", line 168, in try_consume_file\n    self.pre_check_duplicate()\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/consumer.py\", line 85, in pre_check_duplicate\n    self._fail(\"Document is a duplicate\")\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/consumer.py\", line 53, in _fail\n    raise ConsumerError(f\"{self.filename}: {message}\")\ndocuments.consumer.ConsumerError: 2021-01-24 2021-01-20 sample_wide_orange.pdf: Document is a duplicate\n", "status": "FAILURE", "task_id": "86494713646a4364b01da17aadca071d", "task_file_name": "2021-01-24 2021-01-20 sample_wide_orange.pdf", "date_created": "2022-05-26T14:26:07.817608-07:00", "date_done": null, "acknowledged": null}, {"id": 85, "type": "file", "result": "cannot open resource : Traceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/django_q/cluster.py\", line 436, in worker\n    res = f(*task[\"args\"], **task[\"kwargs\"])\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/tasks.py\", line 81, in consume_file\n    task_id=task_id\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/consumer.py\", line 244, in try_consume_file\n    self.path, mime_type, self.filename)\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/documents/parsers.py\", line 302, in get_optimised_thumbnail\n    thumbnail = self.get_thumbnail(document_path, mime_type, file_name)\n  File \"/Users/<USER>/Documents/Work/Contributions/paperless-ng/src/paperless_text/parsers.py\", line 29, in get_thumbnail\n    layout_engine=ImageFont.LAYOUT_BASIC)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/PIL/ImageFont.py\", line 852, in truetype\n    return freetype(font)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/PIL/ImageFont.py\", line 849, in freetype\n    return FreeTypeFont(font, size, index, encoding, layout_engine)\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ng/lib/python3.6/site-packages/PIL/ImageFont.py\", line 210, in __init__\n    font, size, index, encoding, layout_engine=layout_engine\nOSError: cannot open resource\n", "status": "FAILURE", "task_id": "abca803fa46342e1ac81f3d3f2080e79", "task_file_name": "simple.txt", "date_created": "2022-05-26T14:26:07.771541-07:00", "date_done": null, "acknowledged": null}, {"id": 41, "type": "file", "result": "commands.txt: Not consuming commands.txt: It is a duplicate. : Traceback (most recent call last):\n  File \"/Users/<USER>/.local/share/virtualenvs/paperless-ngx.nosync-udqDZzaE/lib/python3.8/site-packages/django_q/cluster.py\", line 432, in worker\n    res = f(*task[\"args\"], **task[\"kwargs\"])\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/tasks.py\", line 70, in consume_file\n    document = Consumer().try_consume_file(\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/consumer.py\", line 199, in try_consume_file\n    self.pre_check_duplicate()\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/consumer.py\", line 97, in pre_check_duplicate\n    self._fail(\n  File \"/Users/<USER>/Documents/paperless-ngx/src/documents/consumer.py\", line 69, in _fail\n    raise ConsumerError(f\"{self.filename}: {log_message or message}\")\ndocuments.consumer.ConsumerError: commands.txt: Not consuming commands.txt: It is a duplicate.\n", "status": "FAILURE", "task_id": "0af67672e8e14404b060d4cf8f69313d", "task_file_name": "commands.txt", "date_created": "2022-05-26T14:26:07.704247-07:00", "date_done": null, "acknowledged": null}, {"id": 10, "type": "file", "result": "Success. New document id 260 created", "status": "SUCCESS", "task_id": "b7629a0f41bd40c7a3ea4680341321b5", "task_file_name": "2022-03-24+Sonstige+ScanPC2022-03-24_081058.pdf", "date_created": "2022-05-26T14:26:07.670577-07:00", "date_done": "2022-05-26T14:26:07.670577-07:00", "acknowledged": false, "related_document": 260}, {"id": 9, "type": "file", "result": "Success. New document id 261 created", "status": "SUCCESS", "task_id": "02e276a86a424ccfb83309df5d8594be", "task_file_name": "2sample-pdf-with-images.pdf", "date_created": "2022-05-26T14:26:07.668987-07:00", "date_done": "2022-05-26T14:26:07.668987-07:00", "acknowledged": false, "related_document": 261}, {"id": 8, "type": "file", "result": "Success. New document id 262 created", "status": "SUCCESS", "task_id": "41229b8be9b445c0a523697d0f58f13e", "task_file_name": "2sample-pdf-with-images_pw.pdf", "date_created": "2022-05-26T14:26:07.667993-07:00", "date_done": "2022-05-26T14:26:07.667993-07:00", "acknowledged": false, "related_document": 262}, {"id": 6, "type": "file", "result": "Success. New document id 264 created", "status": "SUCCESS", "task_id": "bbbca32d408c4619bd0b512a8327c773", "task_file_name": "homebridge.log", "date_created": "2022-05-26T14:26:07.665560-07:00", "date_done": "2022-05-26T14:26:07.665560-07:00", "acknowledged": false, "related_document": 264}, {"id": 5, "type": "file", "result": "Success. New document id 265 created", "status": "SUCCESS", "task_id": "00ab285ab4bf482ab30c7d580b252ecb", "task_file_name": "IMG_7459.PNG", "date_created": "2022-05-26T14:26:07.664506-07:00", "date_done": "2022-05-26T14:26:07.664506-07:00", "acknowledged": false, "related_document": 265}, {"id": 3, "type": "file", "result": "Success. New document id 267 created", "status": "SUCCESS", "task_id": "289c5163cfec410db42948a0cacbeb9c", "task_file_name": "IMG_7459.PNG", "date_created": "2022-05-26T14:26:07.659661-07:00", "date_done": "2022-05-26T14:26:07.659661-07:00", "acknowledged": false, "related_document": 267}, {"id": 1, "type": "file", "result": null, "status": "STARTED", "task_id": "7a4ebdb2bde04311935284027ef8ca65", "task_file_name": "2019-08-04 DSA Questionnaire - 5-8-19.pdf", "date_created": "2022-05-26T14:26:07.655276-07:00", "date_done": null, "acknowledged": false, "related_document": null}]
class R {
  const R._();
  static const landing = "landing";
  static const login = "login";
  static const loginToExistingAccount = 'loginToExistingAccount';
  static const documents = "documents";
  static const verifyIdentity = "verifyIdentity";
  static const switchingAccount = "switchingAccount";
  static const savedView = "savedView";
  static const createSavedView = "createSavedView";
  static const editSavedView = "editSavedView";
  static const documentDetails = "documentDetails";
  static const editDocument = "editDocument";
  static const labels = "labels";
  static const createLabel = "createLabel";
  static const editLabel = "editLabel";
  static const scanner = "scanner";
  static const uploadDocument = "upload";
  static const inbox = "inbox";
  static const documentPreview = "documentPreview";
  static const settings = "settings";
  static const linkedDocuments = "linkedDocuments";
  static const bulkEditDocuments = "bulkEditDocuments";
  static const uploadQueue = "uploadQueue";
  static const authenticating = "authenticating";
  static const loggingOut = "loggingOut";
  static const restoringSession = "restoringSession";
  static const addAccount = 'addAccount';
  static const addNote = 'addNote';
}

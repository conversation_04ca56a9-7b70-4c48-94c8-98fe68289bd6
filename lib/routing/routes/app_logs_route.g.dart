// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_logs_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $appLogsRoute,
    ];

RouteBase get $appLogsRoute => GoRouteData.$route(
      path: '/app-logs',
      parentNavigatorKey: AppLogsRoute.$parentNavigatorKey,
      factory: $AppLogsRouteExtension._fromState,
    );

extension $AppLogsRouteExtension on AppLogsRoute {
  static AppLogsRoute _fromState(GoRouterState state) => AppLogsRoute();

  String get location => GoRouteData.$location(
        '/app-logs',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

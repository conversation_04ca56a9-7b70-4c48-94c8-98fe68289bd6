// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authenticated_route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $authenticatedRoute,
    ];

RouteBase get $authenticatedRoute => ShellRouteData.$route(
      navigatorKey: AuthenticatedRoute.$navigatorKey,
      factory: $AuthenticatedRouteExtension._fromState,
      routes: [
        GoRouteData.$route(
          path: '/settings',
          name: 'settings',
          parentNavigatorKey: SettingsRoute.$parentNavigatorKey,
          factory: $SettingsRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/upload-queue',
          name: 'uploadQueue',
          parentNavigatorKey: UploadQueueRoute.$parentNavigatorKey,
          factory: $UploadQueueRouteExtension._fromState,
        ),
        GoRouteData.$route(
          path: '/saved-views',
          factory: $SavedViewsRouteExtension._fromState,
          routes: [
            GoRouteData.$route(
              path: 'create',
              name: 'createSavedView',
              factory: $CreateSavedViewRouteExtension._fromState,
            ),
            GoRouteData.$route(
              path: 'edit',
              name: 'editSavedView',
              factory: $EditSavedViewRouteExtension._fromState,
            ),
          ],
        ),
        StatefulShellRouteData.$route(
          navigatorContainerBuilder:
              ScaffoldShellRoute.$navigatorContainerBuilder,
          factory: $ScaffoldShellRouteExtension._fromState,
          branches: [
            StatefulShellBranchData.$branch(
              navigatorKey: LandingBranch.$navigatorKey,
              routes: [
                GoRouteData.$route(
                  path: '/landing',
                  name: 'landing',
                  factory: $LandingRouteExtension._fromState,
                ),
              ],
            ),
            StatefulShellBranchData.$branch(
              navigatorKey: DocumentsBranch.$navigatorKey,
              routes: [
                GoRouteData.$route(
                  path: '/documents',
                  factory: $DocumentsRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'details/:id',
                      name: 'documentDetails',
                      parentNavigatorKey:
                          DocumentDetailsRoute.$parentNavigatorKey,
                      factory: $DocumentDetailsRouteExtension._fromState,
                    ),
                    GoRouteData.$route(
                      path: 'edit',
                      name: 'editDocument',
                      parentNavigatorKey: EditDocumentRoute.$parentNavigatorKey,
                      factory: $EditDocumentRouteExtension._fromState,
                    ),
                    GoRouteData.$route(
                      path: 'bulk-edit',
                      name: 'bulkEditDocuments',
                      factory: $BulkEditDocumentsRouteExtension._fromState,
                    ),
                    GoRouteData.$route(
                      path: 'preview',
                      name: 'documentPreview',
                      parentNavigatorKey:
                          DocumentPreviewRoute.$parentNavigatorKey,
                      factory: $DocumentPreviewRouteExtension._fromState,
                    ),
                  ],
                ),
              ],
            ),
            StatefulShellBranchData.$branch(
              navigatorKey: ScannerBranch.$navigatorKey,
              routes: [
                GoRouteData.$route(
                  path: '/scanner',
                  name: 'scanner',
                  factory: $ScannerRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'upload',
                      name: 'upload',
                      parentNavigatorKey:
                          DocumentUploadRoute.$parentNavigatorKey,
                      factory: $DocumentUploadRouteExtension._fromState,
                    ),
                  ],
                ),
              ],
            ),
            StatefulShellBranchData.$branch(
              navigatorKey: LabelsBranch.$navigatorKey,
              routes: [
                GoRouteData.$route(
                  path: '/labels',
                  name: 'labels',
                  factory: $LabelsRouteExtension._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'edit',
                      name: 'editLabel',
                      parentNavigatorKey: EditLabelRoute.$parentNavigatorKey,
                      factory: $EditLabelRouteExtension._fromState,
                    ),
                    GoRouteData.$route(
                      path: 'create',
                      name: 'createLabel',
                      parentNavigatorKey: CreateLabelRoute.$parentNavigatorKey,
                      factory: $CreateLabelRouteExtension._fromState,
                    ),
                    GoRouteData.$route(
                      path: 'linked-documents',
                      name: 'linkedDocuments',
                      parentNavigatorKey:
                          LinkedDocumentsRoute.$parentNavigatorKey,
                      factory: $LinkedDocumentsRouteExtension._fromState,
                    ),
                  ],
                ),
              ],
            ),
            StatefulShellBranchData.$branch(
              navigatorKey: InboxBranch.$navigatorKey,
              routes: [
                GoRouteData.$route(
                  path: '/inbox',
                  name: 'inbox',
                  factory: $InboxRouteExtension._fromState,
                ),
              ],
            ),
          ],
        ),
      ],
    );

extension $AuthenticatedRouteExtension on AuthenticatedRoute {
  static AuthenticatedRoute _fromState(GoRouterState state) =>
      const AuthenticatedRoute();
}

extension $SettingsRouteExtension on SettingsRoute {
  static SettingsRoute _fromState(GoRouterState state) => SettingsRoute();

  String get location => GoRouteData.$location(
        '/settings',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $UploadQueueRouteExtension on UploadQueueRoute {
  static UploadQueueRoute _fromState(GoRouterState state) => UploadQueueRoute();

  String get location => GoRouteData.$location(
        '/upload-queue',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $SavedViewsRouteExtension on SavedViewsRoute {
  static SavedViewsRoute _fromState(GoRouterState state) =>
      const SavedViewsRoute();

  String get location => GoRouteData.$location(
        '/saved-views',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $CreateSavedViewRouteExtension on CreateSavedViewRoute {
  static CreateSavedViewRoute _fromState(GoRouterState state) =>
      CreateSavedViewRoute(
        showOnDashboard: _$convertMapValue(
            'show-on-dashboard', state.uri.queryParameters, _$boolConverter),
        showInSidebar: _$convertMapValue(
            'show-in-sidebar', state.uri.queryParameters, _$boolConverter),
        $extra: state.extra as DocumentFilter?,
      );

  String get location => GoRouteData.$location(
        '/saved-views/create',
        queryParams: {
          if (showOnDashboard != null)
            'show-on-dashboard': showOnDashboard!.toString(),
          if (showInSidebar != null)
            'show-in-sidebar': showInSidebar!.toString(),
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $EditSavedViewRouteExtension on EditSavedViewRoute {
  static EditSavedViewRoute _fromState(GoRouterState state) =>
      EditSavedViewRoute(
        $extra: state.extra as SavedView,
      );

  String get location => GoRouteData.$location(
        '/saved-views/edit',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $ScaffoldShellRouteExtension on ScaffoldShellRoute {
  static ScaffoldShellRoute _fromState(GoRouterState state) =>
      const ScaffoldShellRoute();
}

extension $LandingRouteExtension on LandingRoute {
  static LandingRoute _fromState(GoRouterState state) => const LandingRoute();

  String get location => GoRouteData.$location(
        '/landing',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DocumentsRouteExtension on DocumentsRoute {
  static DocumentsRoute _fromState(GoRouterState state) => DocumentsRoute();

  String get location => GoRouteData.$location(
        '/documents',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DocumentDetailsRouteExtension on DocumentDetailsRoute {
  static DocumentDetailsRoute _fromState(GoRouterState state) =>
      DocumentDetailsRoute(
        id: int.parse(state.pathParameters['id']!),
        isLabelClickable: _$convertMapValue('is-label-clickable',
                state.uri.queryParameters, _$boolConverter) ??
            true,
        queryString: state.uri.queryParameters['query-string'],
        thumbnailUrl: state.uri.queryParameters['thumbnail-url'],
        title: state.uri.queryParameters['title'],
      );

  String get location => GoRouteData.$location(
        '/documents/details/${Uri.encodeComponent(id.toString())}',
        queryParams: {
          if (isLabelClickable != true)
            'is-label-clickable': isLabelClickable.toString(),
          if (queryString != null) 'query-string': queryString,
          if (thumbnailUrl != null) 'thumbnail-url': thumbnailUrl,
          if (title != null) 'title': title,
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditDocumentRouteExtension on EditDocumentRoute {
  static EditDocumentRoute _fromState(GoRouterState state) => EditDocumentRoute(
        state.extra as DocumentModel,
      );

  String get location => GoRouteData.$location(
        '/documents/edit',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $BulkEditDocumentsRouteExtension on BulkEditDocumentsRoute {
  static BulkEditDocumentsRoute _fromState(GoRouterState state) =>
      BulkEditDocumentsRoute(
        state.extra as BulkEditExtraWrapper,
      );

  String get location => GoRouteData.$location(
        '/documents/bulk-edit',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $DocumentPreviewRouteExtension on DocumentPreviewRoute {
  static DocumentPreviewRoute _fromState(GoRouterState state) =>
      DocumentPreviewRoute(
        id: int.parse(state.uri.queryParameters['id']!),
        title: state.uri.queryParameters['title'],
      );

  String get location => GoRouteData.$location(
        '/documents/preview',
        queryParams: {
          'id': id.toString(),
          if (title != null) 'title': title,
        },
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $ScannerRouteExtension on ScannerRoute {
  static ScannerRoute _fromState(GoRouterState state) => const ScannerRoute();

  String get location => GoRouteData.$location(
        '/scanner',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $DocumentUploadRouteExtension on DocumentUploadRoute {
  static DocumentUploadRoute _fromState(GoRouterState state) =>
      DocumentUploadRoute(
        title: state.uri.queryParameters['title'],
        filename: state.uri.queryParameters['filename'],
        fileExtension: state.uri.queryParameters['file-extension'],
        $extra: state.extra as FutureOr<Uint8List>,
      );

  String get location => GoRouteData.$location(
        '/scanner/upload',
        queryParams: {
          if (title != null) 'title': title,
          if (filename != null) 'filename': filename,
          if (fileExtension != null) 'file-extension': fileExtension,
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $LabelsRouteExtension on LabelsRoute {
  static LabelsRoute _fromState(GoRouterState state) => LabelsRoute();

  String get location => GoRouteData.$location(
        '/labels',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

extension $EditLabelRouteExtension on EditLabelRoute {
  static EditLabelRoute _fromState(GoRouterState state) => EditLabelRoute(
        state.extra as Label,
      );

  String get location => GoRouteData.$location(
        '/labels/edit',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $CreateLabelRouteExtension on CreateLabelRoute {
  static CreateLabelRoute _fromState(GoRouterState state) => CreateLabelRoute(
        name: state.uri.queryParameters['name'],
        state.extra as LabelType,
      );

  String get location => GoRouteData.$location(
        '/labels/create',
        queryParams: {
          if (name != null) 'name': name,
        },
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $LinkedDocumentsRouteExtension on LinkedDocumentsRoute {
  static LinkedDocumentsRoute _fromState(GoRouterState state) =>
      LinkedDocumentsRoute(
        state.extra as DocumentFilter,
      );

  String get location => GoRouteData.$location(
        '/labels/linked-documents',
      );

  void go(BuildContext context) => context.go(location, extra: $extra);

  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: $extra);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: $extra);

  void replace(BuildContext context) =>
      context.replace(location, extra: $extra);
}

extension $InboxRouteExtension on InboxRoute {
  static InboxRoute _fromState(GoRouterState state) => InboxRoute();

  String get location => GoRouteData.$location(
        '/inbox',
      );

  void go(BuildContext context) => context.go(location);

  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  void replace(BuildContext context) => context.replace(location);
}

T? _$convertMapValue<T>(
  String key,
  Map<String, String> map,
  T Function(String) converter,
) {
  final value = map[key];
  return value == null ? null : converter(value);
}

bool _$boolConverter(String value) {
  switch (value) {
    case 'true':
      return true;
    case 'false':
      return false;
    default:
      throw UnsupportedError('Cannot convert "$value" into a bool.');
  }
}
